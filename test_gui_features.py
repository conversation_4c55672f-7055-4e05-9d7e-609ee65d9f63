#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI功能测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from src.utils.data_manager import DataManager
from src.services.account_service import AccountService
from src.services.platform_service import PlatformService
from src.services.cookie_service import CookieService
from src.gui.cookie_input_dialog import CookieInputDialog
from src.gui.account_detail_dialog import AccountDetailDialog


def test_cookie_input_dialog():
    """测试Cookie输入对话框"""
    print("测试Cookie输入对话框...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 创建对话框
    dialog = CookieInputDialog("测试账号", "sessionid=test123; token=abc456")
    
    # 设置自动关闭
    QTimer.singleShot(1000, dialog.accept)
    
    # 显示对话框
    result = dialog.exec_()
    
    print(f"✓ Cookie输入对话框测试完成，结果: {result}")
    return True


def test_account_detail_dialog():
    """测试账号详情对话框"""
    print("测试账号详情对话框...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    detail_info = """
账号详情

账号ID: test-123
账号名称: 测试账号
关联平台: 小红书千帆
创建时间: 2025-07-09 17:00:00

Cookie信息:
Cookie更新时间: 2025-07-09 17:00:00
Cookie过期时间: 未设置
Cookie状态: 有效
    """
    
    # 创建对话框
    dialog = AccountDetailDialog("测试账号", detail_info)
    
    # 设置自动关闭
    QTimer.singleShot(1000, dialog.accept)
    
    # 显示对话框
    result = dialog.exec_()
    
    print(f"✓ 账号详情对话框测试完成，结果: {result}")
    return True


def test_cookie_service():
    """测试Cookie服务"""
    print("测试Cookie服务...")
    
    data_manager = DataManager()
    data_manager.init_data_files()
    
    cookie_service = CookieService(data_manager)
    
    # 测试保存和获取Cookie
    test_account_id = "test-account-123"
    test_cookie = "sessionid=test123; token=abc456; userid=789"
    
    # 保存Cookie
    cookie_service.save_account_cookie(test_account_id, test_cookie)
    
    # 获取Cookie
    retrieved_cookie = cookie_service.get_account_cookie(test_account_id)
    
    assert retrieved_cookie == test_cookie, f"Cookie不匹配: {retrieved_cookie} != {test_cookie}"
    
    # 删除Cookie
    cookie_service.delete_account_cookie(test_account_id)
    
    # 验证删除
    deleted_cookie = cookie_service.get_account_cookie(test_account_id)
    assert deleted_cookie is None, f"Cookie未删除: {deleted_cookie}"
    
    print("✓ Cookie服务测试通过")
    return True


def test_account_with_cookie():
    """测试账号与Cookie的集成"""
    print("测试账号与Cookie集成...")
    
    data_manager = DataManager()
    data_manager.init_data_files()
    
    account_service = AccountService(data_manager)
    platform_service = PlatformService(data_manager)
    cookie_service = CookieService(data_manager)
    
    # 获取小红书千帆平台
    platforms = platform_service.get_all_platforms()
    xiaohongshu_platform = None
    for platform in platforms:
        if "小红书千帆" in platform.platform_name:
            xiaohongshu_platform = platform
            break
    
    assert xiaohongshu_platform is not None, "未找到小红书千帆平台"
    
    # 添加测试账号
    account_id = account_service.add_account("GUI测试账号", xiaohongshu_platform.platform_id)
    
    # 检查初始登录状态
    assert not account_service.is_account_logged_in(account_id), "新账号不应该显示已登录"
    
    # 设置Cookie
    test_cookie = "sessionid=gui_test123; token=gui_abc456"
    cookie_service.save_account_cookie(account_id, test_cookie)
    
    # 检查登录状态
    assert account_service.is_account_logged_in(account_id), "设置Cookie后应该显示已登录"
    
    # 获取账号详情
    account_platform = account_service.get_account_with_platform(account_id)
    assert account_platform is not None, "应该能获取账号和平台信息"
    
    account, platform = account_platform
    assert account.account_name == "GUI测试账号", "账号名称不匹配"
    assert platform.platform_name == "小红书千帆", "平台名称不匹配"
    
    # 清理测试数据
    account_service.delete_account(account_id)
    
    print("✓ 账号与Cookie集成测试通过")
    return True


def main():
    """主测试函数"""
    print("开始GUI功能测试...\n")
    
    try:
        # 测试后端服务
        test_cookie_service()
        test_account_with_cookie()
        
        # 测试GUI组件（需要显示环境）
        if os.environ.get('DISPLAY') or sys.platform == 'darwin' or sys.platform == 'win32':
            test_cookie_input_dialog()
            test_account_detail_dialog()
        else:
            print("⚠️  跳过GUI组件测试（无显示环境）")
        
        print("\n🎉 所有GUI功能测试通过！")
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试出错: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
