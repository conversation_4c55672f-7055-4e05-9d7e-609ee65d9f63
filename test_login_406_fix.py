#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录406错误修复
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService
from src.services.shop_name_service import ShopNameService


def test_shop_name_api_fix():
    """测试店铺名称API修复"""
    print("=" * 60)
    print("登录406错误修复测试")
    print("=" * 60)
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    shop_name_service = ShopNameService()
    
    # 获取小红书千帆账号
    accounts = account_service.get_all_accounts()
    qianfan_account = None
    
    for account in accounts:
        account_platform = account_service.get_account_with_platform(account.account_id)
        if account_platform:
            account_obj, platform = account_platform
            if platform.platform_name == "小红书千帆":
                qianfan_account = account_obj
                qianfan_platform = platform
                break
    
    if not qianfan_account:
        print("❌ 没有找到小红书千帆账号")
        return
    
    print(f"✅ 测试账号: {qianfan_account.account_name}")
    print(f"✅ 平台: {qianfan_platform.platform_name}")
    
    # 检查是否有Cookie
    cookie_str = data_manager.get_cookie(qianfan_account.account_id)
    if not cookie_str:
        print("❌ 账号没有Cookie，请先登录")
        print("正在启动浏览器进行登录...")
        
        try:
            # 启动浏览器登录
            cookie_service.open_incognito_browser(
                qianfan_platform.login_url,
                qianfan_account.account_id,
                callback=None
            )
            
            print("请在浏览器中完成登录，然后点击'我已完成登录'按钮")
            
            # 等待登录完成
            max_wait = 300  # 5分钟超时
            wait_time = 0
            
            while wait_time < max_wait:
                time.sleep(5)
                wait_time += 5
                
                # 检查是否获取到Cookie
                cookie_str = data_manager.get_cookie(qianfan_account.account_id)
                if cookie_str:
                    print("✅ 登录成功，获取到Cookie")
                    break
                
                print(f"等待登录中... ({wait_time}/{max_wait}秒)")
            
            if not cookie_str:
                print("❌ 登录超时，请重试")
                return
                
        except Exception as e:
            print(f"❌ 登录过程出错: {e}")
            return
        finally:
            try:
                cookie_service.force_close_browser()
            except:
                pass
    
    print(f"✅ 使用Cookie: {cookie_str[:50]}...")
    
    # 测试店铺名称API修复
    print("\n" + "=" * 40)
    print("测试店铺名称API修复")
    print("=" * 40)
    
    # 获取平台配置
    platforms = data_manager.get_platforms()
    platform_data = platforms.get("xiaohongshu_qianfan", {})
    
    shop_name_api_url = platform_data.get("shop_name_api_url")
    shop_name_field = platform_data.get("shop_name_field")
    
    print(f"店铺名称接口: {shop_name_api_url}")
    print(f"字段路径: {shop_name_field}")
    
    if not shop_name_api_url or not shop_name_field:
        print("❌ 平台未配置店铺名称接口")
        return
    
    # 测试修复后的店铺名称获取
    print("\n开始测试店铺名称获取...")
    
    try:
        shop_name = shop_name_service.get_shop_name(
            shop_name_api_url, shop_name_field, cookie_str
        )
        
        if shop_name:
            print(f"✅ 成功获取店铺名称: {shop_name}")
        else:
            print("⚠️ 未能获取店铺名称，但修复后不会影响登录流程")
            
    except Exception as e:
        print(f"❌ 店铺名称获取出错: {e}")
    
    # 测试完整的登录流程
    print("\n" + "=" * 40)
    print("测试完整登录流程")
    print("=" * 40)
    
    try:
        # 清除现有Cookie，重新测试登录
        print("清除现有Cookie，重新测试登录流程...")
        data_manager.clear_cookie(qianfan_account.account_id)
        
        print("启动浏览器进行完整登录测试...")
        cookie_service.open_incognito_browser(
            qianfan_platform.login_url,
            qianfan_account.account_id,
            callback=None
        )
        
        print("请在浏览器中完成登录，然后点击'我已完成登录'按钮")
        print("系统将测试修复后的登录流程...")
        
        # 等待登录完成
        max_wait = 300  # 5分钟超时
        wait_time = 0
        
        while wait_time < max_wait:
            time.sleep(5)
            wait_time += 5
            
            # 检查是否获取到Cookie
            new_cookie_str = data_manager.get_cookie(qianfan_account.account_id)
            if new_cookie_str:
                print("✅ 登录流程完成，获取到Cookie")
                
                # 检查账号信息是否包含店铺名称
                accounts = data_manager.get_accounts()
                account_data = accounts.get(qianfan_account.account_id, {})
                shop_name = account_data.get("shop_name")
                
                if shop_name:
                    print(f"✅ 店铺名称已保存: {shop_name}")
                else:
                    print("⚠️ 店铺名称未保存，但登录流程正常")
                
                break
            
            print(f"等待登录中... ({wait_time}/{max_wait}秒)")
        
        if wait_time >= max_wait:
            print("❌ 登录超时")
        
    except Exception as e:
        print(f"❌ 完整登录流程测试出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            cookie_service.force_close_browser()
            print("✅ 浏览器已关闭")
        except:
            pass
    
    print("\n" + "=" * 60)
    print("修复总结")
    print("=" * 60)
    print("问题: 点击登录接口返回406 Not Acceptable")
    print("\n修复内容:")
    print("✅ 增强了HTTP请求头，包含更完整的浏览器标识")
    print("✅ 添加了POST请求尝试，应对不同接口要求")
    print("✅ 增加了备用接口地址尝试")
    print("✅ 添加了备用字段名尝试")
    print("✅ 店铺名称获取失败时不影响登录流程")
    print("✅ 自动设置默认店铺名称作为后备方案")
    
    print("\n预期效果:")
    print("✅ 登录流程不再因406错误中断")
    print("✅ 即使店铺名称获取失败，登录仍然成功")
    print("✅ 提供详细的错误信息便于调试")
    print("✅ 自动尝试多种解决方案")


def test_api_requests():
    """测试API请求修复"""
    print("\n" + "=" * 40)
    print("API请求修复测试")
    print("=" * 40)
    
    import requests
    
    # 测试不同的请求方式
    test_urls = [
        "https://ark.xiaohongshu.com/api/edith/seller/detail",
        "https://ark.xiaohongshu.com/api/edith/user/info",
        "https://ark.xiaohongshu.com/api/user/profile",
        "https://ark.xiaohongshu.com/api/seller/info"
    ]
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    for url in test_urls:
        print(f"\n测试接口: {url}")
        
        # 测试GET请求
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"  GET状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"  响应内容: {response.text[:200]}...")
        except Exception as e:
            print(f"  GET请求失败: {e}")
        
        # 测试POST请求
        try:
            response = requests.post(url, headers=headers, json={}, timeout=10)
            print(f"  POST状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"  响应内容: {response.text[:200]}...")
        except Exception as e:
            print(f"  POST请求失败: {e}")


def main():
    """主函数"""
    print("登录406错误修复测试工具")
    print("修复内容: 解决点击登录接口返回406 Not Acceptable的问题")
    
    try:
        # 测试API请求
        test_api_requests()
        
        # 测试店铺名称API修复
        test_shop_name_api_fix()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
