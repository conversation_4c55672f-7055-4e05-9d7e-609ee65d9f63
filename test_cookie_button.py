#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cookie获取按钮功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def test_cookie_button():
    """测试Cookie获取按钮功能"""
    print("=== 测试Cookie获取按钮功能 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("没有可用的账号进行测试")
        return
    
    account = accounts[0]
    print(f"使用账号: {account.account_name}")
    
    # 获取平台信息
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("账号没有关联的平台")
        return
    
    account, platform = account_platform
    print(f"平台: {platform.platform_name}")
    print(f"登录URL: {platform.login_url}")
    
    # 定义回调函数
    def login_callback(success, message):
        print(f"登录回调: {'成功' if success else '失败'} - {message}")
    
    try:
        print("\n开始测试Cookie获取...")
        print("将打开无痕浏览器，请在浏览器中:")
        print("1. 完成登录操作")
        print("2. 点击右上角的'我已完成登录'按钮")
        print("3. 观察按钮状态变化")
        print("4. 测试页面跳转后按钮是否仍然显示")
        
        # 启动Cookie获取
        cookie_service.start_incognito_login(
            account.account_id, 
            platform.login_url, 
            login_callback
        )
        
        # 等待用户操作
        print("\n等待用户操作...")
        start_time = time.time()
        timeout = 300  # 5分钟超时
        
        while time.time() - start_time < timeout:
            # 检查登录结果
            result = cookie_service.get_login_result(account.account_id)
            if result is not None:
                success, message = result
                print(f"\n登录结果: {'成功' if success else '失败'} - {message}")
                
                if success:
                    # 检查Cookie是否保存
                    cookie_str = data_manager.get_cookie(account.account_id)
                    if cookie_str:
                        print(f"✅ Cookie已保存: {len(cookie_str)} 字符")
                        
                        # 检查店铺名称是否获取
                        updated_account = account_service.get_account_by_id(account.account_id)
                        if updated_account and updated_account.shop_name:
                            print(f"✅ 店铺名称已获取: {updated_account.shop_name}")
                        else:
                            print("⚠️ 店铺名称未获取")
                    else:
                        print("❌ Cookie未保存")
                else:
                    print(f"❌ 登录失败: {message}")
                
                break
            
            time.sleep(1)
        else:
            print("⏰ 测试超时")
        
        # 清理
        cookie_service.force_close_browser()
        print("\n测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_button_persistence():
    """测试按钮在页面跳转后的持久性"""
    print("\n=== 测试按钮持久性 ===")
    print("请在浏览器中:")
    print("1. 观察右上角的'我已完成登录'按钮")
    print("2. 在网站内进行页面跳转")
    print("3. 确认按钮在跳转后仍然显示")
    print("4. 测试按钮的交互功能")


def main():
    """主函数"""
    print("Cookie获取按钮功能测试")
    print("=" * 50)
    
    try:
        test_cookie_button()
        test_button_persistence()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
