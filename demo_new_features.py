#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新功能演示脚本
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.status_manager import StatusManager
from src.utils.data_manager import DataManager
from src.services.account_service import AccountService
from src.services.platform_service import PlatformService


def demo_status_manager():
    """演示状态管理器功能"""
    print("=" * 50)
    print("状态管理器功能演示")
    print("=" * 50)
    
    status_manager = StatusManager()
    
    print(f"初始状态: {status_manager.get_agent_status()}")
    print(f"运行时间: {status_manager.get_runtime()}")
    print(f"抓取账号数: {status_manager.get_scraping_count()}")
    print(f"登录账号数: {status_manager.get_login_count()}")
    
    print("\n模拟开始登录...")
    status_manager.start_login("demo_account_1")
    print(f"Agent状态: {status_manager.get_agent_status_detail()}")
    
    time.sleep(2)
    
    print("\n模拟开始抓取...")
    status_manager.start_scraping("demo_account_2")
    print(f"Agent状态: {status_manager.get_agent_status_detail()}")
    
    time.sleep(2)
    
    print("\n模拟登录完成...")
    status_manager.stop_login("demo_account_1")
    print(f"Agent状态: {status_manager.get_agent_status_detail()}")
    
    time.sleep(2)
    
    print("\n模拟抓取完成...")
    status_manager.stop_scraping("demo_account_2")
    print(f"Agent状态: {status_manager.get_agent_status_detail()}")
    
    print("\n状态管理器演示完成！")


def demo_account_management():
    """演示账号管理功能"""
    print("\n" + "=" * 50)
    print("账号管理功能演示")
    print("=" * 50)
    
    # 初始化服务
    data_manager = DataManager()
    data_manager.init_data_files()
    
    account_service = AccountService(data_manager)
    platform_service = PlatformService(data_manager)
    
    # 显示预配置的平台
    platforms = platform_service.get_all_platforms()
    print(f"预配置平台数量: {len(platforms)}")
    
    for platform in platforms:
        print(f"- {platform.platform_name}: {platform.login_url}")
    
    # 添加演示账号
    if platforms:
        platform = platforms[0]  # 使用第一个平台（小红书千帆）
        
        print(f"\n添加演示账号到平台: {platform.platform_name}")
        account_id = account_service.add_account("演示账号", platform.platform_id)
        print(f"账号ID: {account_id}")
        
        # 获取账号详情
        account_with_platform = account_service.get_account_with_platform(account_id)
        if account_with_platform:
            account, platform_info = account_with_platform
            print(f"账号名称: {account.account_name}")
            print(f"关联平台: {platform_info.platform_name}")
            print(f"创建时间: {account.create_time}")
            print(f"登录状态: {'已登录' if account_service.is_account_logged_in(account_id) else '未登录'}")
        
        # 清理演示数据
        print(f"\n清理演示账号...")
        account_service.delete_account(account_id)
        print("演示账号已删除")
    
    print("\n账号管理演示完成！")


def demo_selenium_features():
    """演示selenium功能"""
    print("\n" + "=" * 50)
    print("Selenium功能演示")
    print("=" * 50)
    
    try:
        from src.services.cookie_service import SELENIUM_AVAILABLE
        
        print(f"Selenium可用性: {'✅ 可用' if SELENIUM_AVAILABLE else '❌ 不可用'}")
        
        if SELENIUM_AVAILABLE:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from webdriver_manager.chrome import ChromeDriverManager
            
            print("✅ Selenium导入成功")
            
            # 测试ChromeDriver
            try:
                driver_path = ChromeDriverManager().install()
                print(f"✅ ChromeDriver路径: {driver_path}")
                
                # 测试无痕模式配置
                chrome_options = Options()
                chrome_options.add_argument("--incognito")
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                print("✅ 无痕模式配置完成")
                print("注意: 实际使用时会打开无痕浏览器窗口")
                
            except Exception as e:
                print(f"⚠️  ChromeDriver测试失败: {e}")
        
        else:
            print("❌ Selenium不可用，请安装selenium和webdriver-manager")
            print("安装命令: pip install selenium webdriver-manager")
    
    except Exception as e:
        print(f"❌ Selenium功能测试失败: {e}")
    
    print("\nSelenium功能演示完成！")


def demo_new_gui_features():
    """演示新GUI功能"""
    print("\n" + "=" * 50)
    print("新GUI功能说明")
    print("=" * 50)
    
    print("🎨 界面更新:")
    print("- 动态状态显示：Agent状态实时更新")
    print("- 运行时间统计：显示应用运行时长")
    print("- 采集状态监控：实时显示正在采集的账号数量")
    print("- 登录状态监控：实时显示正在登录的账号数量")
    
    print("\n🔧 功能增强:")
    print("- 无痕浏览器登录：保护用户隐私")
    print("- 智能Cookie检测：自动识别登录成功")
    print("- 状态同步更新：登录和抓取状态实时同步")
    print("- 错误处理优化：更好的错误提示和恢复")
    
    print("\n🚀 使用流程:")
    print("1. 点击'浏览器登录'按钮")
    print("2. 系统打开无痕浏览器窗口")
    print("3. 在浏览器中完成登录操作")
    print("4. 系统自动检测登录状态并获取Cookie")
    print("5. 登录成功后自动更新账号状态")
    print("6. 可以开始数据抓取操作")
    
    print("\n新GUI功能说明完成！")


def main():
    """主演示函数"""
    print("🎉 运营数据采集器 - 新功能演示")
    print("版本: v1.2.0")
    print("更新时间: 2025-07-09")
    
    try:
        demo_status_manager()
        demo_account_management()
        demo_selenium_features()
        demo_new_gui_features()
        
        print("\n" + "=" * 50)
        print("🎊 所有新功能演示完成！")
        print("=" * 50)
        print("\n要体验完整功能，请运行: python main.py")
        
    except Exception as e:
        print(f"\n💥 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
