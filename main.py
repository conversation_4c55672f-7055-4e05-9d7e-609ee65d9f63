#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多平台运营数据抓取系统
主程序入口
"""

import sys
import os
import signal
import atexit
import threading
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.gui.main_window import MainWindow
from src.utils.data_manager import DataManager


def cleanup_resources():
    """清理资源"""
    try:
        # 清理可能残留的浏览器进程
        from src.services.cookie_service import CookieService

        data_manager = DataManager()
        cookie_service = CookieService(data_manager)
        cookie_service.cleanup_chrome_processes()

        print("资源清理完成")
    except Exception as e:
        print(f"清理资源时出错: {e}")


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"接收到信号 {signum}，正在清理资源...")
    cleanup_resources()
    sys.exit(0)


def main():
    """主函数"""
    # 确保在主线程中运行
    if threading.current_thread() != threading.main_thread():
        print("错误: 应用程序必须在主线程中启动")
        sys.exit(1)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 注册退出时的清理函数
    atexit.register(cleanup_resources)

    # 设置高DPI支持（在创建QApplication之前）
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("运营数据采集器")
    app.setApplicationVersion("1.2.1")

    # 设置应用程序样式
    app.setStyle('Fusion')

    # 确保Qt在主线程中运行
    app.moveToThread(app.thread())

    # 初始化数据管理器
    data_manager = DataManager()
    data_manager.init_data_files()

    # 创建主窗口（确保在主线程中）
    main_window = MainWindow()

    # 设置窗口关闭时的清理
    def on_window_close(event):
        cleanup_resources()
        event.accept()

    main_window.closeEvent = on_window_close

    main_window.show()

    # 使用定时器处理信号（避免NSInternalInconsistencyException）
    timer = QTimer()
    timer.timeout.connect(lambda: None)
    timer.start(100)

    # 运行应用程序
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        cleanup_resources()
        sys.exit(0)


if __name__ == '__main__':
    main()
