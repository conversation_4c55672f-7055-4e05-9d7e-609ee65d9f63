#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试防重定向功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def test_wrapper_page_creation():
    """测试包装页面创建"""
    print("=== 测试包装页面创建 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    
    # 测试参数
    account_id = "test-account-123"
    login_url = "https://www.baidu.com"  # 使用百度作为测试页面
    
    # 创建包装页面
    wrapper_html = cookie_service._create_login_wrapper_page(account_id, login_url)
    
    print(f"✅ 包装页面HTML长度: {len(wrapper_html)} 字符")
    
    # 检查防重定向机制
    checks = [
        ("防重定向函数", "preventRedirect" in wrapper_html),
        ("beforeunload监听", "beforeunload" in wrapper_html),
        ("location保护", "Object.defineProperty(window, 'location'" in wrapper_html),
        ("iframe沙箱", "sandbox=" in wrapper_html),
        ("用户关闭标记", "userClosing" in wrapper_html)
    ]
    
    for check_name, result in checks:
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {'通过' if result else '失败'}")
    
    # 保存测试文件
    test_file = "test_no_redirect.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(wrapper_html)
    
    print(f"✅ 测试文件已保存: {test_file}")
    print("可以在浏览器中打开测试防重定向功能")
    
    return test_file


def test_manual_browser():
    """手动测试浏览器功能"""
    print("\n=== 手动测试浏览器功能 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号进行测试")
        return
    
    account = accounts[0]
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return
    
    account, platform = account_platform
    
    print(f"✅ 使用账号: {account.account_name}")
    print(f"✅ 平台: {platform.platform_name}")
    print(f"✅ 登录URL: {platform.login_url}")
    
    try:
        print("\n正在启动浏览器...")
        print("请测试以下功能:")
        print("1. 页面是否保持在包装页面（不会重定向到目标网站）")
        print("2. 顶部是否有蓝色按钮区域")
        print("3. 下方iframe是否正确显示目标网站")
        print("4. 点击按钮是否弹出确认对话框")
        print("5. 在iframe中操作是否不影响父页面")
        
        # 启动浏览器
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        print("\n✅ 浏览器已启动")
        print("请在浏览器中测试功能...")
        
        # 等待用户测试
        input("按回车键结束测试...")
        
        # 清理
        cookie_service.force_close_browser()
        print("✅ 浏览器已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def create_test_html():
    """创建独立的测试HTML文件"""
    print("\n=== 创建独立测试文件 ===")
    
    test_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防重定向测试页面</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; height: 100vh; overflow: hidden; }
        .header-container { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 9999; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; }
        .header-title { color: white; font-size: 16px; font-weight: 600; }
        .login-button { background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.3s ease; }
        .login-button:hover { background: #45a017; }
        .login-button:disabled { background: #d9d9d9; cursor: not-allowed; }
        .iframe-container { position: absolute; top: 60px; left: 0; right: 0; bottom: 0; background: white; }
        .login-iframe { width: 100%; height: 100%; border: none; background: white; }
        .test-info { position: fixed; top: 70px; left: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 300px; z-index: 9998; }
    </style>
</head>
<body>
    <div class="header-container">
        <div class="header-title">防重定向测试页面</div>
        <button class="login-button" onclick="testRedirect()">测试重定向</button>
    </div>
    
    <div class="test-info">
        <h3>测试说明</h3>
        <p>1. 点击"测试重定向"按钮</p>
        <p>2. 页面应该保持在当前页面</p>
        <p>3. 不应该跳转到百度</p>
        <p>4. 查看控制台日志</p>
    </div>
    
    <div class="iframe-container">
        <iframe class="login-iframe" src="https://www.baidu.com" sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"></iframe>
    </div>

    <script>
        window.userClosing = false;
        
        // 防止页面被重定向
        function preventRedirect() {
            console.log('初始化防重定向机制');
            
            // 防止整个页面被重定向
            window.addEventListener('beforeunload', function(e) {
                if (!window.userClosing) {
                    console.log('阻止页面跳转');
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }
            });
            
            // 防止location被修改
            const originalLocation = window.location;
            Object.defineProperty(window, 'location', {
                get: function() {
                    return originalLocation;
                },
                set: function(value) {
                    console.log('阻止location重定向:', value);
                    alert('阻止了重定向到: ' + value);
                }
            });
            
            console.log('防重定向机制已启用');
        }
        
        function testRedirect() {
            console.log('测试重定向功能');
            
            // 尝试各种重定向方式
            try {
                console.log('尝试 window.location.href 重定向');
                window.location.href = 'https://www.baidu.com';
            } catch (e) {
                console.log('location.href 重定向被阻止:', e);
            }
            
            try {
                console.log('尝试 window.location 重定向');
                window.location = 'https://www.baidu.com';
            } catch (e) {
                console.log('location 重定向被阻止:', e);
            }
            
            alert('重定向测试完成，检查控制台日志');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            preventRedirect();
            console.log('页面加载完成，防重定向机制已启用');
        });
    </script>
</body>
</html>
    """
    
    test_file = "redirect_test.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print(f"✅ 独立测试文件已创建: {test_file}")
    print("可以在浏览器中打开此文件测试防重定向功能")
    
    return test_file


def main():
    """主函数"""
    print("防重定向功能测试工具")
    print("=" * 50)
    
    try:
        # 测试包装页面创建
        test_wrapper_page_creation()
        
        # 创建独立测试文件
        create_test_html()
        
        # 询问是否进行手动测试
        choice = input("\n是否进行手动浏览器测试？(y/n): ").lower().strip()
        if choice == 'y':
            test_manual_browser()
        
        print("\n✅ 所有测试完成")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
