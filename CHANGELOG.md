# 更新日志

## v1.3.2 (2025-07-10) - 导出选项功能

### 🎯 新增功能

#### 1. 导出选项选择
- **导出模式选择**: 用户可以选择导出全部数据或精确字段数据
- **可视化选择界面**: 提供直观的导出选项对话框
- **智能检测**: 自动检测平台是否配置了字段映射
- **预览功能**: 实时预览导出选项和效果

#### 2. 多种导出模式
- **全部数据导出**: 导出API返回的所有字段和数据
- **精确字段导出**: 只导出平台配置中指定的字段
- **附加选项**: 可选择包含原始数据工作表和统计信息
- **文件名标识**: 自动在文件名中添加导出模式标识

#### 3. 增强的Excel导出
- **多工作表支持**: 支持精确字段数据、原始数据、统计信息等多个工作表
- **数据类型优化**: 根据字段映射配置进行数据类型转换
- **统计信息**: 自动生成导出时间、数据条数等统计信息
- **兼容性保证**: 保持与现有导出功能的完全兼容

### 🛠️ 技术实现

#### 核心组件
- **ExportOptionsDialog**: 导出选项选择对话框
- **_export_data_by_mode()**: 根据导出模式处理数据
- **_export_field_mapping_data()**: 精确字段数据导出
- **_add_stats_sheet()**: 统计信息工作表生成

#### 导出流程优化
- **智能数据检测**: 自动识别数据结构类型
- **模式化处理**: 根据用户选择的模式处理数据
- **文件名优化**: 添加模式后缀区分不同导出类型
- **错误处理**: 完善的异常处理和用户提示

### 📊 功能对比

#### 全部数据导出
- 包含API返回的所有字段
- 适合需要完整数据分析的场景
- 文件较大，包含所有原始信息

#### 精确字段导出
- 只包含配置的特定字段
- 数据经过字段映射和类型转换
- 文件更小，数据更精确
- 可选择包含原始数据作为参考

---

## v1.3.1 (2025-07-10) - 字段配置保存修复

### 🔧 修复内容

#### 1. 字段映射保存功能修复
- **修复参数不匹配**: 修复了`update_platform`方法参数不匹配问题
- **添加保存方法**: 新增`save_platforms`方法到DataManager
- **单独保存字段映射**: 实现了字段映射配置的单独保存功能
- **错误处理**: 添加了保存失败时的错误处理和提示

#### 2. 数据管理功能增强
- **完整序列化**: 确保Platform对象的字段映射配置正确序列化
- **数据持久化**: 改进平台配置的持久化存储机制
- **配置验证**: 添加了配置保存后的验证机制
- **兼容性保证**: 保持与现有数据格式的兼容性

### 🛠️ 技术实现

#### 核心修复
- **save_platforms()**: 新增方法，保存平台配置数据
- **save_field_mappings()**: 新增方法，单独保存字段映射配置
- **configure_fields()**: 修复方法，正确处理字段映射配置保存

---

## v1.3.0 (2025-07-10) - 精确字段提取功能

### 🎯 重大功能更新

#### 1. 精确字段提取系统
- **字段映射配置**: 支持配置需要提取的特定字段，而不是提取所有数据
- **JSONPath精确提取**: 使用JSONPath路径精确定位和提取指定字段
- **数据类型转换**: 支持文本、数字、日期等数据类型的自动转换
- **默认值设置**: 当字段不存在时使用预设的默认值

#### 2. 字段配置界面
- **可视化配置**: 提供直观的字段映射配置对话框
- **实时预览**: 配置过程中实时预览字段映射效果
- **配置验证**: 自动验证字段名称唯一性和JSONPath格式
- **配置说明**: 内置详细的配置说明和示例

#### 3. 数据提取优化
- **减少数据传输**: 只提取需要的字段，大幅减少数据传输量
- **提高处理效率**: 避免处理不需要的数据，提高系统性能
- **灵活字段选择**: 支持嵌套字段、数组字段等复杂数据结构
- **向后兼容**: 保持与原有提取方式的完全兼容

### 🛠️ 技术实现

#### 核心组件
- **FieldMappingDialog**: 字段映射配置对话框
- **extract_data_with_field_mappings()**: 字段映射提取方法
- **determine_record_count()**: 智能记录数量检测
- **convert_data_type()**: 数据类型转换器

#### 平台模型扩展
- **field_mappings**: 新增字段映射配置属性
- **Platform.to_dict()**: 支持字段映射序列化
- **Platform.from_dict()**: 支持字段映射反序列化

### 📊 功能对比

#### 传统提取方式
```json
// 提取所有字段
{
  "title": "笔记标题",
  "view_count": 1000,
  "like_count": 50,
  "extra_field": "不需要的数据",
  "nested": {"deep": {"value": "深层数据"}}
}
```

#### 字段映射提取
```json
// 只提取配置的字段
{
  "笔记标题": "笔记标题",
  "浏览量": 1000,
  "点赞数": 50
}
```

### 📋 使用说明

1. **配置字段映射**
   - 在"平台配置"页面点击"字段配置"按钮
   - 添加需要提取的字段和对应的JSONPath路径
   - 设置字段的数据类型和默认值

2. **JSONPath示例**
   - `$.data.title` - 提取data对象中的title字段
   - `$.data.list[*].name` - 提取list数组中所有元素的name字段
   - `$.data.stats.total` - 提取嵌套对象中的total字段

3. **数据类型支持**
   - **文本**: 字符串类型数据
   - **数字**: 自动转换为数值类型
   - **日期**: 自动格式化为标准日期格式

---

## v1.2.2 (2025-07-10) - NSInternalInconsistencyException彻底修复

### 🔧 重要修复

#### 1. NSInternalInconsistencyException彻底解决
- **移除线程间UI操作**: 完全移除了在非主线程中操作UI组件的代码
- **轮询机制**: 使用轮询方式替代线程间回调，避免跨线程UI操作
- **回调结果存储**: 使用结果存储机制代替直接UI回调
- **主线程安全**: 确保所有UI操作都在主线程中执行

#### 2. 用户界面增强
- **刷新按钮**: 新增手动刷新账号信息功能
- **实时状态**: 刷新时显示"刷新中..."状态
- **按钮样式**: 优化刷新和批量操作按钮的视觉效果
- **用户反馈**: 改进操作反馈和状态提示

#### 3. 架构优化
- **轮询检查**: 每0.5秒检查登录结果，替代回调机制
- **结果存储**: 使用字典存储登录结果，避免跨线程传递
- **定时器机制**: 使用Qt定时器在主线程中处理结果
- **进程清理**: 优化浏览器进程清理机制

### 🛠️ 技术改进

#### 核心修复
- **CookieService**: 移除线程间回调，使用结果存储机制
- **AccountListTab**: 添加轮询检查和手动刷新功能
- **MainWindow**: 确保所有组件在主线程中创建
- **main.py**: 强化主线程验证和应用程序安全启动

#### 新增功能
- `check_login_results()`: 轮询检查登录结果
- `get_login_result()`: 获取存储的登录结果
- `manual_refresh()`: 手动刷新账号列表
- `callback_results`: 登录结果存储字典

### 📊 修复验证
- ✅ 线程安全测试通过
- ✅ NSInternalInconsistencyException已修复
- ✅ 刷新按钮功能正常
- ✅ UI操作线程安全
- ✅ 应用程序稳定性提升

---

## v1.2.1 (2025-07-10) - 进程管理和稳定性修复

### 🔧 重要修复

#### 1. 浏览器进程管理
- **强制进程清理**: 修复selenium浏览器进程未正确关闭的问题
- **内存泄漏修复**: 防止Chrome进程残留导致的内存泄漏
- **多层次清理**: 实现正常关闭→进程终止→强制杀死的清理策略
- **残留进程检测**: 自动检测和清理可能残留的无痕Chrome进程

#### 2. NSInternalInconsistencyException修复
- **信号处理优化**: 使用QTimer避免Qt内部一致性异常
- **高DPI设置优化**: 在正确时机设置高DPI支持
- **资源清理机制**: 完善应用程序退出时的资源清理

#### 3. 应用程序稳定性提升
- **信号处理器**: 注册SIGINT和SIGTERM信号处理
- **退出清理**: 使用atexit确保应用退出时清理资源
- **异常处理**: 改进错误处理和恢复机制

### 🛠️ 技术改进

#### 新增依赖
```
psutil==5.9.6  # 进程管理和监控
```

#### 核心组件更新
- **CookieService**: 新增强制进程清理方法
- **MainWindow**: 优化应用程序生命周期管理
- **AccountListTab**: 改进登录回调中的资源清理

### 📊 修复验证
- ✅ 进程管理功能测试通过
- ✅ 应用程序稳定性测试通过
- ✅ 浏览器登录功能正常
- ✅ 资源清理机制正常

---

## v1.2.0 (2025-07-09) - 重大功能更新

### 🚀 新增功能

#### 1. Agent状态动态管理
- **智能状态判断**: Agent状态现在会根据实际操作动态显示
  - `采集中`: 当有账号正在进行数据抓取时
  - `登录中`: 当有账号正在进行登录操作时  
  - `运行中`: 系统空闲状态
- **实时状态更新**: 每秒自动更新状态信息
- **详细状态显示**: 显示具体的操作账号数量

#### 2. Selenium无痕浏览器登录
- **隐私保护**: 使用无痕模式打开浏览器，保护用户隐私
- **自动化程度更高**: 无需手动复制Cookie，系统自动检测
- **智能Cookie监控**: 实时监控登录状态，自动获取Cookie
- **登录状态回调**: 登录成功/失败自动通知用户

#### 3. 状态管理器 (StatusManager)
- **单例模式**: 全局统一的状态管理
- **线程安全**: 支持多线程环境下的状态管理
- **状态追踪**: 追踪抓取和登录操作的账号
- **运行时间统计**: 显示应用运行时长

### 🔧 功能改进

#### 1. 界面优化
- **动态统计信息**: 仪表盘实时显示系统状态
- **运行时间显示**: 新增运行时间统计
- **状态颜色区分**: 不同状态使用不同颜色标识
- **按钮交互优化**: 改进按钮布局和响应

#### 2. Cookie管理增强
- **双重获取方式**: 支持selenium自动获取和手动设置
- **智能检测**: 自动识别有效的认证Cookie
- **状态同步**: Cookie获取状态与界面实时同步
- **错误处理**: 更好的错误提示和降级处理

#### 3. 用户体验提升
- **操作反馈**: 所有操作都有明确的状态反馈
- **进度提示**: 登录和抓取过程有详细的进度提示
- **自动刷新**: 状态变化后自动刷新界面
- **错误恢复**: 操作失败后自动恢复到正常状态

### 🛠️ 技术改进

#### 1. 依赖更新
```
新增依赖:
- selenium==4.15.0
- webdriver-manager==4.0.1
```

#### 2. 架构优化
- **状态管理分离**: 将状态管理逻辑独立为单独模块
- **回调机制**: 引入回调函数处理异步操作结果
- **线程安全**: 改进多线程环境下的数据安全性

#### 3. 错误处理
- **降级策略**: selenium不可用时自动降级到传统方式
- **异常捕获**: 更完善的异常处理机制
- **用户友好**: 错误信息更加用户友好

### 📋 使用说明

#### 新的登录流程
1. 点击账号列表中的"浏览器登录"按钮
2. 系统自动打开无痕浏览器窗口
3. 在浏览器中完成正常的登录操作
4. 系统自动检测登录状态并获取Cookie
5. 登录成功后会弹出提示，账号状态自动更新为"已登录"

#### 状态监控
- **Agent状态**: 实时显示系统当前状态
- **账号数量**: 显示总账号数
- **采集中账号**: 显示正在抓取数据的账号数
- **运行时间**: 显示应用运行时长

### 🔍 测试验证

#### 功能测试
- ✅ 状态管理器单例模式
- ✅ 动态状态更新
- ✅ Selenium环境检测
- ✅ Cookie服务集成
- ✅ 账号管理功能

#### 兼容性测试
- ✅ macOS 系统兼容性
- ✅ Chrome浏览器支持
- ✅ 降级机制验证

### 🚨 注意事项

1. **Chrome浏览器要求**: 需要安装Chrome浏览器才能使用selenium功能
2. **网络环境**: 首次使用时需要下载ChromeDriver
3. **权限要求**: 可能需要授权应用访问浏览器
4. **隐私保护**: 无痕模式不会保存浏览历史和Cookie到本地浏览器

### 🔄 向后兼容

- 保持与v1.1.0的完全向后兼容
- 原有的手动Cookie设置功能继续可用
- 数据文件格式无变化
- API接口保持不变

---

## v1.1.0 (2025-07-09) - 界面重构

### 主要更新
- 🎨 重构GUI界面，采用现代化设计风格
- 🍪 新增手动设置Cookie功能
- 📋 新增账号详情查看功能
- 🔧 优化浏览器Cookie获取机制
- 📊 改进仪表盘统计信息显示
- 🎯 优化按钮布局和交互体验

---

## v1.0.0 (2025-07-09) - 初始版本

### 基础功能
- 初始版本发布
- 支持小红书千帆数据抓取
- 实现基础的平台和账号管理功能
- 支持Excel数据导出
