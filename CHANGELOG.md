# 更新日志

## v1.2.1 (2025-07-10) - 进程管理和稳定性修复

### 🔧 重要修复

#### 1. 浏览器进程管理
- **强制进程清理**: 修复selenium浏览器进程未正确关闭的问题
- **内存泄漏修复**: 防止Chrome进程残留导致的内存泄漏
- **多层次清理**: 实现正常关闭→进程终止→强制杀死的清理策略
- **残留进程检测**: 自动检测和清理可能残留的无痕Chrome进程

#### 2. NSInternalInconsistencyException修复
- **信号处理优化**: 使用QTimer避免Qt内部一致性异常
- **高DPI设置优化**: 在正确时机设置高DPI支持
- **资源清理机制**: 完善应用程序退出时的资源清理

#### 3. 应用程序稳定性提升
- **信号处理器**: 注册SIGINT和SIGTERM信号处理
- **退出清理**: 使用atexit确保应用退出时清理资源
- **异常处理**: 改进错误处理和恢复机制

### 🛠️ 技术改进

#### 新增依赖
```
psutil==5.9.6  # 进程管理和监控
```

#### 核心组件更新
- **CookieService**: 新增强制进程清理方法
- **MainWindow**: 优化应用程序生命周期管理
- **AccountListTab**: 改进登录回调中的资源清理

### 📊 修复验证
- ✅ 进程管理功能测试通过
- ✅ 应用程序稳定性测试通过
- ✅ 浏览器登录功能正常
- ✅ 资源清理机制正常

---

## v1.2.0 (2025-07-09) - 重大功能更新

### 🚀 新增功能

#### 1. Agent状态动态管理
- **智能状态判断**: Agent状态现在会根据实际操作动态显示
  - `采集中`: 当有账号正在进行数据抓取时
  - `登录中`: 当有账号正在进行登录操作时  
  - `运行中`: 系统空闲状态
- **实时状态更新**: 每秒自动更新状态信息
- **详细状态显示**: 显示具体的操作账号数量

#### 2. Selenium无痕浏览器登录
- **隐私保护**: 使用无痕模式打开浏览器，保护用户隐私
- **自动化程度更高**: 无需手动复制Cookie，系统自动检测
- **智能Cookie监控**: 实时监控登录状态，自动获取Cookie
- **登录状态回调**: 登录成功/失败自动通知用户

#### 3. 状态管理器 (StatusManager)
- **单例模式**: 全局统一的状态管理
- **线程安全**: 支持多线程环境下的状态管理
- **状态追踪**: 追踪抓取和登录操作的账号
- **运行时间统计**: 显示应用运行时长

### 🔧 功能改进

#### 1. 界面优化
- **动态统计信息**: 仪表盘实时显示系统状态
- **运行时间显示**: 新增运行时间统计
- **状态颜色区分**: 不同状态使用不同颜色标识
- **按钮交互优化**: 改进按钮布局和响应

#### 2. Cookie管理增强
- **双重获取方式**: 支持selenium自动获取和手动设置
- **智能检测**: 自动识别有效的认证Cookie
- **状态同步**: Cookie获取状态与界面实时同步
- **错误处理**: 更好的错误提示和降级处理

#### 3. 用户体验提升
- **操作反馈**: 所有操作都有明确的状态反馈
- **进度提示**: 登录和抓取过程有详细的进度提示
- **自动刷新**: 状态变化后自动刷新界面
- **错误恢复**: 操作失败后自动恢复到正常状态

### 🛠️ 技术改进

#### 1. 依赖更新
```
新增依赖:
- selenium==4.15.0
- webdriver-manager==4.0.1
```

#### 2. 架构优化
- **状态管理分离**: 将状态管理逻辑独立为单独模块
- **回调机制**: 引入回调函数处理异步操作结果
- **线程安全**: 改进多线程环境下的数据安全性

#### 3. 错误处理
- **降级策略**: selenium不可用时自动降级到传统方式
- **异常捕获**: 更完善的异常处理机制
- **用户友好**: 错误信息更加用户友好

### 📋 使用说明

#### 新的登录流程
1. 点击账号列表中的"浏览器登录"按钮
2. 系统自动打开无痕浏览器窗口
3. 在浏览器中完成正常的登录操作
4. 系统自动检测登录状态并获取Cookie
5. 登录成功后会弹出提示，账号状态自动更新为"已登录"

#### 状态监控
- **Agent状态**: 实时显示系统当前状态
- **账号数量**: 显示总账号数
- **采集中账号**: 显示正在抓取数据的账号数
- **运行时间**: 显示应用运行时长

### 🔍 测试验证

#### 功能测试
- ✅ 状态管理器单例模式
- ✅ 动态状态更新
- ✅ Selenium环境检测
- ✅ Cookie服务集成
- ✅ 账号管理功能

#### 兼容性测试
- ✅ macOS 系统兼容性
- ✅ Chrome浏览器支持
- ✅ 降级机制验证

### 🚨 注意事项

1. **Chrome浏览器要求**: 需要安装Chrome浏览器才能使用selenium功能
2. **网络环境**: 首次使用时需要下载ChromeDriver
3. **权限要求**: 可能需要授权应用访问浏览器
4. **隐私保护**: 无痕模式不会保存浏览历史和Cookie到本地浏览器

### 🔄 向后兼容

- 保持与v1.1.0的完全向后兼容
- 原有的手动Cookie设置功能继续可用
- 数据文件格式无变化
- API接口保持不变

---

## v1.1.0 (2025-07-09) - 界面重构

### 主要更新
- 🎨 重构GUI界面，采用现代化设计风格
- 🍪 新增手动设置Cookie功能
- 📋 新增账号详情查看功能
- 🔧 优化浏览器Cookie获取机制
- 📊 改进仪表盘统计信息显示
- 🎯 优化按钮布局和交互体验

---

## v1.0.0 (2025-07-09) - 初始版本

### 基础功能
- 初始版本发布
- 支持小红书千帆数据抓取
- 实现基础的平台和账号管理功能
- 支持Excel数据导出
