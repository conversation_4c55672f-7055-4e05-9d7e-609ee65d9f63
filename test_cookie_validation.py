#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cookie验证功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_validator import CookieValidator
from src.services.account_service import AccountService


def test_cookie_validation():
    """测试Cookie验证功能"""
    print("=== 测试Cookie验证功能 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_validator = CookieValidator(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取所有账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("没有可用的账号进行测试")
        return
    
    print(f"找到 {len(accounts)} 个账号")
    
    for account in accounts:
        print(f"\n测试账号: {account.account_name} (ID: {account.account_id})")
        
        # 检查是否有Cookie
        cookie_data = data_manager.get_cookie(account.account_id)
        if not cookie_data:
            print("  ❌ 没有Cookie")
            continue
        
        print("  ✅ 有Cookie")
        
        # 验证Cookie
        is_valid, message = cookie_validator.validate_cookie(account.account_id)
        print(f"  验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
        print(f"  验证消息: {message}")
        
        # 测试验证并更新状态
        print("  测试验证并更新状态...")
        result = cookie_validator.validate_and_update_status(account.account_id)
        print(f"  更新结果: {'✅ 保持有效' if result else '❌ 已清除无效Cookie'}")
        
        # 检查更新后的登录状态
        is_logged_in = account_service.is_account_logged_in(account.account_id)
        print(f"  当前登录状态: {'✅ 已登录' if is_logged_in else '❌ 未登录'}")


def test_account_login_status_update():
    """测试账号登录状态更新"""
    print("\n=== 测试账号登录状态更新 ===")
    
    # 初始化服务
    data_manager = DataManager()
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("没有可用的账号进行测试")
        return
    
    account = accounts[0]
    print(f"测试账号: {account.account_name}")
    
    # 检查当前登录状态
    current_status = account_service.is_account_logged_in(account.account_id)
    print(f"当前登录状态: {'已登录' if current_status else '未登录'}")
    
    # 测试更新登录状态为未登录
    print("测试设置为未登录状态...")
    account_service.update_account_login_status(account.account_id, False)
    
    # 检查更新后状态
    new_status = account_service.is_account_logged_in(account.account_id)
    print(f"更新后登录状态: {'已登录' if new_status else '未登录'}")
    
    # 检查Cookie是否被清除
    cookie_data = data_manager.get_cookie(account.account_id)
    print(f"Cookie状态: {'存在' if cookie_data else '已清除'}")


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_validator = CookieValidator(data_manager)
    
    # 测试不存在的账号
    print("测试不存在的账号...")
    is_valid, message = cookie_validator.validate_cookie("non-existent-account")
    print(f"验证结果: {'有效' if is_valid else '无效'}")
    print(f"错误消息: {message}")
    
    # 测试验证并更新不存在的账号
    print("测试验证并更新不存在的账号...")
    result = cookie_validator.validate_and_update_status("non-existent-account")
    print(f"更新结果: {'成功' if result else '失败'}")


def main():
    """主函数"""
    print("Cookie验证功能测试")
    print("=" * 50)
    
    try:
        test_cookie_validation()
        test_account_login_status_update()
        test_error_handling()
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
