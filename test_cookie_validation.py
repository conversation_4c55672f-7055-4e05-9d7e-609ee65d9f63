#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie验证功能测试
验证修改后的cookie验证是否正确调用scrape_xiaohongshu_data方法
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_cookie_validator_import():
    """测试CookieValidator导入"""
    print("=" * 60)
    print("CookieValidator导入测试")
    print("=" * 60)
    
    try:
        from src.services.cookie_validator import CookieValidator
        from src.utils.data_manager import DataManager
        print("✅ CookieValidator 导入成功")
        return True
    except Exception as e:
        print(f"❌ CookieValidator 导入失败: {e}")
        return False


def test_cookie_validator_initialization():
    """测试CookieValidator初始化"""
    print("=" * 60)
    print("CookieValidator初始化测试")
    print("=" * 60)
    
    try:
        from src.services.cookie_validator import CookieValidator
        from src.utils.data_manager import DataManager
        
        # 初始化数据管理器
        data_manager = DataManager()
        print("✅ DataManager 初始化成功")
        
        # 初始化Cookie验证器
        cookie_validator = CookieValidator(data_manager)
        print("✅ CookieValidator 初始化成功")
        
        return cookie_validator
        
    except Exception as e:
        print(f"❌ CookieValidator 初始化失败: {e}")
        return None


def test_cookie_validation_method():
    """测试Cookie验证方法"""
    print("=" * 60)
    print("Cookie验证方法测试")
    print("=" * 60)
    
    try:
        from src.services.cookie_validator import CookieValidator
        from src.utils.data_manager import DataManager
        
        # 初始化服务
        data_manager = DataManager()
        cookie_validator = CookieValidator(data_manager)
        
        # 获取账号列表
        accounts = data_manager.get_accounts()
        if not accounts:
            print("⚠️ 没有找到账号，无法测试Cookie验证")
            return False
        
        # 选择第一个账号进行测试
        account_id = list(accounts.keys())[0]
        account_name = accounts[account_id].get('name', account_id)
        
        print(f"测试账号: {account_name} (ID: {account_id})")
        
        # 检查账号是否有Cookie
        cookie_data = data_manager.get_cookie(account_id)
        if not cookie_data:
            print(f"⚠️ 账号 {account_name} 没有Cookie，无法测试验证功能")
            return False
        
        print(f"✅ 账号 {account_name} 有Cookie，开始验证...")
        
        # 调用Cookie验证方法
        is_valid, message = cookie_validator.validate_cookie(account_id)
        
        print(f"验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
        print(f"验证消息: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ Cookie验证方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_scraper_integration():
    """测试数据抓取器集成"""
    print("=" * 60)
    print("数据抓取器集成测试")
    print("=" * 60)
    
    try:
        from src.services.data_scraper import DataScraper
        from src.utils.data_manager import DataManager
        
        # 初始化服务
        data_manager = DataManager()
        data_scraper = DataScraper(data_manager)
        
        print("✅ DataScraper 初始化成功")
        
        # 检查scrape_xiaohongshu_data方法是否存在
        if hasattr(data_scraper, 'scrape_xiaohongshu_data'):
            print("✅ scrape_xiaohongshu_data 方法存在")
        else:
            print("❌ scrape_xiaohongshu_data 方法不存在")
            return False
        
        # 获取账号列表
        accounts = data_manager.get_accounts()
        if not accounts:
            print("⚠️ 没有找到账号，无法测试数据抓取")
            return False
        
        # 选择第一个账号进行测试
        account_id = list(accounts.keys())[0]
        account_name = accounts[account_id].get('name', account_id)
        
        print(f"测试账号: {account_name} (ID: {account_id})")
        
        # 检查账号是否有Cookie
        cookie_data = data_manager.get_cookie(account_id)
        if not cookie_data:
            print(f"⚠️ 账号 {account_name} 没有Cookie，无法测试数据抓取")
            return False
        
        print(f"✅ 账号 {account_name} 有Cookie，可以进行数据抓取测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据抓取器集成测试失败: {e}")
        return False


def test_method_signature():
    """测试方法签名"""
    print("=" * 60)
    print("方法签名测试")
    print("=" * 60)
    
    try:
        from src.services.cookie_validator import CookieValidator
        from src.utils.data_manager import DataManager
        import inspect
        
        # 初始化服务
        data_manager = DataManager()
        cookie_validator = CookieValidator(data_manager)
        
        # 检查_test_cookie_with_api方法签名
        method = getattr(cookie_validator, '_test_cookie_with_api')
        signature = inspect.signature(method)
        
        print(f"_test_cookie_with_api 方法签名: {signature}")
        
        # 检查参数
        params = list(signature.parameters.keys())
        if 'account_id' in params:
            print("✅ 方法包含 account_id 参数")
        else:
            print("❌ 方法缺少 account_id 参数")
            return False
        
        if 'api_url' not in params:
            print("✅ 方法已移除 api_url 参数")
        else:
            print("⚠️ 方法仍包含 api_url 参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法签名测试失败: {e}")
        return False


def main():
    """主函数"""
    print("Cookie验证功能测试开始...")
    print()
    
    # 运行所有测试
    test_results = []
    
    # 导入测试
    import_result = test_cookie_validator_import()
    test_results.append(("模块导入", import_result))
    print()
    
    if not import_result:
        print("❌ 模块导入失败，跳过后续测试")
        return
    
    # 初始化测试
    init_result = test_cookie_validator_initialization()
    test_results.append(("服务初始化", init_result is not None))
    print()
    
    # 方法签名测试
    signature_result = test_method_signature()
    test_results.append(("方法签名", signature_result))
    print()
    
    # 数据抓取器集成测试
    integration_result = test_data_scraper_integration()
    test_results.append(("数据抓取器集成", integration_result))
    print()
    
    # Cookie验证方法测试
    validation_result = test_cookie_validation_method()
    test_results.append(("Cookie验证方法", validation_result))
    print()
    
    # 总结
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有测试通过！Cookie验证功能修改成功")
        print()
        print("修改内容:")
        print("1. ✅ CookieValidator现在调用DataScraper.scrape_xiaohongshu_data方法")
        print("2. ✅ 验证逻辑基于实际数据抓取结果")
        print("3. ✅ 移除了直接HTTP请求的验证方式")
        print("4. ✅ 方法签名已更新为使用account_id参数")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    print()
    print("功能说明:")
    print("- Cookie验证现在通过调用scrape_xiaohongshu_data方法来判断接口是否正常工作")
    print("- 如果数据抓取成功，说明Cookie有效")
    print("- 如果数据抓取失败，说明Cookie无效或已过期")
    print("- 这种方式更准确地反映了Cookie在实际业务中的可用性")


if __name__ == "__main__":
    main()
