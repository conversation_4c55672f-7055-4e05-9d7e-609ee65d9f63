#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射功能测试脚本
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.data_scraper import DataScraper
from src.utils.data_manager import DataManager


def test_field_mapping_extraction():
    """测试字段映射提取功能"""
    print("🔍 测试字段映射提取功能...")
    
    try:
        # 创建测试数据
        test_data = {
            "code": 200,
            "message": "success",
            "data": {
                "list": [
                    {
                        "id": 1,
                        "title": "测试笔记1",
                        "view_count": 1000,
                        "like_count": 50,
                        "create_time": "2025-01-01",
                        "author": {
                            "name": "测试用户1",
                            "followers": 5000
                        }
                    },
                    {
                        "id": 2,
                        "title": "测试笔记2",
                        "view_count": 2000,
                        "like_count": 80,
                        "create_time": "2025-01-02",
                        "author": {
                            "name": "测试用户2",
                            "followers": 8000
                        }
                    }
                ],
                "total": 2
            }
        }
        
        # 创建字段映射配置
        field_mappings = [
            {
                "field_name": "笔记标题",
                "json_path": "$.data.list[*].title",
                "data_type": "文本",
                "default_value": ""
            },
            {
                "field_name": "浏览量",
                "json_path": "$.data.list[*].view_count",
                "data_type": "数字",
                "default_value": "0"
            },
            {
                "field_name": "点赞数",
                "json_path": "$.data.list[*].like_count",
                "data_type": "数字",
                "default_value": "0"
            },
            {
                "field_name": "发布日期",
                "json_path": "$.data.list[*].create_time",
                "data_type": "日期",
                "default_value": ""
            },
            {
                "field_name": "作者名称",
                "json_path": "$.data.list[*].author.name",
                "data_type": "文本",
                "default_value": "未知"
            }
        ]
        
        # 创建数据抓取器
        data_manager = DataManager()
        scraper = DataScraper(data_manager)
        
        # 测试字段映射提取
        extracted_data = scraper.extract_data_with_field_mappings(test_data, field_mappings)
        
        print(f"✅ 提取到 {len(extracted_data)} 条记录")
        
        # 验证提取结果
        assert len(extracted_data) == 2, f"应该提取到2条记录，实际提取到{len(extracted_data)}条"
        
        # 验证第一条记录
        first_record = extracted_data[0]
        assert first_record["笔记标题"] == "测试笔记1", f"标题不匹配: {first_record['笔记标题']}"
        assert first_record["浏览量"] == 1000, f"浏览量不匹配: {first_record['浏览量']}"
        assert first_record["点赞数"] == 50, f"点赞数不匹配: {first_record['点赞数']}"
        assert first_record["发布日期"] == "2025-01-01", f"日期不匹配: {first_record['发布日期']}"
        assert first_record["作者名称"] == "测试用户1", f"作者不匹配: {first_record['作者名称']}"
        
        # 验证第二条记录
        second_record = extracted_data[1]
        assert second_record["笔记标题"] == "测试笔记2", f"标题不匹配: {second_record['笔记标题']}"
        assert second_record["浏览量"] == 2000, f"浏览量不匹配: {second_record['浏览量']}"
        
        print("✅ 字段映射提取功能测试通过")
        
        # 打印提取结果
        print("\n提取结果:")
        for i, record in enumerate(extracted_data):
            print(f"记录 {i+1}: {record}")
        
        return True
        
    except Exception as e:
        print(f"❌ 字段映射提取功能测试失败: {e}")
        return False


def test_data_type_conversion():
    """测试数据类型转换"""
    print("\n🔍 测试数据类型转换...")
    
    try:
        data_manager = DataManager()
        scraper = DataScraper(data_manager)
        
        # 测试数字转换
        assert scraper.convert_data_type("1000", "数字") == 1000
        assert scraper.convert_data_type("1000.5", "数字") == 1000.5
        assert scraper.convert_data_type("1,000", "数字") == 1000
        assert scraper.convert_data_type("abc", "数字") == 0
        
        # 测试日期转换
        assert scraper.convert_data_type("2025-01-01", "日期") == "2025-01-01"
        assert scraper.convert_data_type("2025/01/01", "日期") == "2025-01-01"
        
        # 测试文本转换
        assert scraper.convert_data_type(123, "文本") == "123"
        assert scraper.convert_data_type(None, "文本") == ""
        
        print("✅ 数据类型转换测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据类型转换测试失败: {e}")
        return False


def test_traditional_vs_field_mapping():
    """测试传统提取vs字段映射提取的对比"""
    print("\n🔍 测试传统提取vs字段映射提取对比...")
    
    try:
        # 创建测试数据
        test_data = {
            "data": {
                "list": [
                    {
                        "title": "笔记1",
                        "stats": {"views": 1000, "likes": 50},
                        "extra_field": "不需要的数据",
                        "nested": {
                            "deep": {
                                "value": "深层数据"
                            }
                        }
                    }
                ]
            }
        }
        
        data_manager = DataManager()
        scraper = DataScraper(data_manager)
        
        # 传统提取（提取所有数据）
        traditional_result = scraper.extract_data_with_traditional_jsonpath(
            test_data, "$.data.list[*]"
        )
        
        print(f"传统提取结果: {len(traditional_result)} 条记录")
        print(f"传统提取字段数: {len(traditional_result[0].keys()) if traditional_result else 0}")
        
        # 字段映射提取（只提取指定字段）
        field_mappings = [
            {
                "field_name": "标题",
                "json_path": "$.data.list[*].title",
                "data_type": "文本",
                "default_value": ""
            },
            {
                "field_name": "浏览量",
                "json_path": "$.data.list[*].stats.views",
                "data_type": "数字",
                "default_value": "0"
            }
        ]
        
        field_mapping_result = scraper.extract_data_with_field_mappings(
            test_data, field_mappings
        )
        
        print(f"字段映射提取结果: {len(field_mapping_result)} 条记录")
        print(f"字段映射提取字段数: {len(field_mapping_result[0].keys()) if field_mapping_result else 0}")
        
        # 验证字段映射只提取了指定字段
        if field_mapping_result:
            expected_fields = {"标题", "浏览量"}
            actual_fields = set(field_mapping_result[0].keys())
            assert actual_fields == expected_fields, f"字段不匹配: {actual_fields} != {expected_fields}"
        
        print("✅ 传统提取vs字段映射提取对比测试通过")
        print(f"传统提取: {traditional_result}")
        print(f"字段映射提取: {field_mapping_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 传统提取vs字段映射提取对比测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🎯 字段映射功能测试")
    print("=" * 50)
    
    tests = [
        ("字段映射提取功能", test_field_mapping_extraction),
        ("数据类型转换", test_data_type_conversion),
        ("传统提取vs字段映射提取对比", test_traditional_vs_field_mapping),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有字段映射功能测试通过！")
        print("\n✨ 功能特点:")
        print("  ✅ 支持精确字段提取")
        print("  ✅ 支持数据类型转换")
        print("  ✅ 支持默认值设置")
        print("  ✅ 支持嵌套字段提取")
        print("  ✅ 减少不必要的数据传输")
    else:
        print("⚠️  部分字段映射功能存在问题")
    
    return failed == 0


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
