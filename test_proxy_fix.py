#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理修复
验证小红书接口访问代理配置修复是否有效
"""

import sys
import os
import requests
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.proxy_manager import ProxyManager, get_proxy_manager, configure_proxy
from src.services.shop_name_service import ShopNameService
from src.services.cookie_validator import CookieValidator
from src.services.data_scraper import DataScraper
from src.utils.data_manager import DataManager


def test_proxy_manager():
    """测试代理管理器"""
    print("=" * 60)
    print("代理管理器测试")
    print("=" * 60)
    
    # 创建代理管理器
    proxy_manager = ProxyManager()
    
    print(f"代理配置: {proxy_manager.proxies}")
    print(f"代理状态: {'可用' if proxy_manager.proxy_available else '不可用'}")
    print()
    
    # 测试基本HTTP请求
    print("测试基本HTTP请求...")
    try:
        response = proxy_manager.get('http://httpbin.org/ip', timeout=10)
        print(f"✅ 请求成功，状态码: {response.status_code}")
        data = response.json()
        print(f"IP信息: {data.get('origin', 'N/A')}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    print()
    
    # 测试HTTPS请求
    print("测试HTTPS请求...")
    try:
        response = proxy_manager.get('https://httpbin.org/ip', timeout=10)
        print(f"✅ HTTPS请求成功，状态码: {response.status_code}")
        data = response.json()
        print(f"IP信息: {data.get('origin', 'N/A')}")
    except Exception as e:
        print(f"❌ HTTPS请求失败: {e}")
    print()


def test_xiaohongshu_api_with_proxy():
    """测试小红书API代理访问"""
    print("=" * 60)
    print("小红书API代理访问测试")
    print("=" * 60)
    
    # 测试URL
    test_urls = [
        "https://ark.xiaohongshu.com/api/user/profile",
        "https://ark.xiaohongshu.com/api/edith/seller/detail"
    ]
    
    proxy_manager = get_proxy_manager()
    
    for url in test_urls:
        print(f"测试URL: {url}")
        
        # 构建请求头
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Referer': 'https://customer.xiaohongshu.com/login?service=https://ark.xiaohongshu.com/app-seller/sellerInfo?from=ark-login',
            'Origin': 'https://customer.xiaohongshu.com',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=1, i'
        }
        
        params = {"service": "https%3A%2F%2Fark.xiaohongshu.com%2Fapp-seller%2FsellerInfo%3Ffrom%3Dark-login"}
        
        try:
            response = proxy_manager.get(url, headers=headers, params=params, timeout=10)
            print(f"  状态码: {response.status_code}")
            print(f"  响应长度: {len(response.text)}")
            
            # 检查是否是"failed to load response data"错误
            if "failed to load response data" in response.text.lower():
                print("  ❌ 检测到 'failed to load response data' 错误")
            elif response.status_code == 200:
                try:
                    data = response.json()
                    print("  ✅ JSON解析成功")
                    print(f"  响应数据: {str(data)[:100]}...")
                except:
                    print("  ⚠️ 非JSON响应（可能是HTML页面）")
                    print(f"  响应内容: {response.text[:100]}...")
            else:
                print(f"  ❌ 请求失败，响应内容: {response.text[:200]}...")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
        
        print()


def test_shop_name_service_with_proxy():
    """测试店铺名称服务代理访问"""
    print("=" * 60)
    print("店铺名称服务代理访问测试")
    print("=" * 60)
    
    shop_name_service = ShopNameService()
    
    # 测试参数
    test_url = "https://ark.xiaohongshu.com/api/user/profile"
    test_field = "data.name"
    mock_cookie = "sessionid=test; token=test"
    
    print(f"测试URL: {test_url}")
    print(f"字段路径: {test_field}")
    print()
    
    try:
        # 测试不带签名头
        print("1. 测试不带签名头:")
        shop_name1 = shop_name_service.get_shop_name(test_url, test_field, mock_cookie)
        print(f"   结果: {shop_name1}")
        print()
        
        # 测试带签名头
        print("2. 测试带签名头:")
        mock_signature_headers = {
            'x-s': 'XYW_test_signature',
            'x-t': str(int(time.time() * 1000))
        }
        shop_name2 = shop_name_service.get_shop_name_with_signatures(test_url, test_field, mock_cookie, mock_signature_headers)
        print(f"   结果: {shop_name2}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_cookie_validator_with_proxy():
    """测试Cookie验证器代理访问"""
    print("=" * 60)
    print("Cookie验证器代理访问测试")
    print("=" * 60)
    
    data_manager = DataManager()
    validator = CookieValidator(data_manager)
    
    # 创建测试账号
    test_account_id = "test_proxy_account"
    mock_cookie = "sessionid=test; token=test"
    
    try:
        # 保存测试Cookie
        data_manager.save_cookie(test_account_id, mock_cookie)
        
        # 验证Cookie
        is_valid, message = validator.validate_cookie(test_account_id)
        print(f"验证结果: {is_valid}")
        print(f"验证消息: {message}")
        
        # 清理测试数据
        data_manager.delete_cookie(test_account_id)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_data_scraper_with_proxy():
    """测试数据抓取器代理访问"""
    print("=" * 60)
    print("数据抓取器代理访问测试")
    print("=" * 60)
    
    try:
        data_manager = DataManager()
        scraper = DataScraper(data_manager)
        
        print("✅ 数据抓取器初始化成功")
        print("注意: 实际数据抓取需要有效的账号和Cookie")
        
    except Exception as e:
        print(f"❌ 数据抓取器初始化失败: {e}")


def test_proxy_fallback():
    """测试代理回退机制"""
    print("=" * 60)
    print("代理回退机制测试")
    print("=" * 60)
    
    # 创建一个使用无效代理的管理器
    invalid_proxy_manager = ProxyManager(proxy_host="127.0.0.1", proxy_port=9999)
    
    print(f"无效代理配置: {invalid_proxy_manager.proxies}")
    print(f"代理状态: {'可用' if invalid_proxy_manager.proxy_available else '不可用'}")
    print()
    
    # 测试回退机制
    print("测试代理回退机制...")
    try:
        response = invalid_proxy_manager.get('http://httpbin.org/ip', timeout=10)
        print(f"✅ 回退成功，状态码: {response.status_code}")
        data = response.json()
        print(f"IP信息: {data.get('origin', 'N/A')}")
    except Exception as e:
        print(f"❌ 回退失败: {e}")


if __name__ == "__main__":
    print("开始测试代理修复...")
    print()
    
    # 配置代理
    configure_proxy("127.0.0.1", 1212)
    
    # 运行所有测试
    test_proxy_manager()
    test_xiaohongshu_api_with_proxy()
    test_shop_name_service_with_proxy()
    test_cookie_validator_with_proxy()
    test_data_scraper_with_proxy()
    test_proxy_fallback()
    
    print("=" * 60)
    print("测试完成")
    print("=" * 60)
    print()
    print("总结:")
    print("1. 已实现统一的代理管理器")
    print("2. 已添加代理回退机制，代理失败时自动直连")
    print("3. 已修复代理配置格式问题")
    print("4. 已优化错误处理和日志输出")
    print("5. 如果仍然出现'failed to load response data'，可能需要:")
    print("   - 检查代理服务器是否正常运行")
    print("   - 验证代理配置是否正确")
    print("   - 确认网络连接是否稳定")
