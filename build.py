#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本
"""

import os
import sys
import platform
import subprocess
import shutil


def clean_build():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")
    
    # 清理.spec文件
    for file in os.listdir('.'):
        if file.endswith('.spec'):
            os.remove(file)
            print(f"已清理文件: {file}")


def build_app():
    """构建应用程序"""
    system = platform.system()
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=运营数据采集器',
        '--icon=icon.ico' if system == 'Windows' else '--icon=icon.icns',
        '--add-data=data:data',
        '--hidden-import=PyQt5.sip',
        '--hidden-import=jsonpath_ng',
        '--hidden-import=openpyxl',
        '--hidden-import=requests',
        'main.py'
    ]
    
    # 如果图标文件不存在，移除图标参数
    icon_file = 'icon.ico' if system == 'Windows' else 'icon.icns'
    if not os.path.exists(icon_file):
        cmd = [arg for arg in cmd if not arg.startswith('--icon')]
    
    print(f"开始构建 {system} 应用程序...")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("构建成功！")
        print(result.stdout)
        
        # 显示输出文件信息
        dist_dir = 'dist'
        if os.path.exists(dist_dir):
            files = os.listdir(dist_dir)
            print(f"\n构建输出文件位于 {dist_dir} 目录:")
            for file in files:
                file_path = os.path.join(dist_dir, file)
                size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                print(f"  - {file} ({size:.1f} MB)")
        
    except subprocess.CalledProcessError as e:
        print("构建失败！")
        print(f"错误信息: {e}")
        print(f"标准输出: {e.stdout}")
        print(f"错误输出: {e.stderr}")
        sys.exit(1)


def create_installer():
    """创建安装包"""
    system = platform.system()
    
    if system == 'Darwin':  # macOS
        print("创建 macOS DMG 安装包...")
        # 这里可以添加创建DMG的逻辑
        print("注意: DMG创建需要额外的工具，请手动创建")
    elif system == 'Windows':
        print("创建 Windows 安装包...")
        # 这里可以添加创建MSI或NSIS安装包的逻辑
        print("注意: Windows安装包创建需要额外的工具，请手动创建")
    else:
        print(f"不支持的系统: {system}")


def main():
    """主函数"""
    print("=" * 50)
    print("运营数据采集器 - 构建脚本")
    print("=" * 50)
    
    # 检查PyInstaller是否安装
    try:
        subprocess.run(['pyinstaller', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        sys.exit(1)
    
    # 清理构建目录
    clean_build()
    
    # 构建应用程序
    build_app()
    
    # 创建安装包
    create_installer()
    
    print("\n构建完成！")
    print("可执行文件位于 dist 目录中")


if __name__ == '__main__':
    main()
