#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于时间的Cookie验证功能测试
验证修改后的cookie验证是否正确传递时间参数
"""

import sys
import os
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_cookie_validation_with_time():
    """测试带时间参数的Cookie验证"""
    print("=" * 60)
    print("带时间参数的Cookie验证测试")
    print("=" * 60)
    
    try:
        from src.services.cookie_validator import CookieValidator
        from src.utils.data_manager import DataManager
        from src.services.data_scraper import DataScraper
        
        # 初始化服务
        data_manager = DataManager()
        cookie_validator = CookieValidator(data_manager)
        
        # 模拟账号ID和时间参数
        test_account_id = "test_account_123"
        start_date = "2024-01-01"
        end_date = "2024-01-02"
        
        print(f"测试账号ID: {test_account_id}")
        print(f"测试时间范围: {start_date} 到 {end_date}")
        
        # 模拟DataScraper的scrape_xiaohongshu_data方法
        with patch.object(DataScraper, 'scrape_xiaohongshu_data') as mock_scrape:
            # 测试场景1: 传递时间参数
            print("\n--- 测试场景1: 传递时间参数 ---")
            mock_scrape.return_value = {
                'data': [{'id': 1, 'name': 'test'}],
                'status': 'success'
            }
            
            is_valid, message = cookie_validator._test_cookie_with_api(test_account_id, start_date, end_date)
            print(f"验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
            print(f"验证消息: {message}")
            print(f"scrape_xiaohongshu_data 被调用: {mock_scrape.called}")
            
            # 检查调用参数
            if mock_scrape.called:
                call_args = mock_scrape.call_args
                print(f"调用参数: {call_args}")
                
                # 验证参数
                args, kwargs = call_args
                if len(args) >= 3:
                    print(f"  - account_id: {args[0]}")
                    print(f"  - start_date: {args[1]}")
                    print(f"  - end_date: {args[2]}")
                    
                    if args[1] == start_date and args[2] == end_date:
                        print("✅ 时间参数传递正确")
                    else:
                        print("❌ 时间参数传递错误")
                else:
                    print("❌ 参数数量不正确")
            
            # 测试场景2: 不传递时间参数（使用默认值）
            print("\n--- 测试场景2: 不传递时间参数（使用默认值） ---")
            mock_scrape.reset_mock()
            
            is_valid, message = cookie_validator._test_cookie_with_api(test_account_id)
            print(f"验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
            print(f"验证消息: {message}")
            print(f"scrape_xiaohongshu_data 被调用: {mock_scrape.called}")
            
            # 检查默认时间参数
            if mock_scrape.called:
                call_args = mock_scrape.call_args
                args, kwargs = call_args
                if len(args) >= 3:
                    print(f"  - account_id: {args[0]}")
                    print(f"  - start_date: {args[1]}")
                    print(f"  - end_date: {args[2]}")
                    
                    # 验证默认时间是否合理（昨天到今天）
                    today = datetime.now().date()
                    yesterday = today - timedelta(days=1)
                    expected_start = yesterday.strftime("%Y-%m-%d")
                    expected_end = today.strftime("%Y-%m-%d")
                    
                    if args[1] == expected_start and args[2] == expected_end:
                        print("✅ 默认时间参数正确")
                    else:
                        print(f"⚠️ 默认时间参数: 期望 {expected_start} 到 {expected_end}")
        
        return True
        
    except Exception as e:
        print(f"❌ 带时间参数的Cookie验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_validate_cookie_method():
    """测试validate_cookie方法的时间参数传递"""
    print("=" * 60)
    print("validate_cookie方法时间参数测试")
    print("=" * 60)
    
    try:
        from src.services.cookie_validator import CookieValidator
        from src.utils.data_manager import DataManager
        
        # 初始化服务
        data_manager = DataManager()
        cookie_validator = CookieValidator(data_manager)
        
        # 模拟账号ID和时间参数
        test_account_id = "test_account_123"
        start_date = "2024-01-01"
        end_date = "2024-01-02"
        
        print(f"测试账号ID: {test_account_id}")
        print(f"测试时间范围: {start_date} 到 {end_date}")
        
        # 模拟完整的验证流程
        with patch.object(data_manager, 'get_cookie') as mock_get_cookie, \
             patch.object(data_manager, 'get_accounts') as mock_get_accounts, \
             patch.object(data_manager, 'get_platforms') as mock_get_platforms, \
             patch.object(cookie_validator, '_test_cookie_with_api') as mock_test:
            
            # 设置模拟数据
            mock_get_cookie.return_value = {'cookie_str': 'test_cookie_string'}
            mock_get_accounts.return_value = {
                test_account_id: {
                    'name': 'Test Account',
                    'platform_id': 'test_platform'
                }
            }
            mock_get_platforms.return_value = {
                'test_platform': {
                    'name': 'Test Platform',
                    'data_api_url': 'https://test.api.com/data'
                }
            }
            mock_test.return_value = (True, "Cookie有效")
            
            # 测试带时间参数的验证
            print("\n--- 测试带时间参数的验证 ---")
            is_valid, message = cookie_validator.validate_cookie(test_account_id, start_date, end_date)
            print(f"验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
            print(f"验证消息: {message}")
            print(f"_test_cookie_with_api 被调用: {mock_test.called}")
            
            # 检查调用参数
            if mock_test.called:
                call_args = mock_test.call_args
                print(f"调用参数: {call_args}")
                
                args, kwargs = call_args
                if len(args) >= 3:
                    if args[1] == start_date and args[2] == end_date:
                        print("✅ 时间参数传递到_test_cookie_with_api正确")
                    else:
                        print("❌ 时间参数传递到_test_cookie_with_api错误")
                else:
                    print("❌ 参数数量不正确")
            
            # 测试不带时间参数的验证
            print("\n--- 测试不带时间参数的验证 ---")
            mock_test.reset_mock()
            
            is_valid, message = cookie_validator.validate_cookie(test_account_id)
            print(f"验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
            print(f"验证消息: {message}")
            print(f"_test_cookie_with_api 被调用: {mock_test.called}")
            
            # 检查默认参数传递
            if mock_test.called:
                call_args = mock_test.call_args
                args, kwargs = call_args
                if len(args) >= 3:
                    print(f"  - 传递的参数: account_id={args[0]}, start_date={args[1]}, end_date={args[2]}")
                    if args[1] is None and args[2] is None:
                        print("✅ 默认参数传递正确（None值）")
                    else:
                        print("⚠️ 默认参数不是None值")
        
        return True
        
    except Exception as e:
        print(f"❌ validate_cookie方法时间参数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_method_signatures():
    """测试方法签名"""
    print("=" * 60)
    print("方法签名测试")
    print("=" * 60)
    
    try:
        from src.services.cookie_validator import CookieValidator
        from src.utils.data_manager import DataManager
        import inspect
        
        # 初始化服务
        data_manager = DataManager()
        cookie_validator = CookieValidator(data_manager)
        
        # 检查validate_cookie方法签名
        validate_method = getattr(cookie_validator, 'validate_cookie')
        validate_signature = inspect.signature(validate_method)
        print(f"validate_cookie 方法签名: {validate_signature}")
        
        # 检查参数
        validate_params = list(validate_signature.parameters.keys())
        expected_params = ['account_id', 'start_date', 'end_date']
        
        for param in expected_params:
            if param in validate_params:
                print(f"✅ validate_cookie 包含 {param} 参数")
            else:
                print(f"❌ validate_cookie 缺少 {param} 参数")
        
        # 检查_test_cookie_with_api方法签名
        test_method = getattr(cookie_validator, '_test_cookie_with_api')
        test_signature = inspect.signature(test_method)
        print(f"_test_cookie_with_api 方法签名: {test_signature}")
        
        # 检查参数
        test_params = list(test_signature.parameters.keys())
        
        for param in expected_params:
            if param in test_params:
                print(f"✅ _test_cookie_with_api 包含 {param} 参数")
            else:
                print(f"❌ _test_cookie_with_api 缺少 {param} 参数")
        
        # 检查validate_and_update_status方法签名
        update_method = getattr(cookie_validator, 'validate_and_update_status')
        update_signature = inspect.signature(update_method)
        print(f"validate_and_update_status 方法签名: {update_signature}")
        
        # 检查参数
        update_params = list(update_signature.parameters.keys())
        
        for param in expected_params:
            if param in update_params:
                print(f"✅ validate_and_update_status 包含 {param} 参数")
            else:
                print(f"❌ validate_and_update_status 缺少 {param} 参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法签名测试失败: {e}")
        return False


def main():
    """主函数"""
    print("基于时间的Cookie验证功能测试开始...")
    print()
    
    # 运行所有测试
    test_results = []
    
    # 方法签名测试
    signature_result = test_method_signatures()
    test_results.append(("方法签名", signature_result))
    print()
    
    # 带时间参数的Cookie验证测试
    time_validation_result = test_cookie_validation_with_time()
    test_results.append(("时间参数验证", time_validation_result))
    print()
    
    # validate_cookie方法测试
    method_result = test_validate_cookie_method()
    test_results.append(("validate_cookie方法", method_result))
    print()
    
    # 总结
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有测试通过！基于时间的Cookie验证功能修改成功")
        print()
        print("功能确认:")
        print("1. ✅ Cookie验证方法现在支持时间参数")
        print("2. ✅ 时间参数正确传递给scrape_xiaohongshu_data方法")
        print("3. ✅ 支持默认时间参数（昨天到今天）")
        print("4. ✅ 所有相关方法签名已更新")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    print()
    print("使用说明:")
    print("- 在点击'开始采集'或'全部采集'按钮时，会先弹出时间选择对话框")
    print("- 选择的时间范围会传递给cookie验证过程")
    print("- Cookie验证会使用实际的数据抓取接口和选择的时间范围")
    print("- 这样可以更准确地验证Cookie在指定时间范围内的有效性")


if __name__ == "__main__":
    main()
