#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按钮在页面跳转后的持久性
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def test_button_persistence():
    """测试按钮在页面跳转后的持久性"""
    print("=== 测试按钮持久性功能 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("没有可用的账号进行测试")
        return
    
    account = accounts[0]
    print(f"使用账号: {account.account_name}")
    
    # 获取平台信息
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("账号没有关联的平台")
        return
    
    account, platform = account_platform
    print(f"平台: {platform.platform_name}")
    print(f"登录URL: {platform.login_url}")
    
    try:
        print("\n开始测试按钮持久性...")
        print("将打开无痕浏览器，请按以下步骤测试:")
        print("1. 观察右上角是否出现'我已完成登录'按钮")
        print("2. 在网站内进行页面跳转（点击链接、导航等）")
        print("3. 观察按钮是否在跳转后重新出现")
        print("4. 测试按钮的交互功能是否正常")
        print("5. 完成登录后点击按钮测试功能")
        
        # 启动Cookie获取
        cookie_service.start_incognito_login(
            account.account_id, 
            platform.login_url, 
            None  # 不需要回调
        )
        
        # 等待用户测试
        print("\n等待用户测试...")
        print("按 Ctrl+C 结束测试")
        
        start_time = time.time()
        
        while True:
            try:
                # 检查登录结果
                result = cookie_service.get_login_result(account.account_id)
                if result is not None:
                    success, message = result
                    print(f"\n登录结果: {'成功' if success else '失败'} - {message}")
                    
                    if success:
                        print("✅ 按钮功能测试成功！")
                        break
                    else:
                        print(f"❌ 登录失败: {message}")
                        break
                
                # 每30秒提示一次
                if int(time.time() - start_time) % 30 == 0:
                    print(".", end="", flush=True)
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                print("\n用户中断测试")
                break
        
        # 清理
        cookie_service.force_close_browser()
        print("\n测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_button_features():
    """测试按钮的各种功能"""
    print("\n=== 按钮功能测试指南 ===")
    print("请在浏览器中测试以下功能:")
    print()
    print("1. 按钮显示测试:")
    print("   - 按钮是否在页面右上角正确显示")
    print("   - 按钮样式是否正确（蓝色背景，白色文字）")
    print("   - 关闭按钮（×）是否显示")
    print()
    print("2. 页面跳转测试:")
    print("   - 点击网站内的链接进行页面跳转")
    print("   - 观察按钮是否在新页面重新出现")
    print("   - 测试多次页面跳转")
    print("   - 测试浏览器前进后退按钮")
    print()
    print("3. 按钮交互测试:")
    print("   - 鼠标悬停时按钮颜色是否变化")
    print("   - 点击按钮前确保已完成登录")
    print("   - 点击按钮后观察状态变化")
    print("   - 测试未登录状态下的提示信息")
    print()
    print("4. 状态保持测试:")
    print("   - 按钮状态是否在页面跳转后保持")
    print("   - 登录成功后按钮是否正确禁用")
    print("   - 错误提示是否正确显示和恢复")
    print()
    print("5. 关闭按钮测试:")
    print("   - 点击×按钮是否能正确关闭")
    print("   - 关闭后是否停止监控")


def main():
    """主函数"""
    print("按钮持久性测试工具")
    print("=" * 50)
    
    try:
        test_button_features()
        test_button_persistence()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
