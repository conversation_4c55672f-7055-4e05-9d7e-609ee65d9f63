#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试包装页面问题
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def debug_wrapper_page():
    """调试包装页面问题"""
    print("=== 调试包装页面问题 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号进行测试")
        return
    
    account = accounts[0]
    print(f"✅ 使用账号: {account.account_name}")
    
    # 获取平台信息
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return
    
    account, platform = account_platform
    print(f"✅ 平台: {platform.platform_name}")
    print(f"✅ 登录URL: {platform.login_url}")
    
    # 测试包装页面创建
    print("\n--- 测试包装页面创建 ---")
    try:
        wrapper_html = cookie_service._create_login_wrapper_page(account.account_id, platform.login_url)
        print(f"✅ 包装页面HTML长度: {len(wrapper_html)} 字符")
        
        # 检查关键内容
        checks = [
            ("账号ID", account.account_id in wrapper_html),
            ("登录URL", platform.login_url in wrapper_html),
            ("按钮元素", "我已完成登录" in wrapper_html),
            ("iframe元素", "login-iframe" in wrapper_html),
            ("JavaScript", "checkLogin" in wrapper_html)
        ]
        
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {'通过' if result else '失败'}")
        
        # 保存调试文件
        debug_file = "debug_wrapper.html"
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(wrapper_html)
        print(f"✅ 调试文件已保存: {debug_file}")
        
    except Exception as e:
        print(f"❌ 创建包装页面失败: {e}")
        return
    
    # 测试浏览器启动
    print("\n--- 测试浏览器启动 ---")
    try:
        print("正在启动无痕浏览器...")
        
        # 检查Selenium是否可用
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from webdriver_manager.chrome import ChromeDriverManager
            print("✅ Selenium组件可用")
        except ImportError as e:
            print(f"❌ Selenium组件不可用: {e}")
            return
        
        # 启动浏览器
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        print("✅ 浏览器已启动")
        print("\n请检查浏览器窗口:")
        print("1. 是否是无痕模式（地址栏左侧有无痕图标）")
        print("2. 页面顶部是否有蓝色渐变背景的按钮区域")
        print("3. 右上角是否有'我已完成登录'按钮")
        print("4. 下方是否显示目标登录页面")
        print("5. 点击按钮是否弹出确认对话框")
        
        # 等待用户检查
        input("\n按回车键继续...")
        
        # 检查浏览器状态
        if hasattr(cookie_service, 'driver') and cookie_service.driver:
            try:
                current_url = cookie_service.driver.current_url
                print(f"✅ 当前页面URL: {current_url}")
                
                if current_url.startswith('file://'):
                    print("✅ 正在使用包装页面")
                else:
                    print("❌ 没有使用包装页面，直接打开了目标URL")
                
                # 检查页面元素
                try:
                    button = cookie_service.driver.find_element("id", "loginButton")
                    print("✅ 找到登录按钮")
                    print(f"   按钮文本: {button.text}")
                except:
                    print("❌ 没有找到登录按钮")
                
                try:
                    iframe = cookie_service.driver.find_element("css selector", ".login-iframe")
                    print("✅ 找到iframe元素")
                except:
                    print("❌ 没有找到iframe元素")
                
            except Exception as e:
                print(f"❌ 检查浏览器状态失败: {e}")
        else:
            print("❌ 浏览器未启动或已关闭")
        
    except Exception as e:
        print(f"❌ 启动浏览器失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 清理
    try:
        if hasattr(cookie_service, 'driver') and cookie_service.driver:
            input("\n按回车键关闭浏览器...")
            cookie_service.force_close_browser()
            print("✅ 浏览器已关闭")
    except:
        pass


def check_selenium_installation():
    """检查Selenium安装情况"""
    print("\n=== 检查Selenium安装情况 ===")
    
    try:
        import selenium
        print(f"✅ Selenium版本: {selenium.__version__}")
    except ImportError:
        print("❌ Selenium未安装")
        print("请运行: pip install selenium")
        return False
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        print("✅ webdriver-manager可用")
    except ImportError:
        print("❌ webdriver-manager未安装")
        print("请运行: pip install webdriver-manager")
        return False
    
    try:
        import psutil
        print("✅ psutil可用")
    except ImportError:
        print("❌ psutil未安装")
        print("请运行: pip install psutil")
        return False
    
    # 检查Chrome浏览器
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument("--headless")  # 无头模式测试
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        driver_path = ChromeDriverManager().install()
        print(f"✅ ChromeDriver路径: {driver_path}")
        
        driver = webdriver.Chrome(
            service=webdriver.chrome.service.Service(driver_path),
            options=options
        )
        driver.get("about:blank")
        driver.quit()
        print("✅ Chrome浏览器可用")
        
    except Exception as e:
        print(f"❌ Chrome浏览器不可用: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("包装页面调试工具")
    print("=" * 50)
    
    try:
        # 检查依赖
        if not check_selenium_installation():
            print("\n❌ 依赖检查失败，请安装必要的组件")
            return
        
        # 调试包装页面
        debug_wrapper_page()
        
    except KeyboardInterrupt:
        print("\n用户中断调试")
    except Exception as e:
        print(f"❌ 调试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
