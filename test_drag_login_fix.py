#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽遮挡和登录功能修复
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def test_drag_and_login_fixes():
    """测试拖拽遮挡和登录功能修复"""
    print("=" * 60)
    print("拖拽遮挡和登录功能修复测试")
    print("=" * 60)
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取测试账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号")
        return
    
    account = accounts[0]
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return
    
    account, platform = account_platform
    
    print(f"✅ 测试账号: {account.account_name}")
    print(f"✅ 测试平台: {platform.platform_name}")
    print(f"✅ 登录URL: {platform.login_url}")
    
    try:
        print("\n启动浏览器进行修复验证...")
        
        # 启动浏览器
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        time.sleep(5)  # 等待页面加载
        
        if hasattr(cookie_service, 'driver') and cookie_service.driver:
            print("\n=== 问题1: 拖拽遮挡修复验证 ===")
            
            # 检查iframe容器的CSS属性
            iframe_container_style = cookie_service.driver.execute_script("""
                const container = document.querySelector('.iframe-container');
                if (container) {
                    const style = window.getComputedStyle(container);
                    return {
                        overflow: style.overflow,
                        position: style.position,
                        width: style.width,
                        height: style.height,
                        minWidth: style.minWidth,
                        minHeight: style.minHeight
                    };
                }
                return null;
            """)
            
            print("iframe容器样式检查:")
            if iframe_container_style:
                print(f"  overflow: {iframe_container_style['overflow']} (应该是 'auto' 而不是 'hidden')")
                print(f"  position: {iframe_container_style['position']}")
                print(f"  width: {iframe_container_style['width']}")
                print(f"  height: {iframe_container_style['height']}")
                
                if iframe_container_style['overflow'] == 'auto':
                    print("  ✅ overflow设置正确，允许滚动而不是隐藏内容")
                else:
                    print("  ❌ overflow设置不正确")
            
            # 检查iframe的属性
            iframe_info = cookie_service.driver.execute_script("""
                const iframe = document.getElementById('loginIframe');
                if (iframe) {
                    return {
                        src: iframe.src,
                        sandbox: iframe.getAttribute('sandbox'),
                        width: iframe.offsetWidth,
                        height: iframe.offsetHeight,
                        scrollWidth: iframe.scrollWidth,
                        scrollHeight: iframe.scrollHeight
                    };
                }
                return null;
            """)
            
            print("\niframe属性检查:")
            if iframe_info:
                print(f"  src: {iframe_info['src']}")
                print(f"  sandbox: {iframe_info['sandbox']}")
                print(f"  尺寸: {iframe_info['width']}x{iframe_info['height']}")
                print(f"  滚动尺寸: {iframe_info['scrollWidth']}x{iframe_info['scrollHeight']}")
                
                # 检查沙箱权限
                sandbox_attrs = iframe_info['sandbox'].split()
                required_attrs = [
                    'allow-same-origin',
                    'allow-scripts', 
                    'allow-forms',
                    'allow-storage-access-by-user-activation'
                ]
                
                print("\n  沙箱权限检查:")
                for attr in required_attrs:
                    if attr in sandbox_attrs:
                        print(f"    ✅ {attr}")
                    else:
                        print(f"    ❌ {attr} (缺失)")
            
            print("\n=== 问题2: 登录功能修复验证 ===")
            
            # 测试不同窗口大小下的显示
            test_sizes = [
                (1200, 800, "标准大小"),
                (900, 600, "中等大小"),
                (600, 400, "小窗口")
            ]
            
            for width, height, name in test_sizes:
                print(f"\n测试 {name} ({width}x{height}):")
                
                try:
                    # 设置窗口大小
                    cookie_service.driver.set_window_size(width, height)
                    time.sleep(1)
                    
                    # 检查内容是否被遮挡
                    visibility_check = cookie_service.driver.execute_script("""
                        const iframe = document.getElementById('loginIframe');
                        const container = document.querySelector('.iframe-container');
                        
                        if (iframe && container) {
                            const iframeRect = iframe.getBoundingClientRect();
                            const containerRect = container.getBoundingClientRect();
                            
                            return {
                                iframeVisible: iframeRect.width > 0 && iframeRect.height > 0,
                                iframeInContainer: iframeRect.left >= containerRect.left && 
                                                 iframeRect.top >= containerRect.top &&
                                                 iframeRect.right <= containerRect.right &&
                                                 iframeRect.bottom <= containerRect.bottom,
                                iframeRect: {
                                    width: iframeRect.width,
                                    height: iframeRect.height,
                                    left: iframeRect.left,
                                    top: iframeRect.top
                                },
                                containerRect: {
                                    width: containerRect.width,
                                    height: containerRect.height,
                                    left: containerRect.left,
                                    top: containerRect.top
                                }
                            };
                        }
                        return null;
                    """)
                    
                    if visibility_check:
                        if visibility_check['iframeVisible']:
                            print(f"  ✅ iframe可见")
                        else:
                            print(f"  ❌ iframe不可见")
                        
                        print(f"  iframe尺寸: {visibility_check['iframeRect']['width']}x{visibility_check['iframeRect']['height']}")
                        print(f"  容器尺寸: {visibility_check['containerRect']['width']}x{visibility_check['containerRect']['height']}")
                        
                        if visibility_check['iframeInContainer']:
                            print(f"  ✅ iframe完全在容器内")
                        else:
                            print(f"  ⚠️  iframe可能超出容器范围")
                    
                except Exception as e:
                    print(f"  ❌ {name} 测试失败: {e}")
            
            # 恢复标准大小
            cookie_service.driver.set_window_size(1200, 800)
            
            print("\n=== 手动测试指导 ===")
            print("请进行以下手动测试:")
            print("1. 拖拽浏览器窗口到屏幕右侧")
            print("2. 调整窗口大小到很小")
            print("3. 检查iframe内容是否完整显示")
            print("4. 检查是否有滚动条出现（正常）")
            print("5. 尝试在iframe中进行登录操作")
            print("6. 检查登录表单是否可以正常提交")
            print("7. 观察登录过程是否有错误")
            
            print("\n预期效果:")
            print("✅ 拖拽时iframe内容不被遮挡")
            print("✅ 小窗口时出现滚动条而不是隐藏内容")
            print("✅ 登录表单可以正常提交")
            print("✅ 登录过程无JavaScript错误")
            print("✅ 登录成功后可以获取Cookie")
            
            input("\n完成手动测试后，按回车键继续...")
            
            # 检查是否有JavaScript错误
            logs = cookie_service.driver.get_log('browser')
            if logs:
                print("\n浏览器控制台日志:")
                for log in logs[-10:]:  # 显示最后10条日志
                    level = log['level']
                    message = log['message']
                    if level in ['SEVERE', 'WARNING']:
                        print(f"  {level}: {message}")
            else:
                print("\n✅ 无严重的浏览器错误")
            
        else:
            print("❌ 浏览器未启动")
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        try:
            cookie_service.force_close_browser()
            print("✅ 浏览器已关闭")
        except:
            pass


def create_drag_test_html():
    """创建拖拽测试HTML文件"""
    test_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽遮挡测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f0f0f0; min-width: 320px; min-height: 500px; }
        .header { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; z-index: 9999; }
        .content { position: fixed; top: 60px; left: 0; right: 0; bottom: 0; background: white; overflow: auto; }
        .test-iframe { width: 100%; height: 100%; border: none; }
        .info { position: fixed; bottom: 20px; left: 20px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 4px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <div>拖拽遮挡测试 - 调整窗口大小观察效果</div>
        <button onclick="toggleInfo()" style="background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">切换信息</button>
    </div>
    
    <div class="content">
        <iframe class="test-iframe" src="https://customer.xiaohongshu.com/login?service=https://ark.xiaohongshu.com/ark"></iframe>
    </div>
    
    <div class="info" id="info">
        窗口大小: <span id="size"></span><br>
        overflow: auto (允许滚动)<br>
        拖拽测试: 正常
    </div>
    
    <script>
        function updateSize() {
            document.getElementById('size').textContent = window.innerWidth + 'x' + window.innerHeight;
        }
        
        function toggleInfo() {
            const info = document.getElementById('info');
            info.style.display = info.style.display === 'none' ? 'block' : 'none';
        }
        
        window.addEventListener('resize', updateSize);
        window.addEventListener('load', updateSize);
    </script>
</body>
</html>
    """
    
    test_file = "drag_test.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print(f"✅ 拖拽测试文件已创建: {test_file}")
    return test_file


def main():
    """主函数"""
    print("拖拽遮挡和登录功能修复测试工具")
    print("修复内容:")
    print("1. 修复右边拖拽后出现部分被遮挡的问题")
    print("2. 修复账号密码正常但无法登录成功的问题")
    
    try:
        # 创建独立测试文件
        create_drag_test_html()
        
        # 进行真实浏览器测试
        test_drag_and_login_fixes()
        
        print("\n" + "=" * 60)
        print("修复总结")
        print("=" * 60)
        print("问题1修复 - 拖拽遮挡:")
        print("✅ 将iframe容器的overflow从hidden改为auto")
        print("✅ 移除iframe的最小宽度限制")
        print("✅ 降低页面最小宽度要求")
        print("✅ 允许滚动而不是隐藏内容")
        
        print("\n问题2修复 - 登录功能:")
        print("✅ 添加allow-storage-access-by-user-activation权限")
        print("✅ 添加allow-downloads权限")
        print("✅ 扩展allow权限包含payment和encrypted-media")
        print("✅ 设置credentialless=false确保凭据传递")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
