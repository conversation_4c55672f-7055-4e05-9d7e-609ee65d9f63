#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试service参数修复
验证小红书千帆登录请求参数修复是否有效
"""

import sys
import os
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.shop_name_service import ShopNameService
from src.services.cookie_validator import CookieValidator
from src.services.account_service import AccountService


def test_service_param_fix():
    """测试service参数修复"""
    print("=" * 60)
    print("Service参数修复测试")
    print("=" * 60)
    
    # 测试URL和参数
    test_urls = [
        "https://ark.xiaohongshu.com/api/edith/seller/detail",
        "https://ark.xiaohongshu.com/api/user/profile"
    ]
    
    # 正确的service参数
    correct_service = "https%3A%2F%2Fark.xiaohongshu.com%2Fapp-seller%2FsellerInfo%3Ffrom%3Dark-login"
    # 错误的service参数（之前使用的）
    wrong_service = "https%3A%2F%2Fark.xiaohongshu.com%2Fark"
    
    print(f"✅ 正确的service参数: {correct_service}")
    print(f"❌ 错误的service参数: {wrong_service}")
    print()
    
    # 模拟请求头
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    for url in test_urls:
        print(f"测试接口: {url}")
        print("-" * 40)
        
        # 测试正确的service参数
        try:
            params_correct = {"service": correct_service}
            response = requests.get(url, headers=headers, params=params_correct, timeout=10)
            print(f"  ✅ 正确参数 - 状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"     响应内容: {response.text[:200]}...")
        except Exception as e:
            print(f"  ✅ 正确参数 - 请求失败: {e}")
        
        # 测试错误的service参数
        try:
            params_wrong = {"service": wrong_service}
            response = requests.get(url, headers=headers, params=params_wrong, timeout=10)
            print(f"  ❌ 错误参数 - 状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"     响应内容: {response.text[:200]}...")
        except Exception as e:
            print(f"  ❌ 错误参数 - 请求失败: {e}")
        
        print()


def test_shop_name_service_fix():
    """测试店铺名称服务修复"""
    print("=" * 60)
    print("店铺名称服务修复测试")
    print("=" * 60)
    
    # 初始化服务
    data_manager = DataManager()
    account_service = AccountService(data_manager)
    shop_name_service = ShopNameService()
    
    # 获取小红书千帆账号
    accounts = account_service.get_all_accounts()
    qianfan_account = None
    
    for account in accounts:
        account_platform = account_service.get_account_with_platform(account.account_id)
        if account_platform:
            account_obj, platform = account_platform
            if "千帆" in platform.platform_name:
                qianfan_account = account_obj
                qianfan_platform = platform
                break
    
    if not qianfan_account:
        print("❌ 没有找到小红书千帆账号")
        return
    
    print(f"✅ 找到千帆账号: {qianfan_account.account_name}")
    print(f"✅ 平台名称: {qianfan_platform.platform_name}")
    print(f"✅ 店铺名称API: {qianfan_platform.shop_name_api_url}")
    print()
    
    # 模拟Cookie（实际使用时需要真实Cookie）
    mock_cookie = "sessionid=test; token=test"
    
    # 测试店铺名称获取
    print("测试店铺名称获取...")
    try:
        shop_name = shop_name_service.get_shop_name(
            qianfan_platform.shop_name_api_url,
            qianfan_platform.shop_name_field,
            mock_cookie
        )
        
        if shop_name:
            print(f"✅ 成功获取店铺名称: {shop_name}")
        else:
            print("❌ 未能获取店铺名称（这是预期的，因为使用的是模拟Cookie）")
    except Exception as e:
        print(f"❌ 获取店铺名称时出错: {e}")


def test_cookie_validator_fix():
    """测试Cookie验证器修复"""
    print("=" * 60)
    print("Cookie验证器修复测试")
    print("=" * 60)
    
    # 初始化验证器
    data_manager = DataManager()
    validator = CookieValidator(data_manager)
    
    # 测试URL
    test_url = "https://ark.xiaohongshu.com/api/user/profile"
    
    print(f"测试URL: {test_url}")
    print()
    
    # 模拟Cookie
    mock_cookie = "sessionid=test; token=test"
    
    # 测试验证（需要先创建一个测试账号）
    print("测试Cookie验证...")
    try:
        # 创建一个临时测试账号ID
        test_account_id = "test_account_for_validation"

        # 保存测试Cookie
        data_manager.save_cookie(test_account_id, mock_cookie)

        # 验证Cookie
        is_valid, message = validator.validate_cookie(test_account_id)
        print(f"验证结果: {is_valid}")
        print(f"验证消息: {message}")

        # 清理测试数据
        data_manager.delete_cookie(test_account_id)
    except Exception as e:
        print(f"❌ 验证时出错: {e}")


def test_platform_config_fix():
    """测试平台配置修复"""
    print("=" * 60)
    print("平台配置修复测试")
    print("=" * 60)
    
    # 初始化数据管理器
    data_manager = DataManager()
    platforms = data_manager.get_platforms()
    
    # 检查小红书千帆配置
    if "xiaohongshu_qianfan" in platforms:
        qianfan_config = platforms["xiaohongshu_qianfan"]
        login_url = qianfan_config.get("login_url", "")
        
        print(f"✅ 找到千帆平台配置")
        print(f"登录URL: {login_url}")
        
        # 检查service参数
        if "service=" in login_url:
            service_param = login_url.split("service=")[1]
            print(f"Service参数: {service_param}")
            
            # 检查是否使用了正确的参数
            correct_service = "https://ark.xiaohongshu.com/app-seller/sellerInfo?from=ark-login"
            if correct_service in service_param:
                print("✅ 使用了正确的service参数")
            else:
                print("❌ 仍在使用错误的service参数")
        else:
            print("❌ 登录URL中没有service参数")
    else:
        print("❌ 没有找到小红书千帆平台配置")


if __name__ == "__main__":
    print("开始测试service参数修复...")
    print()
    
    # 运行所有测试
    test_service_param_fix()
    test_platform_config_fix()
    test_shop_name_service_fix()
    test_cookie_validator_fix()
    
    print("=" * 60)
    print("测试完成")
    print("=" * 60)
