#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cookie检测功能
验证自定义页面中的cookie检测是否正常工作
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.cookie_service import CookieService
from src.utils.data_manager import DataManager
from src.services.account_service import AccountService


def test_cookie_detection():
    """测试cookie检测功能"""
    print("=" * 60)
    print("Cookie检测功能测试")
    print("=" * 60)
    
    # 初始化服务
    data_manager = DataManager()
    account_service = AccountService(data_manager)
    cookie_service = CookieService(data_manager, account_service)
    
    # 获取小红书千帆账号进行测试
    accounts = account_service.get_all_accounts()
    test_account = None
    
    for account in accounts:
        account_platform = account_service.get_account_with_platform(account.account_id)
        if account_platform:
            account_obj, platform = account_platform
            if "千帆" in platform.platform_name:
                test_account = account_obj
                break
    
    if not test_account:
        print("❌ 没有找到小红书千帆账号用于测试")
        print("请先添加一个小红书千帆账号")
        return
    
    print(f"✅ 找到测试账号: {test_account.account_name}")
    print(f"账号ID: {test_account.account_id}")
    print()
    
    # 测试cookie检测功能
    print("开始测试cookie检测...")
    print("注意: 这将打开浏览器窗口，请在登录完成后点击'我已完成登录'按钮")
    print()
    
    try:
        # 启动cookie获取流程
        def test_callback(success, message):
            print(f"回调结果: {'成功' if success else '失败'} - {message}")
        
        # 使用自定义页面获取cookie
        cookie_service.get_cookie_with_custom_page(
            test_account.account_id,
            callback=test_callback
        )
        
        print("✅ Cookie检测测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    
    finally:
        # 清理资源
        try:
            cookie_service.cleanup()
        except:
            pass


def test_cookie_parsing():
    """测试cookie解析功能"""
    print("=" * 60)
    print("Cookie解析功能测试")
    print("=" * 60)
    
    # 初始化服务
    data_manager = DataManager()
    account_service = AccountService(data_manager)
    cookie_service = CookieService(data_manager, account_service)
    
    # 测试cookie字符串解析
    test_cookie_strings = [
        "sessionid=abc123; token=def456; user_id=789",
        "auth_token=xyz789; login_time=**********",
        "session=test; csrf_token=abcdef; remember_me=true"
    ]
    
    for i, cookie_str in enumerate(test_cookie_strings, 1):
        print(f"测试Cookie字符串 {i}: {cookie_str}")
        
        try:
            parsed_cookies = cookie_service._parse_cookie_string(cookie_str)
            print(f"  解析结果: {len(parsed_cookies)} 个cookie")
            
            for cookie in parsed_cookies:
                print(f"    - {cookie['name']} = {cookie['value']}")
            
        except Exception as e:
            print(f"  ❌ 解析失败: {e}")
        
        print()


def test_cookie_merging():
    """测试cookie合并功能"""
    print("=" * 60)
    print("Cookie合并功能测试")
    print("=" * 60)
    
    # 初始化服务
    data_manager = DataManager()
    account_service = AccountService(data_manager)
    cookie_service = CookieService(data_manager, account_service)
    
    # 模拟不同来源的cookie
    js_cookies = {
        'all': 'js_session=abc123; js_token=def456',
        'auth': ['js_session=abc123'],
        'timestamp': '2024-01-15T10:00:00Z'
    }
    
    iframe_cookies = [
        {'name': 'iframe_session', 'value': 'xyz789', 'domain': 'example.com', 'path': '/', 'secure': False, 'httpOnly': True},
        {'name': 'iframe_token', 'value': 'token123', 'domain': 'example.com', 'path': '/', 'secure': True, 'httpOnly': False}
    ]
    
    main_cookies = [
        {'name': 'main_session', 'value': 'main123', 'domain': 'example.com', 'path': '/', 'secure': False, 'httpOnly': False},
        {'name': 'js_session', 'value': 'duplicate', 'domain': 'example.com', 'path': '/', 'secure': False, 'httpOnly': False}  # 重复的cookie
    ]
    
    print("JavaScript检测的cookie:")
    print(f"  {js_cookies}")
    print()
    
    print("iframe中的cookie:")
    for cookie in iframe_cookies:
        print(f"  {cookie['name']} = {cookie['value']}")
    print()
    
    print("主页面的cookie:")
    for cookie in main_cookies:
        print(f"  {cookie['name']} = {cookie['value']}")
    print()
    
    try:
        # 测试合并功能
        merged_cookies = cookie_service._merge_all_cookies(js_cookies, iframe_cookies, main_cookies)
        
        print("合并后的cookie:")
        for cookie in merged_cookies:
            print(f"  {cookie['name']} = {cookie['value']}")
        
        print(f"\n✅ 成功合并 {len(merged_cookies)} 个cookie")
        
    except Exception as e:
        print(f"❌ 合并失败: {e}")


def test_cookie_filtering():
    """测试cookie过滤功能"""
    print("=" * 60)
    print("Cookie过滤功能测试")
    print("=" * 60)
    
    # 初始化服务
    data_manager = DataManager()
    account_service = AccountService(data_manager)
    cookie_service = CookieService(data_manager, account_service)
    
    # 模拟各种类型的cookie
    test_cookies = [
        {'name': 'sessionid', 'value': 'session123', 'domain': 'xiaohongshu.com'},
        {'name': 'auth_token', 'value': 'auth456', 'domain': 'xiaohongshu.com'},
        {'name': 'user_id', 'value': '789', 'domain': 'xiaohongshu.com'},
        {'name': 'csrf_token', 'value': 'csrf123', 'domain': 'xiaohongshu.com'},
        {'name': 'analytics_id', 'value': 'analytics456', 'domain': 'xiaohongshu.com'},
        {'name': 'preferences', 'value': 'pref789', 'domain': 'xiaohongshu.com'},
        {'name': 'login_time', 'value': '**********', 'domain': 'xiaohongshu.com'}
    ]
    
    print("原始cookie列表:")
    for cookie in test_cookies:
        print(f"  {cookie['name']} = {cookie['value']}")
    print()
    
    try:
        # 测试过滤功能
        auth_cookies = cookie_service._filter_auth_cookies(test_cookies)
        
        print("过滤后的认证cookie:")
        for cookie in auth_cookies:
            print(f"  {cookie['name']} = {cookie['value']}")
        
        print(f"\n✅ 从 {len(test_cookies)} 个cookie中过滤出 {len(auth_cookies)} 个认证cookie")
        
    except Exception as e:
        print(f"❌ 过滤失败: {e}")


if __name__ == "__main__":
    print("开始测试Cookie检测功能...")
    print()
    
    # 运行所有测试
    test_cookie_parsing()
    test_cookie_merging()
    test_cookie_filtering()
    
    # 询问是否进行实际的浏览器测试
    user_input = input("是否进行实际的浏览器Cookie检测测试？(y/N): ").strip().lower()
    if user_input in ['y', 'yes']:
        test_cookie_detection()
    else:
        print("跳过浏览器测试")
    
    print("=" * 60)
    print("测试完成")
    print("=" * 60)
    print()
    print("总结:")
    print("1. ✅ 已实现JavaScript cookie检测功能")
    print("2. ✅ 已实现多来源cookie合并功能")
    print("3. ✅ 已实现cookie解析和过滤功能")
    print("4. ✅ 已支持iframe内嵌网页跳转")
    print("5. 🔄 实际登录测试需要在浏览器中手动完成")
