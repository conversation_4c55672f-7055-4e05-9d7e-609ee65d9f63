#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试iframe加载问题
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def debug_iframe_loading():
    """调试iframe加载问题"""
    print("=" * 50)
    print("调试iframe加载问题")
    print("=" * 50)
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取测试账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号")
        return
    
    account = accounts[0]
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return
    
    account, platform = account_platform
    
    print(f"✅ 测试账号: {account.account_name}")
    print(f"✅ 测试平台: {platform.platform_name}")
    print(f"✅ 登录URL: {platform.login_url}")
    
    try:
        print("\n启动浏览器...")
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        time.sleep(5)  # 等待页面加载
        
        if hasattr(cookie_service, 'driver') and cookie_service.driver:
            print("\n检查页面状态...")
            
            # 检查当前URL
            current_url = cookie_service.driver.current_url
            print(f"当前页面URL: {current_url}")
            
            # 检查页面标题
            title = cookie_service.driver.title
            print(f"页面标题: {title}")
            
            # 检查iframe元素
            try:
                iframe = cookie_service.driver.find_element("css selector", ".login-iframe")
                iframe_src = iframe.get_attribute('src')
                print(f"iframe src属性: {iframe_src}")
                
                # 检查iframe的其他属性
                sandbox = iframe.get_attribute('sandbox')
                print(f"iframe sandbox: {sandbox}")
                
            except Exception as e:
                print(f"❌ 检查iframe失败: {e}")
            
            # 执行JavaScript来检查页面状态
            try:
                js_result = cookie_service.driver.execute_script("""
                    console.log('=== JavaScript调试信息 ===');
                    console.log('window.loginUrl:', window.loginUrl);
                    console.log('window.accountId:', window.accountId);
                    
                    const iframe = document.getElementById('loginIframe');
                    if (iframe) {
                        console.log('iframe.src:', iframe.src);
                        console.log('iframe元素存在');
                        return {
                            loginUrl: window.loginUrl,
                            accountId: window.accountId,
                            iframeSrc: iframe.src,
                            iframeExists: true
                        };
                    } else {
                        console.log('iframe元素不存在');
                        return {
                            loginUrl: window.loginUrl,
                            accountId: window.accountId,
                            iframeSrc: null,
                            iframeExists: false
                        };
                    }
                """)
                
                print(f"\nJavaScript执行结果:")
                print(f"  window.loginUrl: {js_result.get('loginUrl')}")
                print(f"  window.accountId: {js_result.get('accountId')}")
                print(f"  iframe.src: {js_result.get('iframeSrc')}")
                print(f"  iframe存在: {js_result.get('iframeExists')}")
                
                # 如果iframe的src不正确，尝试手动设置
                if js_result.get('iframeSrc') == 'about:blank' and js_result.get('loginUrl'):
                    print("\n尝试手动设置iframe src...")
                    cookie_service.driver.execute_script(f"""
                        const iframe = document.getElementById('loginIframe');
                        if (iframe) {{
                            console.log('手动设置iframe src为:', '{platform.login_url}');
                            iframe.src = '{platform.login_url}';
                            console.log('设置后iframe.src:', iframe.src);
                        }}
                    """)
                    
                    time.sleep(3)  # 等待加载
                    
                    # 再次检查
                    iframe = cookie_service.driver.find_element("css selector", ".login-iframe")
                    new_src = iframe.get_attribute('src')
                    print(f"手动设置后iframe src: {new_src}")
                
            except Exception as e:
                print(f"❌ JavaScript执行失败: {e}")
            
            print("\n请在浏览器中检查:")
            print("1. iframe是否显示目标登录页面")
            print("2. 浏览器控制台是否有错误信息")
            print("3. 网络面板是否有加载失败的请求")
            
            input("检查完成后按回车键继续...")
            
        else:
            print("❌ 浏览器未启动")
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        try:
            cookie_service.force_close_browser()
            print("✅ 浏览器已关闭")
        except:
            pass


def main():
    """主函数"""
    try:
        debug_iframe_loading()
    except KeyboardInterrupt:
        print("\n用户中断调试")
    except Exception as e:
        print(f"❌ 调试出错: {e}")


if __name__ == "__main__":
    main()
