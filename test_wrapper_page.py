#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试包装页面方案
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def test_wrapper_page_creation():
    """测试包装页面创建"""
    print("=== 测试包装页面创建 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    
    # 测试参数
    account_id = "test-account-123"
    login_url = "https://example.com/login"
    
    # 创建包装页面
    wrapper_html = cookie_service._create_login_wrapper_page(account_id, login_url)
    
    print(f"包装页面HTML长度: {len(wrapper_html)} 字符")
    
    # 检查关键内容
    checks = [
        ("账号ID替换", account_id in wrapper_html),
        ("登录URL替换", login_url in wrapper_html),
        ("按钮元素", "我已完成登录" in wrapper_html),
        ("iframe元素", "login-iframe" in wrapper_html),
        ("JavaScript函数", "checkLogin" in wrapper_html),
        ("CSS样式", "header-container" in wrapper_html)
    ]
    
    for check_name, result in checks:
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {'通过' if result else '失败'}")
    
    # 保存测试文件
    test_file = "test_wrapper_output.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(wrapper_html)
    
    print(f"✅ 包装页面已保存到: {test_file}")
    print("可以在浏览器中打开查看效果")


def test_wrapper_page_functionality():
    """测试包装页面功能"""
    print("\n=== 测试包装页面功能 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("没有可用的账号进行测试")
        return
    
    account = accounts[0]
    print(f"使用账号: {account.account_name}")
    
    # 获取平台信息
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("账号没有关联的平台")
        return
    
    account, platform = account_platform
    print(f"平台: {platform.platform_name}")
    print(f"登录URL: {platform.login_url}")
    
    try:
        print("\n开始测试包装页面功能...")
        print("将打开包装页面，请按以下步骤测试:")
        print()
        print("🎯 包装页面功能测试:")
        print("1. 观察页面布局：顶部应该有蓝色渐变背景的按钮区域")
        print("2. 按钮位置：右上角应该有'我已完成登录'按钮")
        print("3. iframe内容：下方应该显示目标登录页面")
        print("4. 响应式设计：调整窗口大小，布局应该保持正常")
        print()
        print("🔧 交互功能测试:")
        print("5. 点击按钮：应该弹出确认对话框")
        print("6. 确认对话框：点击'取消'按钮应该保持原状")
        print("7. 确认对话框：点击'确定'按钮应该变为'正在检查登录状态...'")
        print("8. 状态指示：左侧应该有状态指示灯")
        print()
        print("🌐 iframe功能测试:")
        print("9. 页面加载：iframe中的登录页面应该正常加载")
        print("10. 页面交互：可以在iframe中正常操作（填写表单等）")
        print("11. 页面跳转：iframe中的页面跳转应该正常工作")
        print("12. Cookie获取：完成登录后点击按钮应该能检测到Cookie")
        print()
        print("🎨 视觉效果测试:")
        print("13. 加载动画：页面加载时应该显示加载动画")
        print("14. 按钮悬停：鼠标悬停按钮应该有颜色变化")
        print("15. 状态变化：按钮状态变化应该有平滑过渡")
        print("16. 提示面板：右上角应该有操作提示面板")
        print()
        
        # 启动Cookie获取
        cookie_service.open_incognito_browser(
            platform.login_url, 
            account.account_id, 
            None  # 不需要回调
        )
        
        # 等待用户测试
        print("等待用户测试...")
        print("按 Ctrl+C 结束测试")
        
        start_time = time.time()
        
        while True:
            try:
                # 检查登录结果
                result = cookie_service.get_login_result(account.account_id)
                if result is not None:
                    success, message = result
                    print(f"\n登录结果: {'成功' if success else '失败'} - {message}")
                    
                    if success:
                        print("✅ 包装页面功能测试成功！")
                        print("✅ iframe Cookie获取正常工作！")
                        print("✅ 用户交互流程完整！")
                        break
                    else:
                        print(f"❌ 登录失败: {message}")
                        break
                
                # 每30秒提示一次
                if int(time.time() - start_time) % 30 == 0:
                    print(".", end="", flush=True)
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                print("\n用户中断测试")
                break
        
        # 清理
        cookie_service.force_close_browser()
        print("\n测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_wrapper_advantages():
    """展示包装页面方案的优势"""
    print("\n=== 包装页面方案优势 ===")
    print()
    print("🎯 技术优势:")
    print("1. 完全控制：按钮在我们的页面上，不受目标网站影响")
    print("2. 稳定可靠：无需复杂的DOM监听和重建逻辑")
    print("3. 简化代码：不需要强化的按钮持久性机制")
    print("4. 跨域安全：iframe提供安全的页面隔离")
    print()
    print("💡 用户体验优势:")
    print("1. 按钮永远存在：不会因为页面跳转而消失")
    print("2. 位置固定：用户总是知道按钮在哪里")
    print("3. 状态清晰：顶部区域显示当前状态")
    print("4. 操作引导：提供清晰的操作提示")
    print()
    print("🔧 开发优势:")
    print("1. 代码简化：移除复杂的监听机制")
    print("2. 维护容易：逻辑集中在包装页面")
    print("3. 扩展性好：可以轻松添加更多功能")
    print("4. 调试方便：问题定位更加容易")
    print()
    print("🛡️ 安全优势:")
    print("1. 隔离保护：iframe提供安全隔离")
    print("2. Cookie安全：只在需要时访问Cookie")
    print("3. 权限控制：明确的权限边界")
    print("4. 错误隔离：iframe错误不影响主页面")


def main():
    """主函数"""
    print("包装页面方案测试工具")
    print("=" * 50)
    
    try:
        test_wrapper_advantages()
        test_wrapper_page_creation()
        test_wrapper_page_functionality()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
