
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强化防重定向测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; height: 100vh; overflow: hidden; }
        .header-container { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 9999; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; }
        .header-title { color: white; font-size: 16px; font-weight: 600; }
        .test-buttons { display: flex; gap: 10px; }
        .test-button { background: #52c41a; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; }
        .test-button:hover { background: #45a017; }
        .iframe-container { position: absolute; top: 60px; left: 0; right: 0; bottom: 0; background: white; }
        .login-iframe { width: 100%; height: 100%; border: none; background: white; }
        .test-info { position: fixed; top: 70px; left: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 400px; z-index: 9998; }
        .log-area { position: fixed; bottom: 20px; left: 20px; right: 20px; height: 150px; background: #000; color: #0f0; font-family: monospace; font-size: 12px; padding: 10px; border-radius: 4px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="header-container">
        <div class="header-title">强化防重定向测试</div>
        <div class="test-buttons">
            <button class="test-button" onclick="testLocationHref()">location.href</button>
            <button class="test-button" onclick="testLocationAssign()">location.assign</button>
            <button class="test-button" onclick="testLocationReplace()">location.replace</button>
            <button class="test-button" onclick="testHistoryPush()">history.push</button>
            <button class="test-button" onclick="testTopLocation()">top.location</button>
            <button class="test-button" onclick="testParentLocation()">parent.location</button>
            <button class="test-button" onclick="clearLog()">清除日志</button>
        </div>
    </div>
    
    <div class="test-info">
        <h3>测试说明</h3>
        <p>点击上方按钮测试各种重定向方式，所有重定向都应该被阻止。</p>
        <p>查看底部日志区域了解拦截情况。</p>
        <p><strong>如果页面跳转了，说明防护失败！</strong></p>
    </div>
    
    <div class="iframe-container">
        <iframe class="login-iframe" src="data:text/html,<h1>这是iframe内容</h1><script>console.log('iframe加载完成');</script>" sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals"></iframe>
    </div>
    
    <div class="log-area" id="logArea">
        <div>防重定向测试日志：</div>
    </div>

    <script>
        window.userClosing = false;
        
        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div>[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '<div>防重定向测试日志：</div>';
        }
        
        // 强化的防重定向机制
        function preventRedirect() {
            log('启动强化防重定向机制');
            
            const originalLocation = window.location;
            const originalHref = originalLocation.href;
            
            // 1. 防止beforeunload跳转
            window.addEventListener('beforeunload', function(e) {
                if (!window.userClosing) {
                    log('阻止beforeunload跳转');
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }
            });
            
            // 2. 阻止历史记录变化
            window.addEventListener('popstate', function(e) {
                log('阻止popstate变化');
                e.preventDefault();
                e.stopPropagation();
                history.pushState(null, '', originalHref);
            });
            
            // 3. 重写location对象的所有属性
            Object.defineProperty(window, 'location', {
                get: function() {
                    return {
                        href: originalHref,
                        protocol: originalLocation.protocol,
                        host: originalLocation.host,
                        hostname: originalLocation.hostname,
                        port: originalLocation.port,
                        pathname: originalLocation.pathname,
                        search: originalLocation.search,
                        hash: originalLocation.hash,
                        origin: originalLocation.origin,
                        assign: function(url) {
                            log('阻止location.assign重定向到: ' + url);
                        },
                        replace: function(url) {
                            log('阻止location.replace重定向到: ' + url);
                        },
                        reload: function() {
                            log('阻止location.reload');
                        },
                        toString: function() {
                            return originalHref;
                        }
                    };
                },
                set: function(value) {
                    log('阻止location设置重定向到: ' + value);
                }
            });
            
            // 4. 重写top和parent的location
            try {
                Object.defineProperty(window, 'top', {
                    get: function() {
                        return {
                            location: window.location,
                            document: window.document,
                            window: window
                        };
                    }
                });
                
                Object.defineProperty(window, 'parent', {
                    get: function() {
                        return {
                            location: window.location,
                            document: window.document,
                            window: window
                        };
                    }
                });
                log('成功重写top和parent对象');
            } catch (e) {
                log('无法重写top/parent对象: ' + e.message);
            }
            
            // 5. 重写history对象
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;
            
            history.pushState = function(state, title, url) {
                if (url && url !== originalHref && !url.startsWith('#')) {
                    log('阻止history.pushState重定向到: ' + url);
                    return;
                }
                return originalPushState.call(this, state, title, url);
            };
            
            history.replaceState = function(state, title, url) {
                if (url && url !== originalHref && !url.startsWith('#')) {
                    log('阻止history.replaceState重定向到: ' + url);
                    return;
                }
                return originalReplaceState.call(this, state, title, url);
            };
            
            // 6. 定期检查URL是否被修改
            setInterval(function() {
                if (window.location.href !== originalHref) {
                    log('检测到URL被修改，强制恢复');
                    try {
                        history.replaceState(null, '', originalHref);
                    } catch (e) {
                        log('无法恢复URL: ' + e.message);
                    }
                }
            }, 1000);
            
            log('防重定向机制已启用，原始URL: ' + originalHref);
        }
        
        // 测试函数
        function testLocationHref() {
            log('测试 window.location.href 重定向');
            try {
                window.location.href = 'https://www.baidu.com';
            } catch (e) {
                log('location.href 重定向被阻止: ' + e.message);
            }
        }
        
        function testLocationAssign() {
            log('测试 window.location.assign 重定向');
            try {
                window.location.assign('https://www.baidu.com');
            } catch (e) {
                log('location.assign 重定向被阻止: ' + e.message);
            }
        }
        
        function testLocationReplace() {
            log('测试 window.location.replace 重定向');
            try {
                window.location.replace('https://www.baidu.com');
            } catch (e) {
                log('location.replace 重定向被阻止: ' + e.message);
            }
        }
        
        function testHistoryPush() {
            log('测试 history.pushState 重定向');
            try {
                history.pushState(null, '', 'https://www.baidu.com');
            } catch (e) {
                log('history.pushState 重定向被阻止: ' + e.message);
            }
        }
        
        function testTopLocation() {
            log('测试 top.location 重定向');
            try {
                top.location.href = 'https://www.baidu.com';
            } catch (e) {
                log('top.location 重定向被阻止: ' + e.message);
            }
        }
        
        function testParentLocation() {
            log('测试 parent.location 重定向');
            try {
                parent.location.href = 'https://www.baidu.com';
            } catch (e) {
                log('parent.location 重定向被阻止: ' + e.message);
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            preventRedirect();
            log('页面加载完成，防重定向机制已启用');
        });
    </script>
</body>
</html>
    