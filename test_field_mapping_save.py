#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射保存功能测试脚本
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.platform_service import PlatformService


def test_save_platforms_method():
    """测试保存平台配置方法"""
    print("🔍 测试保存平台配置方法...")
    
    try:
        data_manager = DataManager()
        data_manager.init_data_files()
        
        # 获取当前平台数据
        platforms_data = data_manager.get_platforms()
        print(f"✅ 获取平台数据成功，共 {len(platforms_data)} 个平台")
        
        # 测试保存方法
        data_manager.save_platforms(platforms_data)
        print("✅ 保存平台数据成功")
        
        # 验证保存后能正确读取
        reloaded_data = data_manager.get_platforms()
        assert len(reloaded_data) == len(platforms_data), "保存后数据数量不匹配"
        print("✅ 保存后数据验证成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存平台配置方法测试失败: {e}")
        return False


def test_field_mapping_save():
    """测试字段映射保存功能"""
    print("\n🔍 测试字段映射保存功能...")
    
    try:
        data_manager = DataManager()
        data_manager.init_data_files()
        platform_service = PlatformService(data_manager)
        
        # 获取第一个平台进行测试
        platforms = platform_service.get_all_platforms()
        if not platforms:
            print("⚠️  没有可用的平台进行测试")
            return True
        
        test_platform = platforms[0]
        platform_id = test_platform.platform_id
        
        print(f"✅ 使用平台进行测试: {test_platform.platform_name}")
        
        # 创建测试字段映射
        test_field_mappings = [
            {
                "field_name": "测试标题",
                "json_path": "$.data.title",
                "data_type": "文本",
                "default_value": ""
            },
            {
                "field_name": "测试数量",
                "json_path": "$.data.count",
                "data_type": "数字",
                "default_value": "0"
            }
        ]
        
        # 获取平台数据并添加字段映射
        platforms_data = data_manager.get_platforms()
        platforms_data[platform_id]["field_mappings"] = test_field_mappings
        
        # 保存配置
        data_manager.save_platforms(platforms_data)
        print("✅ 字段映射配置保存成功")
        
        # 验证保存结果
        reloaded_data = data_manager.get_platforms()
        saved_mappings = reloaded_data[platform_id].get("field_mappings", [])
        
        assert len(saved_mappings) == 2, f"字段映射数量不匹配: {len(saved_mappings)} != 2"
        assert saved_mappings[0]["field_name"] == "测试标题", "第一个字段名称不匹配"
        assert saved_mappings[1]["field_name"] == "测试数量", "第二个字段名称不匹配"
        
        print("✅ 字段映射配置验证成功")
        
        # 通过PlatformService验证
        updated_platform = platform_service.get_platform_by_id(platform_id)
        assert updated_platform.field_mappings is not None, "Platform对象中字段映射为空"
        assert len(updated_platform.field_mappings) == 2, "Platform对象中字段映射数量不匹配"
        
        print("✅ PlatformService读取字段映射成功")
        
        # 清理测试数据
        platforms_data[platform_id].pop("field_mappings", None)
        data_manager.save_platforms(platforms_data)
        print("✅ 测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 字段映射保存功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_platform_service_update():
    """测试平台服务更新功能"""
    print("\n🔍 测试平台服务更新功能...")
    
    try:
        data_manager = DataManager()
        data_manager.init_data_files()
        platform_service = PlatformService(data_manager)
        
        # 获取第一个平台进行测试
        platforms = platform_service.get_all_platforms()
        if not platforms:
            print("⚠️  没有可用的平台进行测试")
            return True
        
        test_platform = platforms[0]
        platform_id = test_platform.platform_id
        
        print(f"✅ 使用平台进行测试: {test_platform.platform_name}")
        
        # 测试update_platform方法
        success = platform_service.update_platform(
            platform_id,
            test_platform.platform_name,
            test_platform.login_url,
            test_platform.data_api_url,
            test_platform.extract_rule
        )
        
        assert success, "平台更新失败"
        print("✅ 平台服务更新功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 平台服务更新功能测试失败: {e}")
        return False


def test_platform_to_dict():
    """测试Platform对象的to_dict方法"""
    print("\n🔍 测试Platform对象的to_dict方法...")
    
    try:
        from src.models.platform import Platform
        
        # 创建测试Platform对象
        test_mappings = [
            {
                "field_name": "标题",
                "json_path": "$.title",
                "data_type": "文本",
                "default_value": ""
            }
        ]
        
        platform = Platform(
            platform_id="test_id",
            platform_name="测试平台",
            login_url="https://test.com/login",
            data_api_url="https://test.com/api",
            extract_rule="$.data",
            field_mappings=test_mappings
        )
        
        # 测试to_dict方法
        platform_dict = platform.to_dict()
        
        assert "field_mappings" in platform_dict, "to_dict结果中缺少field_mappings"
        assert len(platform_dict["field_mappings"]) == 1, "field_mappings数量不正确"
        assert platform_dict["field_mappings"][0]["field_name"] == "标题", "字段名称不匹配"
        
        print("✅ Platform.to_dict方法测试通过")
        
        # 测试from_dict方法
        recreated_platform = Platform.from_dict("test_id", platform_dict)
        
        assert recreated_platform.field_mappings is not None, "from_dict后field_mappings为空"
        assert len(recreated_platform.field_mappings) == 1, "from_dict后field_mappings数量不正确"
        
        print("✅ Platform.from_dict方法测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ Platform对象方法测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 字段映射保存功能修复验证")
    print("=" * 50)
    
    tests = [
        ("保存平台配置方法", test_save_platforms_method),
        ("字段映射保存功能", test_field_mapping_save),
        ("平台服务更新功能", test_platform_service_update),
        ("Platform对象方法", test_platform_to_dict),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有字段映射保存功能测试通过！")
        print("\n✨ 修复成果:")
        print("  ✅ 修复了update_platform参数不匹配问题")
        print("  ✅ 添加了save_platforms方法")
        print("  ✅ 实现了字段映射配置的正确保存")
        print("  ✅ Platform对象序列化和反序列化正常")
    else:
        print("⚠️  部分功能存在问题，请检查相关代码")
    
    return failed == 0


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
