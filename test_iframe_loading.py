#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试iframe加载问题修复
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def create_iframe_test_page():
    """创建iframe加载测试页面"""
    test_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe加载测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif; background: #f5f5f5; height: 100vh; overflow: hidden; }
        .header-container { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 9999; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; }
        .header-title { color: white; font-size: 16px; font-weight: 600; }
        .test-buttons { display: flex; gap: 10px; }
        .test-button { background: #52c41a; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; }
        .test-button:hover { background: #45a017; }
        .iframe-container { position: absolute; top: 60px; left: 0; right: 0; bottom: 0; background: white; }
        .login-iframe { width: 100%; height: 100%; border: none; background: white; }
        .loading-overlay { position: absolute; top: 60px; left: 0; right: 0; bottom: 0; background: rgba(255, 255, 255, 0.9); display: flex; align-items: center; justify-content: center; z-index: 1000; }
        .loading-spinner { width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #1890ff; border-radius: 50%; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .loading-text { margin-left: 15px; color: #666; font-size: 14px; }
        .log-area { position: fixed; bottom: 20px; left: 20px; right: 20px; height: 150px; background: #000; color: #0f0; font-family: monospace; font-size: 12px; padding: 10px; border-radius: 4px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="header-container">
        <div class="header-title">iframe加载测试</div>
        <div class="test-buttons">
            <button class="test-button" onclick="loadBaidu()">加载百度</button>
            <button class="test-button" onclick="loadGoogle()">加载Google</button>
            <button class="test-button" onclick="loadXiaohongshu()">加载小红书</button>
            <button class="test-button" onclick="clearLog()">清除日志</button>
        </div>
    </div>
    
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载页面...</div>
    </div>
    
    <div class="iframe-container">
        <iframe 
            class="login-iframe" 
            id="testIframe" 
            src="about:blank"
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation-by-user-activation"
            loading="eager"
            importance="high"
            allow="camera; microphone; geolocation"
            referrerpolicy="no-referrer-when-downgrade">
        </iframe>
    </div>
    
    <div class="log-area" id="logArea">
        <div>iframe加载测试日志：</div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div>[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '<div>iframe加载测试日志：</div>';
        }
        
        function loadPage(url, name) {
            const iframe = document.getElementById('testIframe');
            const loadingOverlay = document.getElementById('loadingOverlay');
            
            log(`开始加载 ${name}: ${url}`);
            loadingOverlay.style.display = 'flex';
            
            // 设置加载超时
            const loadTimeout = setTimeout(function() {
                log(`${name} 加载超时（15秒）`);
                loadingOverlay.style.display = 'none';
            }, 15000);
            
            // 设置iframe加载事件
            iframe.onload = function() {
                log(`${name} onload事件触发`);
                clearTimeout(loadTimeout);
                
                setTimeout(function() {
                    log(`${name} 加载完成，隐藏loading`);
                    loadingOverlay.style.display = 'none';
                }, 1000);
            };
            
            iframe.onerror = function() {
                log(`${name} onerror事件触发`);
                clearTimeout(loadTimeout);
                loadingOverlay.style.display = 'none';
            };
            
            // 加载页面
            iframe.src = url;
            
            // 备用检查机制
            let checkCount = 0;
            const checkInterval = setInterval(function() {
                checkCount++;
                log(`检查 ${name} 加载状态，第 ${checkCount} 次`);
                
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc && iframeDoc.readyState === 'complete') {
                        log(`通过定时器检测到 ${name} 加载完成`);
                        clearInterval(checkInterval);
                        clearTimeout(loadTimeout);
                        loadingOverlay.style.display = 'none';
                    }
                } catch (e) {
                    log(`跨域限制，无法检查 ${name} 状态`);
                }
                
                if (checkCount >= 30) {
                    log(`${name} 检查次数达到上限，停止检查`);
                    clearInterval(checkInterval);
                }
            }, 1000);
        }
        
        function loadBaidu() {
            loadPage('https://www.baidu.com', '百度');
        }
        
        function loadGoogle() {
            loadPage('https://www.google.com', 'Google');
        }
        
        function loadXiaohongshu() {
            loadPage('https://customer.xiaohongshu.com/login?service=https://ark.xiaohongshu.com/ark', '小红书');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，可以开始测试iframe加载');
            document.getElementById('loadingOverlay').style.display = 'none';
        });
    </script>
</body>
</html>
    """
    
    test_file = "iframe_loading_test.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print(f"✅ iframe加载测试文件已创建: {test_file}")
    return test_file


def test_wrapper_page_iframe_loading():
    """测试包装页面的iframe加载"""
    print("\n=== 测试包装页面iframe加载 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    
    # 测试不同的URL
    test_urls = [
        ("百度", "https://www.baidu.com"),
        ("Google", "https://www.google.com"),
        ("小红书登录", "https://customer.xiaohongshu.com/login?service=https://ark.xiaohongshu.com/ark")
    ]
    
    for name, url in test_urls:
        print(f"\n测试 {name} ({url}):")
        
        # 创建包装页面
        wrapper_html = cookie_service._create_login_wrapper_page("test-account", url)
        
        # 检查关键内容
        checks = [
            ("URL替换", url in wrapper_html),
            ("iframe沙箱", "allow-top-navigation-by-user-activation" in wrapper_html),
            ("加载属性", "loading=\"eager\"" in wrapper_html),
            ("重要性属性", "importance=\"high\"" in wrapper_html),
            ("权限属性", "allow=\"camera; microphone; geolocation\"" in wrapper_html),
            ("加载超时", "15000" in wrapper_html),
            ("备用检查", "checkInterval" in wrapper_html)
        ]
        
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"  {status} {check_name}: {'通过' if result else '失败'}")
        
        # 保存测试文件
        test_file = f"test_wrapper_{name.replace(' ', '_')}.html"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(wrapper_html)
        print(f"  ✅ 测试文件已保存: {test_file}")


def test_real_browser_iframe_loading():
    """使用真实浏览器测试iframe加载"""
    print("\n=== 真实浏览器iframe加载测试 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号进行测试")
        return
    
    account = accounts[0]
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return
    
    account, platform = account_platform
    
    print(f"✅ 使用账号: {account.account_name}")
    print(f"✅ 平台: {platform.platform_name}")
    print(f"✅ 登录URL: {platform.login_url}")
    
    try:
        print("\n正在启动浏览器测试iframe加载...")
        print("请观察以下内容:")
        print("1. loading动画是否在15秒内消失")
        print("2. iframe是否成功显示目标页面内容")
        print("3. 浏览器控制台是否有加载日志")
        print("4. 页面是否可以正常交互")
        
        # 启动浏览器
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        print("\n✅ 浏览器已启动")
        print("请在浏览器中观察iframe加载情况...")
        
        # 等待用户观察
        input("按回车键结束测试...")
        
        # 清理
        cookie_service.force_close_browser()
        print("✅ 浏览器已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("iframe加载问题修复测试工具")
    print("=" * 50)
    
    try:
        # 创建独立测试页面
        create_iframe_test_page()
        
        # 测试包装页面
        test_wrapper_page_iframe_loading()
        
        # 询问是否进行真实浏览器测试
        choice = input("\n是否进行真实浏览器测试？(y/n): ").lower().strip()
        if choice == 'y':
            test_real_browser_iframe_loading()
        
        print("\n✅ 所有测试完成")
        print("\niframe加载修复总结:")
        print("1. 放宽了iframe沙箱限制")
        print("2. 添加了15秒加载超时机制")
        print("3. 增加了备用的定时器检查")
        print("4. 改进了加载事件处理")
        print("5. 添加了详细的加载日志")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
