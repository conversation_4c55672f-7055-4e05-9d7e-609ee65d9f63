#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试强化的防重定向机制
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def create_test_page_with_redirect_attempts():
    """创建包含各种重定向尝试的测试页面"""
    test_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强化防重定向测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; height: 100vh; overflow: hidden; }
        .header-container { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 9999; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; }
        .header-title { color: white; font-size: 16px; font-weight: 600; }
        .test-buttons { display: flex; gap: 10px; }
        .test-button { background: #52c41a; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; }
        .test-button:hover { background: #45a017; }
        .iframe-container { position: absolute; top: 60px; left: 0; right: 0; bottom: 0; background: white; }
        .login-iframe { width: 100%; height: 100%; border: none; background: white; }
        .test-info { position: fixed; top: 70px; left: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 400px; z-index: 9998; }
        .log-area { position: fixed; bottom: 20px; left: 20px; right: 20px; height: 150px; background: #000; color: #0f0; font-family: monospace; font-size: 12px; padding: 10px; border-radius: 4px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="header-container">
        <div class="header-title">强化防重定向测试</div>
        <div class="test-buttons">
            <button class="test-button" onclick="testLocationHref()">location.href</button>
            <button class="test-button" onclick="testLocationAssign()">location.assign</button>
            <button class="test-button" onclick="testLocationReplace()">location.replace</button>
            <button class="test-button" onclick="testHistoryPush()">history.push</button>
            <button class="test-button" onclick="testTopLocation()">top.location</button>
            <button class="test-button" onclick="testParentLocation()">parent.location</button>
            <button class="test-button" onclick="clearLog()">清除日志</button>
        </div>
    </div>
    
    <div class="test-info">
        <h3>测试说明</h3>
        <p>点击上方按钮测试各种重定向方式，所有重定向都应该被阻止。</p>
        <p>查看底部日志区域了解拦截情况。</p>
        <p><strong>如果页面跳转了，说明防护失败！</strong></p>
    </div>
    
    <div class="iframe-container">
        <iframe class="login-iframe" src="data:text/html,<h1>这是iframe内容</h1><script>console.log('iframe加载完成');</script>" sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals"></iframe>
    </div>
    
    <div class="log-area" id="logArea">
        <div>防重定向测试日志：</div>
    </div>

    <script>
        window.userClosing = false;
        
        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div>[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '<div>防重定向测试日志：</div>';
        }
        
        // 强化的防重定向机制
        function preventRedirect() {
            log('启动强化防重定向机制');
            
            const originalLocation = window.location;
            const originalHref = originalLocation.href;
            
            // 1. 防止beforeunload跳转
            window.addEventListener('beforeunload', function(e) {
                if (!window.userClosing) {
                    log('阻止beforeunload跳转');
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }
            });
            
            // 2. 阻止历史记录变化
            window.addEventListener('popstate', function(e) {
                log('阻止popstate变化');
                e.preventDefault();
                e.stopPropagation();
                history.pushState(null, '', originalHref);
            });
            
            // 3. 重写location对象的所有属性
            Object.defineProperty(window, 'location', {
                get: function() {
                    return {
                        href: originalHref,
                        protocol: originalLocation.protocol,
                        host: originalLocation.host,
                        hostname: originalLocation.hostname,
                        port: originalLocation.port,
                        pathname: originalLocation.pathname,
                        search: originalLocation.search,
                        hash: originalLocation.hash,
                        origin: originalLocation.origin,
                        assign: function(url) {
                            log('阻止location.assign重定向到: ' + url);
                        },
                        replace: function(url) {
                            log('阻止location.replace重定向到: ' + url);
                        },
                        reload: function() {
                            log('阻止location.reload');
                        },
                        toString: function() {
                            return originalHref;
                        }
                    };
                },
                set: function(value) {
                    log('阻止location设置重定向到: ' + value);
                }
            });
            
            // 4. 重写top和parent的location
            try {
                Object.defineProperty(window, 'top', {
                    get: function() {
                        return {
                            location: window.location,
                            document: window.document,
                            window: window
                        };
                    }
                });
                
                Object.defineProperty(window, 'parent', {
                    get: function() {
                        return {
                            location: window.location,
                            document: window.document,
                            window: window
                        };
                    }
                });
                log('成功重写top和parent对象');
            } catch (e) {
                log('无法重写top/parent对象: ' + e.message);
            }
            
            // 5. 重写history对象
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;
            
            history.pushState = function(state, title, url) {
                if (url && url !== originalHref && !url.startsWith('#')) {
                    log('阻止history.pushState重定向到: ' + url);
                    return;
                }
                return originalPushState.call(this, state, title, url);
            };
            
            history.replaceState = function(state, title, url) {
                if (url && url !== originalHref && !url.startsWith('#')) {
                    log('阻止history.replaceState重定向到: ' + url);
                    return;
                }
                return originalReplaceState.call(this, state, title, url);
            };
            
            // 6. 定期检查URL是否被修改
            setInterval(function() {
                if (window.location.href !== originalHref) {
                    log('检测到URL被修改，强制恢复');
                    try {
                        history.replaceState(null, '', originalHref);
                    } catch (e) {
                        log('无法恢复URL: ' + e.message);
                    }
                }
            }, 1000);
            
            log('防重定向机制已启用，原始URL: ' + originalHref);
        }
        
        // 测试函数
        function testLocationHref() {
            log('测试 window.location.href 重定向');
            try {
                window.location.href = 'https://www.baidu.com';
            } catch (e) {
                log('location.href 重定向被阻止: ' + e.message);
            }
        }
        
        function testLocationAssign() {
            log('测试 window.location.assign 重定向');
            try {
                window.location.assign('https://www.baidu.com');
            } catch (e) {
                log('location.assign 重定向被阻止: ' + e.message);
            }
        }
        
        function testLocationReplace() {
            log('测试 window.location.replace 重定向');
            try {
                window.location.replace('https://www.baidu.com');
            } catch (e) {
                log('location.replace 重定向被阻止: ' + e.message);
            }
        }
        
        function testHistoryPush() {
            log('测试 history.pushState 重定向');
            try {
                history.pushState(null, '', 'https://www.baidu.com');
            } catch (e) {
                log('history.pushState 重定向被阻止: ' + e.message);
            }
        }
        
        function testTopLocation() {
            log('测试 top.location 重定向');
            try {
                top.location.href = 'https://www.baidu.com';
            } catch (e) {
                log('top.location 重定向被阻止: ' + e.message);
            }
        }
        
        function testParentLocation() {
            log('测试 parent.location 重定向');
            try {
                parent.location.href = 'https://www.baidu.com';
            } catch (e) {
                log('parent.location 重定向被阻止: ' + e.message);
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            preventRedirect();
            log('页面加载完成，防重定向机制已启用');
        });
    </script>
</body>
</html>
    """
    
    test_file = "strong_redirect_test.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print(f"✅ 强化防重定向测试文件已创建: {test_file}")
    return test_file


def test_wrapper_page_with_real_browser():
    """使用真实浏览器测试包装页面"""
    print("\n=== 真实浏览器测试 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号进行测试")
        return
    
    account = accounts[0]
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return
    
    account, platform = account_platform
    
    print(f"✅ 使用账号: {account.account_name}")
    print(f"✅ 平台: {platform.platform_name}")
    print(f"✅ 登录URL: {platform.login_url}")
    
    try:
        print("\n正在启动强化防重定向的浏览器...")
        print("请测试以下功能:")
        print("1. 页面是否保持在包装页面（不会重定向）")
        print("2. 在iframe中进行各种操作")
        print("3. 尝试在iframe中点击可能导致重定向的链接")
        print("4. 观察浏览器控制台的防重定向日志")
        print("5. 点击'我已完成登录'按钮测试功能")
        
        # 启动浏览器
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        print("\n✅ 浏览器已启动")
        print("请在浏览器中测试强化的防重定向功能...")
        
        # 等待用户测试
        input("按回车键结束测试...")
        
        # 清理
        cookie_service.force_close_browser()
        print("✅ 浏览器已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("强化防重定向机制测试工具")
    print("=" * 50)
    
    try:
        # 创建测试页面
        create_test_page_with_redirect_attempts()
        
        # 询问是否进行真实浏览器测试
        choice = input("\n是否进行真实浏览器测试？(y/n): ").lower().strip()
        if choice == 'y':
            test_wrapper_page_with_real_browser()
        
        print("\n✅ 所有测试完成")
        print("如果页面仍然会重定向，请检查:")
        print("1. iframe的sandbox属性是否正确设置")
        print("2. 防重定向JavaScript是否正确执行")
        print("3. 目标网站是否使用了特殊的重定向技术")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
