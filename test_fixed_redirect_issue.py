#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的重定向问题
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def test_wrapper_page_creation():
    """测试包装页面创建"""
    print("=== 测试包装页面创建 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    
    # 测试参数
    account_id = "test-account-123"
    login_url = "https://www.baidu.com"
    
    # 创建包装页面
    wrapper_html = cookie_service._create_login_wrapper_page(account_id, login_url)
    
    print(f"✅ 包装页面HTML长度: {len(wrapper_html)} 字符")
    
    # 检查关键内容
    checks = [
        ("账号ID替换", account_id in wrapper_html),
        ("登录URL替换", login_url in wrapper_html),
        ("按钮元素", "我已完成登录" in wrapper_html),
        ("iframe元素", "login-iframe" in wrapper_html),
        ("JavaScript函数", "checkLogin" in wrapper_html),
        ("防重定向机制", "preventRedirect" in wrapper_html),
        ("iframe沙箱", "sandbox=" in wrapper_html)
    ]
    
    for check_name, result in checks:
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {'通过' if result else '失败'}")
    
    # 保存测试文件
    test_file = "test_fixed_wrapper.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(wrapper_html)
    
    print(f"✅ 测试文件已保存: {test_file}")
    return test_file


def analyze_open_incognito_browser_method():
    """分析open_incognito_browser方法"""
    print("\n=== 分析open_incognito_browser方法 ===")
    
    # 读取源代码
    with open('src/services/cookie_service.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找方法定义
    method_start = content.find('def open_incognito_browser(')
    if method_start == -1:
        print("❌ 未找到open_incognito_browser方法")
        return
    
    # 查找方法结束
    lines = content[method_start:].split('\n')
    method_lines = []
    indent_level = None
    
    for line in lines:
        if line.strip().startswith('def open_incognito_browser('):
            indent_level = len(line) - len(line.lstrip())
            method_lines.append(line)
        elif indent_level is not None:
            current_indent = len(line) - len(line.lstrip())
            if line.strip() and current_indent <= indent_level:
                # 方法结束
                break
            method_lines.append(line)
    
    method_code = '\n'.join(method_lines)
    
    # 分析关键点
    print("分析关键代码片段:")
    
    # 检查driver.get调用
    get_calls = []
    for i, line in enumerate(method_lines):
        if 'driver.get(' in line or 'self.driver.get(' in line:
            get_calls.append((i + 1, line.strip()))
    
    print(f"\n找到 {len(get_calls)} 个driver.get()调用:")
    for line_num, line in get_calls:
        print(f"  第{line_num}行: {line}")
    
    # 检查是否有重复的浏览器创建
    driver_creations = []
    for i, line in enumerate(method_lines):
        if 'webdriver.Chrome(' in line:
            driver_creations.append((i + 1, line.strip()))
    
    print(f"\n找到 {len(driver_creations)} 个webdriver.Chrome()调用:")
    for line_num, line in driver_creations:
        print(f"  第{line_num}行: {line}")
    
    # 检查包装页面相关代码
    wrapper_related = []
    for i, line in enumerate(method_lines):
        if any(keyword in line.lower() for keyword in ['wrapper', 'temp_file', 'file://']):
            wrapper_related.append((i + 1, line.strip()))
    
    print(f"\n找到 {len(wrapper_related)} 个包装页面相关调用:")
    for line_num, line in wrapper_related:
        print(f"  第{line_num}行: {line}")
    
    # 检查是否有冲突的逻辑
    if len(get_calls) > 1:
        print("\n⚠️  警告: 发现多个driver.get()调用，可能存在冲突!")
        print("   这可能导致包装页面被覆盖")
    
    if len(driver_creations) > 1:
        print("\n⚠️  警告: 发现多个webdriver.Chrome()调用，可能存在冲突!")
        print("   这可能导致多个浏览器实例")
    
    return method_code


def test_browser_launch():
    """测试浏览器启动"""
    print("\n=== 测试浏览器启动 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号进行测试")
        return
    
    account = accounts[0]
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return
    
    account, platform = account_platform
    
    print(f"✅ 使用账号: {account.account_name}")
    print(f"✅ 平台: {platform.platform_name}")
    print(f"✅ 登录URL: {platform.login_url}")
    
    try:
        print("\n正在启动浏览器...")
        print("请检查以下内容:")
        print("1. 浏览器是否打开了包装页面（file://开头的URL）")
        print("2. 页面顶部是否有蓝色按钮区域")
        print("3. 下方iframe是否显示目标登录页面")
        print("4. 页面是否保持在包装页面（不会跳转到原登录页面）")
        print("5. 点击按钮是否弹出确认对话框")
        
        # 启动浏览器
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        print("\n✅ 浏览器已启动")
        
        # 检查浏览器状态
        if hasattr(cookie_service, 'driver') and cookie_service.driver:
            try:
                current_url = cookie_service.driver.current_url
                print(f"✅ 当前页面URL: {current_url}")
                
                if current_url.startswith('file://'):
                    print("✅ 正确使用了包装页面")
                else:
                    print("❌ 没有使用包装页面，可能存在重定向问题")
                
                # 检查页面标题
                try:
                    title = cookie_service.driver.title
                    print(f"✅ 页面标题: {title}")
                except:
                    print("❌ 无法获取页面标题")
                
            except Exception as e:
                print(f"❌ 检查浏览器状态失败: {e}")
        else:
            print("❌ 浏览器未启动或已关闭")
        
        # 等待用户测试
        input("\n按回车键结束测试...")
        
        # 清理
        cookie_service.force_close_browser()
        print("✅ 浏览器已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("修复后的重定向问题测试工具")
    print("=" * 50)
    
    try:
        # 测试包装页面创建
        test_wrapper_page_creation()
        
        # 分析方法代码
        analyze_open_incognito_browser_method()
        
        # 询问是否进行浏览器测试
        choice = input("\n是否进行浏览器启动测试？(y/n): ").lower().strip()
        if choice == 'y':
            test_browser_launch()
        
        print("\n✅ 所有测试完成")
        print("\n修复总结:")
        print("1. 移除了重复的driver.get()调用")
        print("2. 确保只加载包装页面")
        print("3. 强化了防重定向机制")
        print("4. 添加了iframe沙箱限制")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
