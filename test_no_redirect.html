<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面 - 数据采集工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }
        
        /* 顶部按钮区域 */
        .header-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .header-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
        }
        
        .header-buttons {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .login-button {
            background: #52c41a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .login-button:hover {
            background: #45a017;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .login-button:active {
            transform: translateY(0);
        }
        
        .login-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff4d4f;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.success {
            background: #52c41a;
            animation: none;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        /* iframe容器 */
        .iframe-container {
            position: absolute;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
        }
        
        .login-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        
        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-left: 15px;
            color: #666;
            font-size: 14px;
        }
        
        /* 提示信息 */
        .info-panel {
            position: fixed;
            top: 70px;
            right: 20px;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 300px;
            z-index: 9998;
            border-left: 4px solid #1890ff;
        }
        
        .info-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .info-content {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
        }
        
        .close-info {
            position: absolute;
            top: 5px;
            right: 8px;
            background: none;
            border: none;
            font-size: 16px;
            color: #999;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- 顶部按钮区域 -->
    <div class="header-container">
        <div class="header-title">
            <span class="status-indicator" id="statusIndicator"></span>
            <span id="headerTitle">请完成登录操作</span>
        </div>
        <div class="header-buttons">
            <button class="login-button" id="loginButton" onclick="checkLogin()">
                我已完成登录
            </button>
        </div>
    </div>
    
    <!-- 信息提示面板 -->
    <div class="info-panel" id="infoPanel">
        <button class="close-info" onclick="closeInfo()">&times;</button>
        <div class="info-title">操作提示</div>
        <div class="info-content">
            1. 在下方页面中完成登录操作<br>
            2. 登录成功后点击"我已完成登录"按钮<br>
            3. 系统将自动检测并保存登录状态
        </div>
    </div>
    
    <!-- 加载状态 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载登录页面...</div>
    </div>
    
    <!-- iframe容器 -->
    <div class="iframe-container">
        <iframe class="login-iframe" id="loginIframe" src="about:blank"></iframe>
    </div>

    <script>
        // 全局变量
        window.accountId = 'test-account-123';
        window.loginUrl = 'https://www.baidu.com';
        window.loginCompleted = false;
        window.checkingLogin = false;
        
        // 防止页面被重定向
        function preventRedirect() {
            // 防止整个页面被重定向
            window.addEventListener('beforeunload', function(e) {
                // 如果不是用户主动关闭，阻止跳转
                if (!window.userClosing) {
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }
            });

            // 监听历史记录变化
            window.addEventListener('popstate', function(e) {
                console.log('检测到历史记录变化，保持在包装页面');
                e.preventDefault();
            });

            // 防止location被修改
            const originalLocation = window.location;
            Object.defineProperty(window, 'location', {
                get: function() {
                    return originalLocation;
                },
                set: function(value) {
                    console.log('阻止location重定向:', value);
                    // 不执行重定向
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            preventRedirect();
            initializePage();
        });
        
        function initializePage() {
            console.log('初始化登录包装页面');
            console.log('账号ID:', window.accountId);
            console.log('登录URL:', window.loginUrl);
            
            // 加载目标登录页面
            loadLoginPage();
            
            // 5秒后隐藏提示面板
            setTimeout(function() {
                closeInfo();
            }, 5000);
        }
        
        function loadLoginPage() {
            const iframe = document.getElementById('loginIframe');
            const loadingOverlay = document.getElementById('loadingOverlay');

            // 设置iframe加载事件
            iframe.onload = function() {
                console.log('登录页面加载完成');
                loadingOverlay.style.display = 'none';
                updateStatus('ready', '请在下方页面完成登录');

                // 防止iframe内的页面跳转影响父页面
                try {
                    // 监听iframe内的导航事件
                    iframe.contentWindow.addEventListener('beforeunload', function(e) {
                        console.log('iframe页面即将跳转');
                    });
                } catch (e) {
                    // 跨域限制，无法访问iframe内容，这是正常的
                    console.log('iframe跨域，无法监听内部事件');
                }
            };

            iframe.onerror = function() {
                console.error('登录页面加载失败');
                loadingOverlay.innerHTML = '<div style="color: #ff4d4f;">页面加载失败，请刷新重试</div>';
                updateStatus('error', '页面加载失败');
            };

            // 防止iframe导航影响父页面
            iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox');

            // 加载目标页面
            iframe.src = window.loginUrl;
        }
        
        function updateStatus(status, message) {
            const indicator = document.getElementById('statusIndicator');
            const title = document.getElementById('headerTitle');
            
            indicator.className = 'status-indicator';
            
            switch(status) {
                case 'ready':
                    indicator.classList.add('ready');
                    break;
                case 'checking':
                    indicator.classList.add('checking');
                    break;
                case 'success':
                    indicator.classList.add('success');
                    break;
                case 'error':
                    indicator.classList.add('error');
                    break;
            }
            
            title.textContent = message;
        }
        
        function checkLogin() {
            if (window.checkingLogin) {
                return;
            }
            
            // 弹出确认对话框
            if (!confirm('请确认您已完成登录操作。\n\n点击"确定"检查登录状态\n点击"取消"继续登录')) {
                return;
            }
            
            window.checkingLogin = true;
            const button = document.getElementById('loginButton');
            
            // 更新按钮状态
            button.disabled = true;
            button.textContent = '正在检查登录状态...';
            updateStatus('checking', '正在检查登录状态...');
            
            // 设置完成标记，让Python端检测
            window.loginCompleted = true;
            
            console.log('用户点击完成登录按钮');
        }
        
        function onLoginSuccess() {
            console.log('登录成功');
            const button = document.getElementById('loginButton');

            button.textContent = '登录成功！';
            button.style.background = '#52c41a';
            updateStatus('success', '登录成功！Cookie已保存');

            // 显示成功提示
            alert('登录成功！Cookie已获取，浏览器将在3秒后自动关闭。');

            // 标记用户主动关闭
            window.userClosing = true;

            // 3秒后关闭窗口
            setTimeout(function() {
                window.userClosing = true;
                window.close();
            }, 3000);
        }
        
        function onLoginFailure(message) {
            console.log('登录失败:', message);
            const button = document.getElementById('loginButton');
            
            // 重置按钮状态
            button.disabled = false;
            button.textContent = '我已完成登录';
            button.style.background = '#52c41a';
            updateStatus('ready', '请完成登录操作');
            
            window.checkingLogin = false;
            window.loginCompleted = false;
            
            // 显示失败提示
            alert('未获取到登录态，请先完成登录操作后再点击按钮');
        }
        
        function closeInfo() {
            const infoPanel = document.getElementById('infoPanel');
            infoPanel.style.display = 'none';
        }
        
        // 监听iframe内的页面变化（如果可能）
        setInterval(function() {
            try {
                const iframe = document.getElementById('loginIframe');
                if (iframe.contentWindow && iframe.contentWindow.location) {
                    // 可以访问iframe内容，说明同域
                    console.log('当前iframe URL:', iframe.contentWindow.location.href);
                }
            } catch (e) {
                // 跨域限制，无法访问iframe内容
                // 这是正常情况
            }
        }, 2000);
    </script>
</body>
</html>
