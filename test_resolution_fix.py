#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分辨率和拖拽显示修复
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def test_resolution_and_drag():
    """测试分辨率和拖拽显示"""
    print("=" * 60)
    print("分辨率和拖拽显示测试")
    print("=" * 60)
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取测试账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号")
        return
    
    account = accounts[0]
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return
    
    account, platform = account_platform
    
    print(f"✅ 测试账号: {account.account_name}")
    print(f"✅ 测试平台: {platform.platform_name}")
    print(f"✅ 登录URL: {platform.login_url}")
    
    try:
        print("\n启动浏览器进行分辨率测试...")
        
        # 启动浏览器
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        time.sleep(3)  # 等待页面加载
        
        if hasattr(cookie_service, 'driver') and cookie_service.driver:
            print("\n检查浏览器窗口设置...")
            
            # 获取当前窗口大小
            window_size = cookie_service.driver.get_window_size()
            print(f"当前窗口大小: {window_size['width']} x {window_size['height']}")
            
            # 测试不同分辨率
            test_resolutions = [
                (1920, 1080, "1080p"),
                (1366, 768, "标准笔记本"),
                (1280, 720, "720p"),
                (1024, 768, "4:3标准"),
                (800, 600, "最小支持")
            ]
            
            for width, height, name in test_resolutions:
                print(f"\n测试分辨率: {name} ({width}x{height})")
                
                try:
                    # 设置窗口大小
                    cookie_service.driver.set_window_size(width, height)
                    time.sleep(1)
                    
                    # 检查页面元素是否正常显示
                    header = cookie_service.driver.find_element("css selector", ".header-container")
                    iframe = cookie_service.driver.find_element("css selector", ".login-iframe")
                    button = cookie_service.driver.find_element("id", "loginButton")
                    
                    # 检查元素位置和大小
                    header_rect = header.rect
                    iframe_rect = iframe.rect
                    button_rect = button.rect
                    
                    print(f"  ✅ 头部区域: {header_rect['width']}x{header_rect['height']} at ({header_rect['x']}, {header_rect['y']})")
                    print(f"  ✅ iframe区域: {iframe_rect['width']}x{iframe_rect['height']} at ({iframe_rect['x']}, {iframe_rect['y']})")
                    print(f"  ✅ 按钮位置: {button_rect['width']}x{button_rect['height']} at ({button_rect['x']}, {button_rect['y']})")
                    
                    # 检查是否有元素溢出
                    if header_rect['width'] > 0 and iframe_rect['width'] > 0 and button_rect['width'] > 0:
                        print(f"  ✅ {name} 分辨率显示正常")
                    else:
                        print(f"  ❌ {name} 分辨率显示异常")
                    
                    # 检查iframe是否正确加载
                    iframe_src = iframe.get_attribute('src')
                    if platform.login_url in iframe_src:
                        print(f"  ✅ iframe内容加载正常")
                    else:
                        print(f"  ❌ iframe内容加载异常: {iframe_src}")
                        
                except Exception as e:
                    print(f"  ❌ {name} 分辨率测试失败: {e}")
            
            # 恢复到标准分辨率
            cookie_service.driver.set_window_size(1366, 768)
            
            print("\n" + "=" * 60)
            print("拖拽测试说明")
            print("=" * 60)
            print("请手动进行以下测试:")
            print("1. 拖拽浏览器窗口到屏幕右侧")
            print("2. 调整浏览器窗口大小")
            print("3. 最大化和还原浏览器窗口")
            print("4. 检查页面元素是否保持正确位置")
            print("5. 检查iframe内容是否正常显示")
            print("6. 检查按钮是否始终可见和可点击")
            print("7. 检查页面是否有横向或纵向滚动条")
            print("8. 检查在不同分辨率下的显示效果")
            
            print("\n预期效果:")
            print("✅ 顶部按钮区域始终固定在顶部")
            print("✅ iframe内容区域自适应剩余空间")
            print("✅ 所有元素在拖拽时保持正确位置")
            print("✅ 页面在小分辨率下仍然可用")
            print("✅ 没有内容溢出或错位")
            
            # 执行一些JavaScript检查
            js_check_result = cookie_service.driver.execute_script("""
                // 检查页面布局
                const header = document.querySelector('.header-container');
                const iframe = document.querySelector('.login-iframe');
                const container = document.querySelector('.iframe-container');
                
                const result = {
                    windowSize: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    headerHeight: header ? header.offsetHeight : 0,
                    iframeVisible: iframe ? iframe.offsetWidth > 0 && iframe.offsetHeight > 0 : false,
                    containerPosition: container ? {
                        top: container.offsetTop,
                        left: container.offsetLeft,
                        width: container.offsetWidth,
                        height: container.offsetHeight
                    } : null,
                    hasHorizontalScroll: document.body.scrollWidth > window.innerWidth,
                    hasVerticalScroll: document.body.scrollHeight > window.innerHeight
                };
                
                return result;
            """)
            
            print(f"\nJavaScript布局检查结果:")
            print(f"  窗口大小: {js_check_result['windowSize']['width']}x{js_check_result['windowSize']['height']}")
            print(f"  头部高度: {js_check_result['headerHeight']}px")
            print(f"  iframe可见: {js_check_result['iframeVisible']}")
            print(f"  容器位置: {js_check_result['containerPosition']}")
            print(f"  水平滚动: {js_check_result['hasHorizontalScroll']}")
            print(f"  垂直滚动: {js_check_result['hasVerticalScroll']}")
            
            input("\n完成手动拖拽测试后，按回车键继续...")
            
        else:
            print("❌ 浏览器未启动")
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        try:
            cookie_service.force_close_browser()
            print("✅ 浏览器已关闭")
        except:
            pass


def create_resolution_test_html():
    """创建分辨率测试HTML文件"""
    test_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分辨率测试页面</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f0f0f0; min-width: 800px; min-height: 600px; }
        .test-header { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; z-index: 9999; }
        .test-content { position: fixed; top: 60px; left: 0; right: 0; bottom: 0; background: white; overflow: auto; }
        .test-iframe { width: 100%; height: 100%; border: none; min-width: 800px; min-height: 540px; }
        .info-panel { position: fixed; top: 70px; right: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 300px; z-index: 9998; }
        .resolution-info { position: fixed; bottom: 20px; left: 20px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="test-header">
        <div>分辨率和拖拽测试页面</div>
        <button onclick="showInfo()" style="background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">显示信息</button>
    </div>
    
    <div class="test-content">
        <iframe class="test-iframe" src="https://www.baidu.com"></iframe>
    </div>
    
    <div class="info-panel" id="infoPanel" style="display: none;">
        <h3>测试说明</h3>
        <p>1. 拖拽窗口到不同位置</p>
        <p>2. 调整窗口大小</p>
        <p>3. 最大化/还原窗口</p>
        <p>4. 检查布局是否正常</p>
        <button onclick="hideInfo()" style="float: right; background: none; border: none; cursor: pointer;">×</button>
    </div>
    
    <div class="resolution-info" id="resolutionInfo">
        分辨率: <span id="resolution"></span><br>
        视口: <span id="viewport"></span>
    </div>
    
    <script>
        function updateResolution() {
            document.getElementById('resolution').textContent = screen.width + 'x' + screen.height;
            document.getElementById('viewport').textContent = window.innerWidth + 'x' + window.innerHeight;
        }
        
        function showInfo() {
            document.getElementById('infoPanel').style.display = 'block';
        }
        
        function hideInfo() {
            document.getElementById('infoPanel').style.display = 'none';
        }
        
        window.addEventListener('resize', updateResolution);
        window.addEventListener('load', updateResolution);
        setInterval(updateResolution, 1000);
    </script>
</body>
</html>
    """
    
    test_file = "resolution_test.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print(f"✅ 分辨率测试文件已创建: {test_file}")
    return test_file


def main():
    """主函数"""
    print("分辨率和拖拽显示修复测试工具")
    print("测试内容:")
    print("1. 不同分辨率下的页面显示")
    print("2. 窗口拖拽时的布局稳定性")
    print("3. 元素定位和大小适配")
    print("4. iframe内容显示正确性")
    
    try:
        # 创建独立测试文件
        create_resolution_test_html()
        
        # 进行真实浏览器测试
        test_resolution_and_drag()
        
        print("\n" + "=" * 60)
        print("修复总结")
        print("=" * 60)
        print("✅ 添加了最小宽度和高度限制")
        print("✅ 使用fixed定位替代absolute定位")
        print("✅ 添加了响应式设计支持")
        print("✅ 优化了拖拽时的渲染性能")
        print("✅ 防止了内容溢出和错位")
        print("✅ 添加了硬件加速优化")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
