
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防重定向测试页面</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; height: 100vh; overflow: hidden; }
        .header-container { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 9999; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; }
        .header-title { color: white; font-size: 16px; font-weight: 600; }
        .login-button { background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.3s ease; }
        .login-button:hover { background: #45a017; }
        .login-button:disabled { background: #d9d9d9; cursor: not-allowed; }
        .iframe-container { position: absolute; top: 60px; left: 0; right: 0; bottom: 0; background: white; }
        .login-iframe { width: 100%; height: 100%; border: none; background: white; }
        .test-info { position: fixed; top: 70px; left: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 300px; z-index: 9998; }
    </style>
</head>
<body>
    <div class="header-container">
        <div class="header-title">防重定向测试页面</div>
        <button class="login-button" onclick="testRedirect()">测试重定向</button>
    </div>
    
    <div class="test-info">
        <h3>测试说明</h3>
        <p>1. 点击"测试重定向"按钮</p>
        <p>2. 页面应该保持在当前页面</p>
        <p>3. 不应该跳转到百度</p>
        <p>4. 查看控制台日志</p>
    </div>
    
    <div class="iframe-container">
        <iframe class="login-iframe" src="https://www.baidu.com" sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"></iframe>
    </div>

    <script>
        window.userClosing = false;
        
        // 防止页面被重定向
        function preventRedirect() {
            console.log('初始化防重定向机制');
            
            // 防止整个页面被重定向
            window.addEventListener('beforeunload', function(e) {
                if (!window.userClosing) {
                    console.log('阻止页面跳转');
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }
            });
            
            // 防止location被修改
            const originalLocation = window.location;
            Object.defineProperty(window, 'location', {
                get: function() {
                    return originalLocation;
                },
                set: function(value) {
                    console.log('阻止location重定向:', value);
                    alert('阻止了重定向到: ' + value);
                }
            });
            
            console.log('防重定向机制已启用');
        }
        
        function testRedirect() {
            console.log('测试重定向功能');
            
            // 尝试各种重定向方式
            try {
                console.log('尝试 window.location.href 重定向');
                window.location.href = 'https://www.baidu.com';
            } catch (e) {
                console.log('location.href 重定向被阻止:', e);
            }
            
            try {
                console.log('尝试 window.location 重定向');
                window.location = 'https://www.baidu.com';
            } catch (e) {
                console.log('location 重定向被阻止:', e);
            }
            
            alert('重定向测试完成，检查控制台日志');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            preventRedirect();
            console.log('页面加载完成，防重定向机制已启用');
        });
    </script>
</body>
</html>
    