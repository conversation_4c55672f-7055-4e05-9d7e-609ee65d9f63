
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分辨率测试页面</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f0f0f0; min-width: 800px; min-height: 600px; }
        .test-header { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; z-index: 9999; }
        .test-content { position: fixed; top: 60px; left: 0; right: 0; bottom: 0; background: white; overflow: auto; }
        .test-iframe { width: 100%; height: 100%; border: none; min-width: 800px; min-height: 540px; }
        .info-panel { position: fixed; top: 70px; right: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 300px; z-index: 9998; }
        .resolution-info { position: fixed; bottom: 20px; left: 20px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="test-header">
        <div>分辨率和拖拽测试页面</div>
        <button onclick="showInfo()" style="background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">显示信息</button>
    </div>
    
    <div class="test-content">
        <iframe class="test-iframe" src="https://www.baidu.com"></iframe>
    </div>
    
    <div class="info-panel" id="infoPanel" style="display: none;">
        <h3>测试说明</h3>
        <p>1. 拖拽窗口到不同位置</p>
        <p>2. 调整窗口大小</p>
        <p>3. 最大化/还原窗口</p>
        <p>4. 检查布局是否正常</p>
        <button onclick="hideInfo()" style="float: right; background: none; border: none; cursor: pointer;">×</button>
    </div>
    
    <div class="resolution-info" id="resolutionInfo">
        分辨率: <span id="resolution"></span><br>
        视口: <span id="viewport"></span>
    </div>
    
    <script>
        function updateResolution() {
            document.getElementById('resolution').textContent = screen.width + 'x' + screen.height;
            document.getElementById('viewport').textContent = window.innerWidth + 'x' + window.innerHeight;
        }
        
        function showInfo() {
            document.getElementById('infoPanel').style.display = 'block';
        }
        
        function hideInfo() {
            document.getElementById('infoPanel').style.display = 'none';
        }
        
        window.addEventListener('resize', updateResolution);
        window.addEventListener('load', updateResolution);
        setInterval(updateResolution, 1000);
    </script>
</body>
</html>
    