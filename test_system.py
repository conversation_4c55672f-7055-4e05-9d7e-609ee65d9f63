#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统功能测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.platform_service import PlatformService
from src.services.account_service import AccountService
from src.services.excel_exporter import ExcelExporter


def test_data_manager():
    """测试数据管理器"""
    print("测试数据管理器...")
    
    data_manager = DataManager()
    data_manager.init_data_files()
    
    # 检查文件是否创建
    assert os.path.exists("data/accounts.json"), "accounts.json 文件未创建"
    assert os.path.exists("data/platforms.json"), "platforms.json 文件未创建"
    assert os.path.exists("data/cookies.json"), "cookies.json 文件未创建"
    
    print("✓ 数据管理器测试通过")


def test_platform_service():
    """测试平台服务"""
    print("测试平台服务...")
    
    data_manager = DataManager()
    platform_service = PlatformService(data_manager)
    
    # 获取预配置的小红书千帆平台
    platforms = platform_service.get_all_platforms()
    assert len(platforms) > 0, "没有找到预配置的平台"
    
    xiaohongshu_platform = None
    for platform in platforms:
        if "小红书千帆" in platform.platform_name:
            xiaohongshu_platform = platform
            break
    
    assert xiaohongshu_platform is not None, "没有找到小红书千帆平台"
    assert "ark.xiaohongshu.com" in xiaohongshu_platform.login_url, "小红书千帆登录URL不正确"
    
    print("✓ 平台服务测试通过")


def test_account_service():
    """测试账号服务"""
    print("测试账号服务...")
    
    data_manager = DataManager()
    account_service = AccountService(data_manager)
    platform_service = PlatformService(data_manager)
    
    # 获取小红书千帆平台ID
    platforms = platform_service.get_all_platforms()
    xiaohongshu_platform = None
    for platform in platforms:
        if "小红书千帆" in platform.platform_name:
            xiaohongshu_platform = platform
            break
    
    # 添加测试账号
    account_id = account_service.add_account("测试账号", xiaohongshu_platform.platform_id)
    assert account_id, "账号添加失败"
    
    # 获取账号
    account = account_service.get_account_by_id(account_id)
    assert account is not None, "无法获取添加的账号"
    assert account.account_name == "测试账号", "账号名称不正确"
    
    # 删除测试账号
    success = account_service.delete_account(account_id)
    assert success, "账号删除失败"
    
    print("✓ 账号服务测试通过")


def test_excel_exporter():
    """测试Excel导出器"""
    print("测试Excel导出器...")
    
    exporter = ExcelExporter()
    
    # 创建测试数据
    test_data = {
        "success": True,
        "data": [
            {"name": "测试指标1", "value": 100},
            {"name": "测试指标2", "value": 200}
        ],
        "platform_name": "测试平台",
        "account_name": "测试账号",
        "date_range": "2025-07-01至2025-07-09"
    }
    
    # 测试导出
    try:
        filepath = exporter.export_single_account_data(test_data, "test_exports")
        assert os.path.exists(filepath), "Excel文件未生成"
        
        # 清理测试文件
        os.remove(filepath)
        if os.path.exists("test_exports") and not os.listdir("test_exports"):
            os.rmdir("test_exports")
        
        print("✓ Excel导出器测试通过")
    except Exception as e:
        print(f"✗ Excel导出器测试失败: {e}")


def main():
    """主测试函数"""
    print("开始系统功能测试...\n")
    
    try:
        test_data_manager()
        test_platform_service()
        test_account_service()
        test_excel_exporter()
        
        print("\n🎉 所有测试通过！系统功能正常。")
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试出错: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
