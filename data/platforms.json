{"xiaohongshu_qianfan": {"platform_name": "小红书千帆", "login_url": "https://customer.xiaohongshu.com/login?service=https://ark.xiaohongshu.com/app-seller/sellerInfo?from=ark-login", "data_api_url": "https://ark.xiaohongshu.com/api/edith/butterfly/data", "extract_rule": "$.data[0].data[0].payGmv.value", "field_mappings": [{"field_name": "退款金额", "json_path": "$.data[0].data[0].refundPayGmv.value", "data_type": "数字", "default_value": ""}, {"field_name": "支付金额", "json_path": "$.data[0].data[0].payGmv.value", "data_type": "数字", "default_value": ""}], "shop_name_api_url": "https://ark.xiaohongshu.com/api/user/profile", "shop_name_field": "data.name"}, "a10f0283-ebf7-4285-9438-e4047b98d47c": {"platform_name": "小红书乘风", "login_url": "https://mcc.xiaohongshu.com/micro/home", "data_api_url": "https://chengfeng.xiaohongshu.com/api/wind/data/report", "shop_name_api_url": "https://chengfeng.xiaohongshu.com/api/wind/user/info", "shop_name_field": "nick<PERSON><PERSON>", "field_mappings": [{"field_name": "新的卖方交易订单Gmv7d", "json_path": "$..newSellerDealOrderGmv7d", "data_type": "文本", "default_value": ""}]}, "3268c8ae-7368-4ed1-b68c-f9ada041fb2d": {"platform_name": "百度", "login_url": "https://www.baidu.com/", "data_api_url": "https://www.baidu.com/", "shop_name_api_url": "https://www.baidu.com/", "shop_name_field": "shop"}}