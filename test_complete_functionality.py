#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整功能测试脚本
测试：1. 自定义页面跳转 2. 目标登录页加载 3. 按钮功能 4. <PERSON>ie获取 5. 进程清理
"""

import sys
import os
import time
import threading
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def test_complete_login_flow():
    """测试完整的登录流程"""
    print("=" * 60)
    print("完整登录功能测试")
    print("=" * 60)
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取测试账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号进行测试")
        return False
    
    account = accounts[0]
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return False
    
    account, platform = account_platform
    
    print(f"✅ 测试账号: {account.account_name}")
    print(f"✅ 测试平台: {platform.platform_name}")
    print(f"✅ 登录URL: {platform.login_url}")
    
    # 测试步骤
    test_results = {}
    
    try:
        # 步骤1: 测试自定义页面跳转
        print("\n📋 步骤1: 测试自定义页面跳转")
        print("正在启动浏览器...")
        
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        # 检查是否成功打开包装页面
        time.sleep(3)  # 等待页面加载
        
        if hasattr(cookie_service, 'driver') and cookie_service.driver:
            try:
                current_url = cookie_service.driver.current_url
                print(f"✅ 当前页面URL: {current_url}")
                
                if current_url.startswith('file://'):
                    print("✅ 成功跳转到自定义包装页面")
                    test_results['custom_page'] = True
                else:
                    print("❌ 没有跳转到自定义页面")
                    test_results['custom_page'] = False
                
                # 检查页面标题
                title = cookie_service.driver.title
                print(f"✅ 页面标题: {title}")
                
            except Exception as e:
                print(f"❌ 检查页面状态失败: {e}")
                test_results['custom_page'] = False
        else:
            print("❌ 浏览器未启动")
            test_results['custom_page'] = False
        
        # 步骤2: 测试目标登录页加载
        print("\n📋 步骤2: 测试目标登录页加载")
        
        try:
            # 检查iframe是否存在
            iframe = cookie_service.driver.find_element("css selector", ".login-iframe")
            print("✅ 找到iframe元素")
            
            # 检查iframe的src属性
            iframe_src = iframe.get_attribute('src')
            print(f"✅ iframe源地址: {iframe_src}")
            
            if platform.login_url in iframe_src or iframe_src == platform.login_url:
                print("✅ iframe正确加载目标登录页")
                test_results['iframe_loading'] = True
            else:
                print("❌ iframe没有加载正确的目标页面")
                test_results['iframe_loading'] = False
                
        except Exception as e:
            print(f"❌ 检查iframe失败: {e}")
            test_results['iframe_loading'] = False
        
        # 步骤3: 测试按钮功能
        print("\n📋 步骤3: 测试按钮功能")
        
        try:
            # 检查登录按钮是否存在
            button = cookie_service.driver.find_element("id", "loginButton")
            print("✅ 找到登录按钮")
            print(f"✅ 按钮文本: {button.text}")
            
            # 检查按钮是否可点击
            if button.is_enabled():
                print("✅ 按钮可点击")
                test_results['button_functional'] = True
            else:
                print("❌ 按钮不可点击")
                test_results['button_functional'] = False
                
        except Exception as e:
            print(f"❌ 检查按钮失败: {e}")
            test_results['button_functional'] = False
        
        # 步骤4: 用户交互测试
        print("\n📋 步骤4: 用户交互测试")
        print("请在浏览器中进行以下操作:")
        print("1. 观察iframe中是否正确显示目标登录页面")
        print("2. 尝试在iframe中进行登录操作（可以是模拟操作）")
        print("3. 点击'我已完成登录'按钮")
        print("4. 观察是否弹出确认对话框")
        
        # 等待用户操作
        input("完成上述操作后，按回车键继续...")
        
        # 步骤5: 检查Cookie获取功能
        print("\n📋 步骤5: 检查Cookie获取功能")
        
        # 检查是否有Cookie被保存
        saved_cookie = data_manager.get_cookie(account.account_id)
        if saved_cookie:
            print("✅ 成功获取并保存Cookie")
            print(f"✅ Cookie长度: {len(saved_cookie.get('cookie_str', ''))}")
            test_results['cookie_capture'] = True
        else:
            print("❌ 没有获取到Cookie")
            test_results['cookie_capture'] = False
        
        # 步骤6: 测试进程清理
        print("\n📋 步骤6: 测试进程清理")
        
        # 获取当前进程信息
        if hasattr(cookie_service, 'driver_process') and cookie_service.driver_process:
            process_pid = cookie_service.driver_process.pid
            print(f"✅ ChromeDriver进程ID: {process_pid}")
        else:
            print("❌ 没有找到ChromeDriver进程")
        
        # 关闭浏览器
        print("正在关闭浏览器...")
        cookie_service.force_close_browser()
        
        # 检查进程是否被清理
        time.sleep(2)
        
        if hasattr(cookie_service, 'driver_process') and cookie_service.driver_process:
            try:
                if cookie_service.driver_process.is_running():
                    print("❌ ChromeDriver进程仍在运行")
                    test_results['process_cleanup'] = False
                else:
                    print("✅ ChromeDriver进程已正确关闭")
                    test_results['process_cleanup'] = True
            except:
                print("✅ ChromeDriver进程已正确关闭")
                test_results['process_cleanup'] = True
        else:
            print("✅ 进程引用已清理")
            test_results['process_cleanup'] = True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        test_display_name = {
            'custom_page': '自定义页面跳转',
            'iframe_loading': '目标登录页加载',
            'button_functional': '按钮功能',
            'cookie_capture': 'Cookie获取',
            'process_cleanup': '进程清理'
        }.get(test_name, test_name)
        
        print(f"{status} {test_display_name}")
    
    print(f"\n总计: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有功能测试通过！")
        return True
    else:
        print("⚠️  部分功能需要改进")
        return False


def test_browser_process_management():
    """专门测试浏览器进程管理"""
    print("\n" + "=" * 60)
    print("浏览器进程管理测试")
    print("=" * 60)
    
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("❌ 没有可用的账号")
        return False
    
    account = accounts[0]
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("❌ 账号没有关联的平台")
        return False
    
    account, platform = account_platform
    
    try:
        print("启动浏览器...")
        cookie_service.open_incognito_browser(
            platform.login_url,
            account.account_id,
            callback=None
        )
        
        time.sleep(3)
        
        # 检查进程
        if hasattr(cookie_service, 'driver_process') and cookie_service.driver_process:
            pid = cookie_service.driver_process.pid
            print(f"✅ ChromeDriver进程启动，PID: {pid}")
            
            # 检查子进程
            try:
                children = cookie_service.driver_process.children(recursive=True)
                print(f"✅ 发现 {len(children)} 个子进程")
                for child in children:
                    print(f"   子进程 PID: {child.pid}, 名称: {child.name()}")
            except Exception as e:
                print(f"检查子进程时出错: {e}")
        
        print("\n测试强制关闭...")
        cookie_service.force_close_browser()
        
        time.sleep(2)
        
        # 验证进程是否被清理
        print("验证进程清理...")
        
        # 检查driver对象
        if cookie_service.driver is None:
            print("✅ driver对象已清理")
        else:
            print("❌ driver对象未清理")
        
        # 检查进程引用
        if cookie_service.driver_process is None:
            print("✅ 进程引用已清理")
        else:
            print("❌ 进程引用未清理")
        
        print("✅ 浏览器进程管理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 进程管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("完整功能测试工具")
    print("测试内容:")
    print("1. 点击浏览器登录按钮时跳转到自定义页面")
    print("2. 自定义页面中'我已完成登录'按钮功能正常")
    print("3. 下方目标登录页加载正常")
    print("4. 能进行完成登录操作")
    print("5. 点击按钮时获取Cookie的判断")
    print("6. 关闭浏览器时关闭ChromeDriver服务进程")
    
    try:
        # 主要功能测试
        main_test_result = test_complete_login_flow()
        
        # 进程管理测试
        process_test_result = test_browser_process_management()
        
        print("\n" + "=" * 60)
        print("最终测试结果")
        print("=" * 60)
        
        if main_test_result and process_test_result:
            print("🎉 所有功能完全正常！")
            print("✅ 自定义页面跳转正常")
            print("✅ 目标登录页加载正常")
            print("✅ 按钮功能正常")
            print("✅ Cookie获取正常")
            print("✅ 进程清理正常")
        else:
            print("⚠️  部分功能需要检查")
            if not main_test_result:
                print("❌ 主要功能测试未完全通过")
            if not process_test_result:
                print("❌ 进程管理测试未通过")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
