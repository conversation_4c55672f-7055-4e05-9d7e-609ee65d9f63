#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程管理测试脚本
"""

import sys
import os
import time
import psutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.cookie_service import CookieService
from src.utils.data_manager import DataManager


def test_psutil_availability():
    """测试psutil可用性"""
    print("测试psutil可用性...")
    
    try:
        # 测试基本功能
        current_process = psutil.Process()
        print(f"✓ 当前进程PID: {current_process.pid}")
        print(f"✓ 当前进程名称: {current_process.name()}")
        
        # 测试进程列表
        process_count = len(list(psutil.process_iter()))
        print(f"✓ 系统进程数量: {process_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ psutil测试失败: {e}")
        return False


def test_chrome_process_detection():
    """测试Chrome进程检测"""
    print("\n测试Chrome进程检测...")
    
    try:
        chrome_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    chrome_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        print(f"✓ 检测到 {len(chrome_processes)} 个Chrome相关进程")
        
        # 显示前5个进程信息
        for i, proc_info in enumerate(chrome_processes[:5]):
            print(f"  - PID: {proc_info['pid']}, 名称: {proc_info['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chrome进程检测失败: {e}")
        return False


def test_cookie_service_cleanup():
    """测试Cookie服务清理功能"""
    print("\n测试Cookie服务清理功能...")
    
    try:
        data_manager = DataManager()
        data_manager.init_data_files()
        
        cookie_service = CookieService(data_manager)
        
        # 测试清理方法是否存在
        assert hasattr(cookie_service, 'force_close_browser'), "force_close_browser方法不存在"
        assert hasattr(cookie_service, 'cleanup_chrome_processes'), "cleanup_chrome_processes方法不存在"
        assert hasattr(cookie_service, 'stop_cookie_monitoring'), "stop_cookie_monitoring方法不存在"
        
        print("✓ 所有清理方法都存在")
        
        # 测试清理方法调用（不会实际启动浏览器）
        cookie_service.cleanup_chrome_processes()
        print("✓ cleanup_chrome_processes方法调用成功")
        
        cookie_service.force_close_browser()
        print("✓ force_close_browser方法调用成功")
        
        cookie_service.stop_cookie_monitoring()
        print("✓ stop_cookie_monitoring方法调用成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Cookie服务清理功能测试失败: {e}")
        return False


def test_signal_handling():
    """测试信号处理"""
    print("\n测试信号处理...")
    
    try:
        import signal
        
        # 测试信号常量是否可用
        assert hasattr(signal, 'SIGINT'), "SIGINT信号不可用"
        assert hasattr(signal, 'SIGTERM'), "SIGTERM信号不可用"
        
        print("✓ 信号常量可用")
        
        # 测试信号处理器注册
        def test_handler(signum, frame):
            print(f"接收到测试信号: {signum}")
        
        original_handler = signal.signal(signal.SIGTERM, test_handler)
        print("✓ 信号处理器注册成功")
        
        # 恢复原始处理器
        signal.signal(signal.SIGTERM, original_handler)
        print("✓ 信号处理器恢复成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 信号处理测试失败: {e}")
        return False


def test_resource_cleanup_simulation():
    """模拟资源清理测试"""
    print("\n模拟资源清理测试...")
    
    try:
        # 模拟创建一些"资源"
        test_processes = []
        
        # 创建一个简单的子进程来模拟浏览器进程
        import subprocess
        proc = subprocess.Popen(['sleep', '1'])
        test_processes.append(proc)
        
        print(f"✓ 创建测试进程 PID: {proc.pid}")
        
        # 使用psutil获取进程信息
        psutil_proc = psutil.Process(proc.pid)
        print(f"✓ psutil进程对象创建成功: {psutil_proc.name()}")
        
        # 模拟清理过程
        if psutil_proc.is_running():
            psutil_proc.terminate()
            psutil_proc.wait(timeout=3)
            print("✓ 进程正常终止")
        
        return True
        
    except Exception as e:
        print(f"❌ 资源清理模拟测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 进程管理功能测试")
    print("=" * 50)
    
    tests = [
        test_psutil_availability,
        test_chrome_process_detection,
        test_cookie_service_cleanup,
        test_signal_handling,
        test_resource_cleanup_simulation
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 出错: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有进程管理功能测试通过！")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
    
    return failed == 0


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
