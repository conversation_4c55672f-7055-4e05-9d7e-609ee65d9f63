#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接数据抓取功能测试
验证删除Cookie验证后的直接抓取流程
"""

import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scrape_account_flow():
    """测试单个账号抓取流程"""
    print("=" * 60)
    print("单个账号抓取流程测试")
    print("=" * 60)
    
    try:
        from src.gui.account_list_tab import AccountListTab
        from src.utils.data_manager import DataManager
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 初始化组件
        data_manager = DataManager()
        account_list_tab = AccountListTab(data_manager)
        
        # 模拟账号ID
        test_account_id = "test_account_123"
        
        print(f"测试账号ID: {test_account_id}")
        
        # 模拟各种依赖
        with patch.object(account_list_tab, 'account_service') as mock_account_service, \
             patch.object(account_list_tab, 'status_manager') as mock_status_manager, \
             patch('src.gui.account_list_tab.DateRangeDialog') as mock_dialog_class, \
             patch('src.gui.account_list_tab.QProgressDialog') as mock_progress_class, \
             patch('src.gui.account_list_tab.ScrapeWorker') as mock_worker_class:
            
            # 设置模拟返回值
            mock_account_service.get_account_with_platform.return_value = (
                Mock(account_id=test_account_id, account_name="Test Account"),
                Mock(platform_id="test_platform", platform_name="Test Platform")
            )
            
            # 模拟日期选择对话框
            mock_dialog = Mock()
            mock_dialog.exec_.return_value = 1  # QDialog.Accepted
            mock_dialog.get_date_range.return_value = ("2024-01-01", "2024-01-02")
            mock_dialog_class.return_value = mock_dialog
            
            # 模拟进度对话框
            mock_progress = Mock()
            mock_progress_class.return_value = mock_progress
            
            # 模拟工作线程
            mock_worker = Mock()
            mock_worker_class.return_value = mock_worker
            
            # 测试场景1: 正常流程
            print("\n--- 测试场景1: 正常抓取流程 ---")
            
            # 调用抓取方法
            account_list_tab.scrape_account(test_account_id)
            
            # 验证调用
            print(f"get_account_with_platform 被调用: {mock_account_service.get_account_with_platform.called}")
            print(f"DateRangeDialog 被创建: {mock_dialog_class.called}")
            print(f"日期选择对话框被显示: {mock_dialog.exec_.called}")
            print(f"获取日期范围: {mock_dialog.get_date_range.called}")
            print(f"开始抓取状态被设置: {mock_status_manager.start_scraping.called}")
            print(f"ScrapeWorker 被创建: {mock_worker_class.called}")
            print(f"工作线程被启动: {mock_worker.start.called}")
            
            # 检查工作线程创建参数
            if mock_worker_class.called:
                call_args = mock_worker_class.call_args
                args, kwargs = call_args
                print(f"ScrapeWorker 创建参数: {args}")
                if len(args) >= 4:
                    print(f"  - data_scraper: {args[0]}")
                    print(f"  - account_id: {args[1]}")
                    print(f"  - start_date: {args[2]}")
                    print(f"  - end_date: {args[3]}")
                    
                    if args[1] == test_account_id and args[2] == "2024-01-01" and args[3] == "2024-01-02":
                        print("✅ 工作线程参数正确")
                    else:
                        print("❌ 工作线程参数错误")
            
            # 测试场景2: 用户取消日期选择
            print("\n--- 测试场景2: 用户取消日期选择 ---")
            
            # 重置模拟对象
            for mock_obj in [mock_account_service, mock_status_manager, mock_dialog_class, mock_progress_class, mock_worker_class]:
                mock_obj.reset_mock()
            
            # 模拟用户取消
            mock_dialog.exec_.return_value = 0  # QDialog.Rejected
            
            # 调用抓取方法
            account_list_tab.scrape_account(test_account_id)
            
            # 验证调用
            print(f"日期选择对话框被显示: {mock_dialog.exec_.called}")
            print(f"ScrapeWorker 被创建: {mock_worker_class.called}")
            
            if not mock_worker_class.called:
                print("✅ 用户取消后正确停止流程")
            else:
                print("❌ 用户取消后仍然继续流程")
        
        return True
        
    except Exception as e:
        print(f"❌ 单个账号抓取流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_batch_scrape_flow():
    """测试批量抓取流程"""
    print("=" * 60)
    print("批量抓取流程测试")
    print("=" * 60)
    
    try:
        from src.gui.account_list_tab import AccountListTab
        from src.utils.data_manager import DataManager
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 初始化组件
        data_manager = DataManager()
        account_list_tab = AccountListTab(data_manager)
        
        print("测试批量抓取流程")
        
        # 模拟各种依赖
        with patch.object(account_list_tab, 'account_service') as mock_account_service, \
             patch('src.gui.account_list_tab.DateRangeDialog') as mock_dialog_class, \
             patch('src.gui.account_list_tab.QProgressDialog') as mock_progress_class, \
             patch('src.gui.account_list_tab.BatchScrapeWorker') as mock_worker_class, \
             patch('src.gui.account_list_tab.QMessageBox') as mock_msgbox:
            
            # 设置模拟返回值
            mock_accounts = [
                Mock(account_id="account1", account_name="Account 1"),
                Mock(account_id="account2", account_name="Account 2"),
                Mock(account_id="account3", account_name="Account 3")
            ]
            mock_account_service.get_all_accounts.return_value = mock_accounts
            
            # 模拟日期选择对话框
            mock_dialog = Mock()
            mock_dialog.exec_.return_value = 1  # QDialog.Accepted
            mock_dialog.get_date_range.return_value = ("2024-01-01", "2024-01-02")
            mock_dialog_class.return_value = mock_dialog
            
            # 模拟进度对话框
            mock_progress = Mock()
            mock_progress_class.return_value = mock_progress
            
            # 模拟工作线程
            mock_worker = Mock()
            mock_worker_class.return_value = mock_worker
            
            # 测试场景1: 正常批量抓取流程
            print("\n--- 测试场景1: 正常批量抓取流程 ---")
            
            # 调用批量抓取方法
            account_list_tab.batch_scrape()
            
            # 验证调用
            print(f"get_all_accounts 被调用: {mock_account_service.get_all_accounts.called}")
            print(f"DateRangeDialog 被创建: {mock_dialog_class.called}")
            print(f"日期选择对话框被显示: {mock_dialog.exec_.called}")
            print(f"获取日期范围: {mock_dialog.get_date_range.called}")
            print(f"BatchScrapeWorker 被创建: {mock_worker_class.called}")
            print(f"工作线程被启动: {mock_worker.start.called}")
            
            # 检查工作线程创建参数
            if mock_worker_class.called:
                call_args = mock_worker_class.call_args
                args, kwargs = call_args
                print(f"BatchScrapeWorker 创建参数: {args}")
                if len(args) >= 3:
                    print(f"  - data_scraper: {args[0]}")
                    print(f"  - start_date: {args[1]}")
                    print(f"  - end_date: {args[2]}")
                    
                    if args[1] == "2024-01-01" and args[2] == "2024-01-02":
                        print("✅ 批量工作线程参数正确")
                    else:
                        print("❌ 批量工作线程参数错误")
            
            # 测试场景2: 没有账号
            print("\n--- 测试场景2: 没有账号 ---")
            
            # 重置模拟对象
            for mock_obj in [mock_account_service, mock_dialog_class, mock_progress_class, mock_worker_class]:
                mock_obj.reset_mock()
            
            # 模拟没有账号
            mock_account_service.get_all_accounts.return_value = []
            
            # 调用批量抓取方法
            account_list_tab.batch_scrape()
            
            # 验证调用
            print(f"get_all_accounts 被调用: {mock_account_service.get_all_accounts.called}")
            print(f"警告消息被显示: {mock_msgbox.warning.called}")
            print(f"BatchScrapeWorker 被创建: {mock_worker_class.called}")
            
            if not mock_worker_class.called:
                print("✅ 没有账号时正确停止流程")
            else:
                print("❌ 没有账号时仍然继续流程")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量抓取流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理"""
    print("=" * 60)
    print("错误处理测试")
    print("=" * 60)
    
    try:
        from src.gui.account_list_tab import AccountListTab
        from src.utils.data_manager import DataManager
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 初始化组件
        data_manager = DataManager()
        account_list_tab = AccountListTab(data_manager)
        
        # 模拟账号ID
        test_account_id = "test_account_123"
        
        print(f"测试账号ID: {test_account_id}")
        
        # 模拟各种依赖
        with patch.object(account_list_tab, 'data_manager') as mock_data_manager, \
             patch.object(account_list_tab, 'status_manager') as mock_status_manager, \
             patch.object(account_list_tab, 'refresh_accounts') as mock_refresh, \
             patch('src.gui.account_list_tab.QMessageBox') as mock_msgbox:
            
            # 测试场景1: 抓取失败处理
            print("\n--- 测试场景1: 抓取失败处理 ---")
            
            # 模拟抓取失败结果
            failed_result = {
                "success": False,
                "error": "Cookie已过期",
                "account_id": test_account_id
            }
            
            # 模拟进度对话框
            mock_progress = Mock()
            
            # 调用失败处理方法
            account_list_tab.on_scrape_finished(failed_result, mock_progress, test_account_id)
            
            # 验证调用
            print(f"停止抓取状态: {mock_status_manager.stop_scraping.called}")
            print(f"清除Cookie: {mock_data_manager.clear_cookie.called}")
            print(f"刷新账号列表: {mock_refresh.called}")
            print(f"显示错误消息: {mock_msgbox.critical.called}")
            
            if mock_data_manager.clear_cookie.called:
                call_args = mock_data_manager.clear_cookie.call_args
                if call_args[0][0] == test_account_id:
                    print("✅ 正确清除了指定账号的Cookie")
                else:
                    print("❌ 清除Cookie的账号ID不正确")
            
            # 测试场景2: 抓取错误处理
            print("\n--- 测试场景2: 抓取错误处理 ---")
            
            # 重置模拟对象
            for mock_obj in [mock_data_manager, mock_status_manager, mock_refresh, mock_msgbox]:
                mock_obj.reset_mock()
            
            # 调用错误处理方法
            account_list_tab.on_scrape_error("网络连接错误", mock_progress, test_account_id)
            
            # 验证调用
            print(f"停止抓取状态: {mock_status_manager.stop_scraping.called}")
            print(f"清除Cookie: {mock_data_manager.clear_cookie.called}")
            print(f"刷新账号列表: {mock_refresh.called}")
            print(f"显示错误消息: {mock_msgbox.critical.called}")
            
            if mock_data_manager.clear_cookie.called:
                call_args = mock_data_manager.clear_cookie.call_args
                if call_args[0][0] == test_account_id:
                    print("✅ 正确清除了指定账号的Cookie")
                else:
                    print("❌ 清除Cookie的账号ID不正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("直接数据抓取功能测试开始...")
    print()
    
    # 运行所有测试
    test_results = []
    
    # 单个账号抓取流程测试
    scrape_result = test_scrape_account_flow()
    test_results.append(("单个账号抓取流程", scrape_result))
    print()
    
    # 批量抓取流程测试
    batch_result = test_batch_scrape_flow()
    test_results.append(("批量抓取流程", batch_result))
    print()
    
    # 错误处理测试
    error_result = test_error_handling()
    test_results.append(("错误处理", error_result))
    print()
    
    # 总结
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有测试通过！直接数据抓取功能修改成功")
        print()
        print("功能确认:")
        print("1. ✅ 删除了Cookie预验证步骤")
        print("2. ✅ 直接通过时间选择进入数据抓取")
        print("3. ✅ 根据抓取结果判断登录状态")
        print("4. ✅ 抓取失败时自动清除登录状态")
        print("5. ✅ 错误处理机制完善")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    print()
    print("新的操作流程:")
    print("1. 用户点击'开始采集'或'全部采集'按钮")
    print("2. 弹出时间选择对话框")
    print("3. 用户选择时间范围并确认")
    print("4. 直接调用数据抓取接口")
    print("5. 如果接口成功返回数据 → 进行导出流程")
    print("6. 如果接口报错 → 清除登录状态，提示重新登录")


if __name__ == "__main__":
    main()
