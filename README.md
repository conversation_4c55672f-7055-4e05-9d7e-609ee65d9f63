# 多平台运营数据抓取系统

一个基于PyQt5的桌面应用程序，支持多平台运营数据的自动化抓取和Excel导出。

## 功能特性

- 🔧 **平台配置管理** - 支持添加、编辑、删除平台配置
- 👤 **账号管理** - 支持多账号管理，账号与平台关联
- 🍪 **多种Cookie获取方式** - 支持浏览器自动获取和手动设置Cookie
- 📊 **数据抓取** - 支持单账号和批量数据抓取
- 📈 **Excel导出** - 自动生成格式化的Excel报表
- 🎯 **小红书千帆支持** - 预配置小红书千帆平台，开箱即用
- 🎨 **现代化界面** - 运营数据采集器界面设计，操作直观
- 📋 **账号详情查看** - 查看账号详细信息和Cookie状态

## 系统要求

- **操作系统**: Windows 10/11 (64位) 或 macOS 10.15+ (64位)
- **内存**: 最低 4GB
- **磁盘空间**: 100MB 可用空间
- **Python**: 3.9+ (开发环境)

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
python main.py
```

### 3. 使用流程

1. **配置平台** (可选，小红书千帆已预配置)

   - 点击"平台配置"标签页
   - 添加新平台或编辑现有平台
2. **添加账号**

   - 点击"添加账号"标签页
   - 输入账号名称，选择关联平台
3. **设置Cookie（两种方式）**

   **方式一：手动设置Cookie**

   - 在"仪表盘"中点击"设置Cookie"按钮
   - 在浏览器中登录平台，复制Cookie字符串
   - 粘贴到Cookie输入框中并保存

   **方式二：浏览器自动获取**

   - 点击"浏览器登录"按钮
   - 在打开的浏览器中完成登录
   - 系统自动监控并获取Cookie
4. **抓取数据**

   - 点击"开始采集"按钮进行单账号抓取
   - 点击"全部抓取"进行批量抓取
   - 选择时间范围，系统自动生成Excel文件
   - 点击"详情"查看账号详细信息

## 项目结构

```
operational_data_capture/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── test_system.py         # 系统测试脚本
├── data/                  # 数据存储目录
│   ├── accounts.json      # 账号信息
│   ├── platforms.json     # 平台配置
│   └── cookies.json       # Cookie存储
└── src/                   # 源代码目录
    ├── gui/               # GUI界面模块
    ├── services/          # 业务服务模块
    ├── models/            # 数据模型
    └── utils/             # 工具模块
```

## 小红书千帆数据说明

系统预配置了小红书千帆平台，支持抓取以下数据：

### 商家经营数据总览

- 支付金额、订单数、买家数
- 商品访客数、加购数据
- 支付转化率、退款数据
- 按渠道分类（笔记、直播、商卡）

### 商家经营数据趋势

- 按日期展示各项指标趋势
- 支持自定义时间范围查询

## 开发说明

### 运行测试

```bash
python test_system.py
```

### 打包应用

使用提供的构建脚本：

```bash
python build.py
```

或手动使用PyInstaller：

**Windows:**

```bash
pyinstaller --onefile --windowed --name="小红书数据采集器" main.py
```

**macOS:**

```bash
pyinstaller --onefile --windowed --name="小红书数据采集器" main.py
```

## 技术栈

- **GUI框架**: PyQt5
- **网络请求**: requests
- **Excel处理**: openpyxl
- **数据提取**: jsonpath-ng
- **打包工具**: PyInstaller

## 注意事项

1. **Cookie安全**: Cookie信息存储在本地，请确保设备安全
2. **网络环境**: 需要稳定的网络连接进行数据抓取
3. **浏览器兼容**: 目前支持Chrome浏览器的Cookie获取
4. **数据格式**: 导出的Excel文件包含原始数据和格式化数据

## 故障排除

### 常见问题

**Q: 无法获取Cookie？**
A:

- 浏览器自动获取：确保Chrome浏览器已安装，并在登录后等待几秒钟让系统检测Cookie
- 手动设置：按F12打开开发者工具，在Network标签页中找到请求的Cookie字段

**Q: 数据抓取失败？**
A: 检查网络连接，确认Cookie有效性，验证平台API地址是否正确。

**Q: Excel导出失败？**
A: 确保目标文件夹有写入权限，关闭可能占用文件的Excel程序。

**Q: Cookie设置后仍显示未登录？**
A: 检查Cookie格式是否正确，确保包含必要的认证字段（如sessionid、token等）。

## 许可证

本项目仅供学习和研究使用。

## 更新日志

### v1.2.1 (2025-07-10)
- 🔧 **重要修复：浏览器进程管理和内存泄漏**
- 🛡️ **稳定性提升：修复NSInternalInconsistencyException**
- 🧹 **资源清理：强制清理残留的Chrome进程**
- ⚡ **性能优化：防止内存泄漏和资源浪费**
- 🔄 **信号处理：优化应用程序退出机制**

### v1.2.0 (2025-07-09)
- 🚀 **重大更新：引入selenium无痕浏览器登录**
- 📊 **动态状态管理：Agent状态实时显示采集/登录/运行状态**
- 🔄 **智能Cookie监控：自动检测登录成功并获取Cookie**
- ⏱️ **运行时间统计：显示应用运行时长**
- 🎯 **状态同步：登录和抓取状态实时更新**
- 🛡️ **隐私保护：无痕模式登录，保护用户隐私**

### v1.1.0 (2025-07-09)

- 🎨 重构GUI界面，采用现代化设计风格
- 🍪 新增手动设置Cookie功能
- 📋 新增账号详情查看功能
- 🔧 优化浏览器Cookie获取机制
- 📊 改进仪表盘统计信息显示
- 🎯 优化按钮布局和交互体验

### v1.0.0 (2025-07-09)

- 初始版本发布
- 支持小红书千帆数据抓取
- 实现基础的平台和账号管理功能
- 支持Excel数据导出
