#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射功能最终验证脚本
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.platform_service import PlatformService
from src.services.data_scraper import DataScraper


def test_complete_field_mapping_workflow():
    """测试完整的字段映射工作流程"""
    print("🔍 测试完整的字段映射工作流程...")
    
    try:
        # 1. 初始化服务
        data_manager = DataManager()
        data_manager.init_data_files()
        platform_service = PlatformService(data_manager)
        scraper = DataScraper(data_manager)
        
        # 2. 获取测试平台
        platforms = platform_service.get_all_platforms()
        if not platforms:
            print("⚠️  没有可用的平台进行测试")
            return True
        
        test_platform = platforms[0]
        platform_id = test_platform.platform_id
        
        print(f"✅ 使用平台: {test_platform.platform_name}")
        
        # 3. 创建字段映射配置
        field_mappings = [
            {
                "field_name": "内容标题",
                "json_path": "$.data.list[*].title",
                "data_type": "文本",
                "default_value": "无标题"
            },
            {
                "field_name": "浏览次数",
                "json_path": "$.data.list[*].view_count",
                "data_type": "数字",
                "default_value": "0"
            },
            {
                "field_name": "发布时间",
                "json_path": "$.data.list[*].publish_time",
                "data_type": "日期",
                "default_value": ""
            }
        ]
        
        # 4. 保存字段映射配置
        platforms_data = data_manager.get_platforms()
        platforms_data[platform_id]["field_mappings"] = field_mappings
        data_manager.save_platforms(platforms_data)
        
        print("✅ 字段映射配置保存成功")
        
        # 5. 验证配置保存
        updated_platform = platform_service.get_platform_by_id(platform_id)
        assert updated_platform.field_mappings is not None, "字段映射配置未保存"
        assert len(updated_platform.field_mappings) == 3, "字段映射数量不正确"
        
        print("✅ 字段映射配置验证成功")
        
        # 6. 测试数据提取
        test_data = {
            "code": 200,
            "data": {
                "list": [
                    {
                        "title": "测试内容1",
                        "view_count": 1500,
                        "publish_time": "2025-01-01",
                        "extra_data": "不需要的数据"
                    },
                    {
                        "title": "测试内容2",
                        "view_count": 2500,
                        "publish_time": "2025-01-02",
                        "extra_data": "更多不需要的数据"
                    }
                ]
            }
        }
        
        # 7. 使用字段映射提取数据
        extracted_data = scraper.extract_data_with_jsonpath(
            test_data, 
            updated_platform.extract_rule, 
            updated_platform.field_mappings
        )
        
        print(f"✅ 提取到 {len(extracted_data)} 条记录")
        
        # 8. 验证提取结果
        assert len(extracted_data) == 2, "提取记录数量不正确"
        
        first_record = extracted_data[0]
        expected_fields = {"内容标题", "浏览次数", "发布时间"}
        actual_fields = set(first_record.keys())
        
        assert actual_fields == expected_fields, f"提取字段不匹配: {actual_fields} != {expected_fields}"
        assert first_record["内容标题"] == "测试内容1", "标题字段值不正确"
        assert first_record["浏览次数"] == 1500, "浏览次数字段值不正确"
        assert first_record["发布时间"] == "2025-01-01", "发布时间字段值不正确"
        
        print("✅ 数据提取结果验证成功")
        
        # 9. 清理测试数据
        platforms_data[platform_id].pop("field_mappings", None)
        data_manager.save_platforms(platforms_data)
        
        print("✅ 测试数据清理完成")
        print("✅ 完整工作流程测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_components_import():
    """测试GUI组件导入"""
    print("\n🔍 测试GUI组件导入...")
    
    try:
        # 测试字段映射对话框导入
        from src.gui.field_mapping_dialog import FieldMappingDialog
        print("✅ FieldMappingDialog导入成功")
        
        # 测试平台标签页导入
        from src.gui.platform_tab import PlatformTab
        print("✅ PlatformTab导入成功")
        
        # 验证方法存在
        assert hasattr(PlatformTab, 'configure_fields'), "configure_fields方法不存在"
        assert hasattr(PlatformTab, 'save_field_mappings'), "save_field_mappings方法不存在"
        
        print("✅ GUI组件方法验证成功")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI组件导入测试失败: {e}")
        return False


def test_data_type_conversion_edge_cases():
    """测试数据类型转换的边界情况"""
    print("\n🔍 测试数据类型转换边界情况...")
    
    try:
        data_manager = DataManager()
        scraper = DataScraper(data_manager)
        
        # 测试数字转换的边界情况
        assert scraper.convert_data_type("", "数字") == ""
        assert scraper.convert_data_type(None, "数字") == ""
        assert scraper.convert_data_type("abc123def", "数字") == 123
        # 多个小数点的情况应该返回合理的数值或0
        result = scraper.convert_data_type("12.34.56", "数字")
        assert isinstance(result, (int, float)) or result == 0, f"多小数点转换结果不正确: {result}"
        
        # 测试日期转换的边界情况
        assert scraper.convert_data_type("", "日期") == ""
        assert scraper.convert_data_type("invalid-date", "日期") == "invalid-date"
        assert scraper.convert_data_type("2025-12-31 23:59:59", "日期") == "2025-12-31"
        
        # 测试文本转换的边界情况
        assert scraper.convert_data_type(None, "文本") == ""
        assert scraper.convert_data_type([], "文本") == "[]"
        assert scraper.convert_data_type({}, "文本") == "{}"
        
        print("✅ 数据类型转换边界情况测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据类型转换边界情况测试失败: {e}")
        return False


def test_field_mapping_performance():
    """测试字段映射性能"""
    print("\n🔍 测试字段映射性能...")
    
    try:
        data_manager = DataManager()
        scraper = DataScraper(data_manager)
        
        # 创建大量测试数据
        large_test_data = {
            "data": {
                "list": []
            }
        }
        
        # 生成1000条测试记录
        for i in range(1000):
            record = {
                "id": i,
                "title": f"测试标题{i}",
                "content": f"测试内容{i}" * 100,  # 长内容
                "view_count": i * 10,
                "like_count": i * 5,
                "comment_count": i * 2,
                "extra_field_1": f"额外数据1_{i}",
                "extra_field_2": f"额外数据2_{i}",
                "extra_field_3": f"额外数据3_{i}",
                "nested": {
                    "deep": {
                        "value": f"深层数据{i}"
                    }
                }
            }
            large_test_data["data"]["list"].append(record)
        
        # 字段映射配置（只提取3个字段）
        field_mappings = [
            {
                "field_name": "标题",
                "json_path": "$.data.list[*].title",
                "data_type": "文本",
                "default_value": ""
            },
            {
                "field_name": "浏览量",
                "json_path": "$.data.list[*].view_count",
                "data_type": "数字",
                "default_value": "0"
            },
            {
                "field_name": "点赞数",
                "json_path": "$.data.list[*].like_count",
                "data_type": "数字",
                "default_value": "0"
            }
        ]
        
        import time
        
        # 测试字段映射提取性能
        start_time = time.time()
        field_mapping_result = scraper.extract_data_with_field_mappings(
            large_test_data, field_mappings
        )
        field_mapping_time = time.time() - start_time
        
        # 测试传统提取性能
        start_time = time.time()
        traditional_result = scraper.extract_data_with_traditional_jsonpath(
            large_test_data, "$.data.list[*]"
        )
        traditional_time = time.time() - start_time
        
        print(f"✅ 字段映射提取: {len(field_mapping_result)} 条记录，耗时 {field_mapping_time:.3f}s")
        print(f"✅ 传统提取: {len(traditional_result)} 条记录，耗时 {traditional_time:.3f}s")
        
        # 验证数据量差异
        if field_mapping_result and traditional_result:
            field_mapping_fields = len(field_mapping_result[0])
            traditional_fields = len(traditional_result[0])
            
            print(f"✅ 字段映射提取字段数: {field_mapping_fields}")
            print(f"✅ 传统提取字段数: {traditional_fields}")
            print(f"✅ 字段减少比例: {(1 - field_mapping_fields/traditional_fields)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 字段映射性能测试失败: {e}")
        return False


def main():
    """主验证函数"""
    print("🎯 字段映射功能最终验证")
    print("版本: v1.3.1")
    print("=" * 60)
    
    tests = [
        ("完整字段映射工作流程", test_complete_field_mapping_workflow),
        ("GUI组件导入", test_gui_components_import),
        ("数据类型转换边界情况", test_data_type_conversion_edge_cases),
        ("字段映射性能", test_field_mapping_performance),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 验证结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有字段映射功能验证通过！")
        print("\n✨ 功能总结:")
        print("  ✅ 精确字段提取功能完全正常")
        print("  ✅ 字段映射配置保存修复完成")
        print("  ✅ 数据类型转换功能稳定")
        print("  ✅ 性能优化效果显著")
        print("  ✅ GUI界面功能完整")
        print("\n🚀 用户现在可以:")
        print("  • 配置需要提取的特定字段")
        print("  • 减少不必要的数据传输")
        print("  • 提高数据处理效率")
        print("  • 自定义字段名称和数据类型")
    else:
        print("⚠️  部分功能存在问题，请检查相关代码")
    
    return failed == 0


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
