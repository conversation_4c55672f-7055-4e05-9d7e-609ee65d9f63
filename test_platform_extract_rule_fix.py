#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Platform extract_rule 属性修复测试
验证Platform对象是否正确包含extract_rule属性
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_platform_model():
    """测试Platform模型"""
    print("=" * 60)
    print("Platform模型测试")
    print("=" * 60)
    
    try:
        from src.models.platform import Platform
        
        # 测试创建Platform对象
        platform_data = {
            "platform_name": "测试平台",
            "login_url": "https://test.com/login",
            "data_api_url": "https://test.com/api/data",
            "shop_name_api_url": "https://test.com/api/shop",
            "shop_name_field": "data.name",
            "extract_rule": "$.data.list[*]",
            "field_mappings": [
                {
                    "field_name": "测试字段",
                    "json_path": "$.test",
                    "data_type": "文本",
                    "default_value": ""
                }
            ]
        }
        
        # 从字典创建Platform对象
        platform = Platform.from_dict("test_platform", platform_data)
        
        print(f"平台ID: {platform.platform_id}")
        print(f"平台名称: {platform.platform_name}")
        print(f"登录URL: {platform.login_url}")
        print(f"数据API URL: {platform.data_api_url}")
        print(f"店铺名称API URL: {platform.shop_name_api_url}")
        print(f"店铺名称字段: {platform.shop_name_field}")
        print(f"提取规则: {platform.extract_rule}")
        print(f"字段映射: {platform.field_mappings}")
        
        # 检查extract_rule属性
        if hasattr(platform, 'extract_rule'):
            print("✅ Platform对象包含extract_rule属性")
            if platform.extract_rule == "$.data.list[*]":
                print("✅ extract_rule值正确")
            else:
                print(f"❌ extract_rule值不正确: {platform.extract_rule}")
        else:
            print("❌ Platform对象缺少extract_rule属性")
            return False
        
        # 测试to_dict方法
        dict_result = platform.to_dict()
        print(f"转换为字典: {dict_result}")
        
        if "extract_rule" in dict_result:
            print("✅ to_dict方法包含extract_rule")
        else:
            print("❌ to_dict方法缺少extract_rule")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Platform模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_platform_service():
    """测试平台服务"""
    print("=" * 60)
    print("平台服务测试")
    print("=" * 60)
    
    try:
        from src.services.platform_service import PlatformService
        from src.utils.data_manager import DataManager
        
        # 初始化服务
        data_manager = DataManager()
        platform_service = PlatformService(data_manager)
        
        # 获取现有平台
        platforms = platform_service.get_all_platforms()
        print(f"获取到 {len(platforms)} 个平台")
        
        for platform in platforms:
            print(f"\n平台: {platform.platform_name}")
            print(f"  - 平台ID: {platform.platform_id}")
            print(f"  - 提取规则: {platform.extract_rule}")
            print(f"  - 字段映射: {platform.field_mappings}")
            
            # 检查extract_rule属性
            if hasattr(platform, 'extract_rule'):
                print("  ✅ 包含extract_rule属性")
            else:
                print("  ❌ 缺少extract_rule属性")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 平台服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_scraper():
    """测试数据抓取器"""
    print("=" * 60)
    print("数据抓取器测试")
    print("=" * 60)
    
    try:
        from src.services.data_scraper import DataScraper
        from src.utils.data_manager import DataManager
        
        # 初始化服务
        data_manager = DataManager()
        data_scraper = DataScraper(data_manager)
        
        # 测试extract_data_with_jsonpath方法签名
        import inspect
        method = getattr(data_scraper, 'extract_data_with_jsonpath')
        signature = inspect.signature(method)
        
        print(f"extract_data_with_jsonpath 方法签名: {signature}")
        
        # 检查参数
        params = list(signature.parameters.keys())
        expected_params = ['data', 'field_mappings']
        
        for param in expected_params:
            if param in params:
                print(f"✅ 包含参数: {param}")
            else:
                print(f"❌ 缺少参数: {param}")
                return False
        
        # 检查是否不再包含extract_rule参数
        if 'extract_rule' not in params:
            print("✅ 已移除extract_rule参数")
        else:
            print("❌ 仍然包含extract_rule参数")
            return False
        
        # 测试方法调用
        test_data = {"data": [{"id": 1, "name": "test"}]}
        test_field_mappings = [
            {
                "field_name": "ID",
                "json_path": "$.data[*].id",
                "data_type": "数字",
                "default_value": ""
            }
        ]
        
        result = data_scraper.extract_data_with_jsonpath(test_data, test_field_mappings)
        print(f"测试提取结果: {result}")
        
        if result:
            print("✅ 方法调用成功")
        else:
            print("⚠️ 方法调用返回空结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据抓取器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_platform_data_loading():
    """测试平台数据加载"""
    print("=" * 60)
    print("平台数据加载测试")
    print("=" * 60)
    
    try:
        from src.utils.data_manager import DataManager
        
        # 初始化数据管理器
        data_manager = DataManager()
        
        # 获取平台数据
        platforms_data = data_manager.get_platforms()
        print(f"加载了 {len(platforms_data)} 个平台配置")
        
        for platform_id, platform_data in platforms_data.items():
            print(f"\n平台ID: {platform_id}")
            print(f"平台名称: {platform_data.get('platform_name')}")
            print(f"提取规则: {platform_data.get('extract_rule')}")
            print(f"字段映射: {platform_data.get('field_mappings')}")
            
            # 检查是否包含extract_rule
            if 'extract_rule' in platform_data:
                print("  ✅ 数据中包含extract_rule")
            else:
                print("  ⚠️ 数据中缺少extract_rule")
        
        return True
        
    except Exception as e:
        print(f"❌ 平台数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("Platform extract_rule 属性修复测试开始...")
    print()
    
    # 运行所有测试
    test_results = []
    
    # Platform模型测试
    model_result = test_platform_model()
    test_results.append(("Platform模型", model_result))
    print()
    
    # 平台服务测试
    service_result = test_platform_service()
    test_results.append(("平台服务", service_result))
    print()
    
    # 数据抓取器测试
    scraper_result = test_data_scraper()
    test_results.append(("数据抓取器", scraper_result))
    print()
    
    # 平台数据加载测试
    loading_result = test_platform_data_loading()
    test_results.append(("平台数据加载", loading_result))
    print()
    
    # 总结
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有测试通过！Platform extract_rule 属性修复成功")
        print()
        print("修复内容:")
        print("1. ✅ Platform模型添加了extract_rule属性")
        print("2. ✅ Platform.from_dict方法支持extract_rule")
        print("3. ✅ Platform.to_dict方法包含extract_rule")
        print("4. ✅ 数据管理器支持extract_rule参数")
        print("5. ✅ 平台服务支持extract_rule参数")
        print("6. ✅ 数据抓取器方法签名已更新")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    print()
    print("修复说明:")
    print("- Platform对象现在包含extract_rule属性")
    print("- extract_data_with_jsonpath方法不再需要extract_rule参数")
    print("- 字段映射从平台配置中自动获取")
    print("- 保持了向后兼容性")


if __name__ == "__main__":
    main()
