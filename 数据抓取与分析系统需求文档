多平台运营数据抓取系统需求文档
目录
引言
1.1 文档目的
1.2 项目范围
1.3 受众
总体描述
2.1 项目背景
2.2 项目目标
2.3 运行环境
2.4 技术栈
详细功能需求
3.1 核心功能概述
3.2 GUI 功能模块
3.3 数据抓取流程
数据存储设计
4.1 存储目录结构
4.2 数据文件规范
技术实现要求
5.1 开发框架要求
5.2 关键技术点
界面设计要求
6.1 整体风格
6.2 核心页面布局
打包与部署要求
7.1 打包格式
7.2 安装流程
测试需求
8.1 功能测试
8.2 兼容性测试
8.3 性能测试
附录
9.1 术语定义
9.2 示例配置文件
1. 引言
1.1 文档目的
本文档明确多平台运营数据抓取系统的功能需求、技术规范、界面设计及部署要求，为开发、测试、运维提供统一指导，确保系统满足 “多平台数据抓取 - 本地存储 - Excel 导出” 的核心目标。
1.2 项目范围
支持 Windows（Win10 及以上）和 Mac（macOS 10.15 及以上）系统的桌面应用
功能覆盖：平台配置、账号管理、登录监控（Cookie 获取）、数据抓取（单账号 / 全账号）、Excel 导出
数据来源：基于用户配置的平台接口，通过 Cookie 认证后抓取数据
1.3 受众
开发人员、测试人员、产品维护人员、最终用户
2. 总体描述
2.1 项目背景
企业运营需从多平台（如千帆、第三方运营平台等）抓取数据进行分析，但现有工具存在 “跨平台适配差、Cookie 管理混乱、数据提取规则不灵活” 等问题。本系统旨在通过本地化桌面应用，实现 “配置简单、操作直观、数据安全” 的多平台数据抓取流程。
2.2 项目目标
提供可视化 GUI，支持非技术人员操作
自动获取并存储登录 Cookie，避免重复登录
按平台配置的规则抓取数据，支持时间范围筛选
生成标准化 Excel 文件，满足数据分析需求
2.3 运行环境
操作系统：Windows 10/11（64 位）、macOS 10.15 及以上（64 位）
硬件要求：最低 4GB 内存，100MB 空闲磁盘空间
依赖环境：系统默认浏览器（用于登录操作）
2.4 技术栈
开发语言：Python 3.9+
网络请求：requests（处理 HTTP 接口调用）
数据处理：openpyxl（生成 Excel）、jsonpath（JSON 数据提取）
GUI 框架：PyQt5（跨平台支持，需打包为桌面应用）
打包工具：PyInstaller（生成 Windows .exe、Mac .dmg 安装包）
3. 详细功能需求
3.1 核心功能概述
系统通过 “平台配置 - 账号管理 - 登录获取 Cookie - 数据抓取 - Excel 导出” 的流程，实现多平台运营数据的自动化采集，支持单账号 / 全账号批量操作。
3.2 GUI 功能模块
3.2.1 添加账号
功能描述：用于新增需抓取数据的平台账号
表单元素：
账号名称（输入框，必填，唯一标识，如 “千帆 - 运营账号 1”）
平台选择（下拉框，必填，选项来源于 “平台配置” 中的平台名称）
操作逻辑：点击 “保存” 后，账号信息存储至本地（accounts.json），并同步显示在 “账号列表”
3.2.2 平台配置
功能描述：配置平台基础信息（用于账号关联及抓取规则）
配置字段：
平台名称（输入框，必填，唯一，如 “千帆”）
登录 URL（输入框，必填，用于跳转登录页面）
数据接口 URL（输入框，必填，抓取数据的接口地址）
数据提取规则（文本框，必填，JSONPath 表达式，用于从接口返回值中提取目标数据，如 “$.data.list”）
操作逻辑：支持新增 / 编辑 / 删除平台配置，配置信息存储至本地（platforms.json）
3.2.3 账号列表
功能描述：展示所有已添加的账号，支持账号管理操作
显示信息：
账号名称
关联平台（来源于平台配置）
登录状态（“已登录”/“未登录”，基于 Cookie 有效性判断）
操作按钮：
编辑：弹窗修改账号名称 / 关联平台，保存后更新 accounts.json
开始登录：
触发逻辑：打开系统默认浏览器，跳转至该账号关联平台的 “登录 URL”
Cookie 获取：监控浏览器登录操作（通过拦截浏览器 Cookie 文件或访问验证接口），获取到有效 Cookie 后存储至本地（cookies.json），并更新登录状态为 “已登录”；若 10 分钟内未获取到有效 Cookie，状态保持 “未登录”
删除：从 accounts.json 中移除该账号，同时删除关联的 Cookie（若存在）
抓取：
触发逻辑：点击后弹出时间选择弹窗（开始时间 / 结束时间，如 “2025-07-01 至 2025-07-09”）
数据抓取：将时间参数传入该平台的 “数据接口 URL”，携带关联的 Cookie（从 cookies.json 获取）发起请求，通过平台配置的 “数据提取规则” 提取数据
导出结果：将提取的数据生成 Excel 文件（命名规则：“[平台名称]-[账号名称]-[开始时间至结束时间].xlsx”），保存至用户指定路径
3.2.4 全部抓取
功能描述：批量抓取所有 “已登录” 状态账号的数据
操作逻辑：
点击 “全部抓取” 按钮，弹出时间选择弹窗（同单个账号抓取）
对所有登录状态为 “已登录” 的账号，按以下流程批量处理：
按账号关联平台的 “数据接口 URL” 发起请求（携带关联 Cookie + 时间参数）
用平台配置的 “数据提取规则” 提取数据
每个账号生成独立 Excel 文件（命名规则同上），或按平台合并生成一个 Excel（支持用户选择）
3.3 数据抓取流程（示例）
管理员在 “平台配置” 中新增 “千帆” 平台：登录 URL 为 “https://qianfan.com/login”，数据接口 URL 为 “https://qianfan.com/api/data”，数据提取规则为 “$.result.data”
运营人员在 “添加账号” 中新增 “千帆 - 日常账号”，关联平台为 “千帆”
点击该账号的 “开始登录”，浏览器打开千帆登录页，手动输入账号密码完成登录
系统获取到 Cookie，登录状态更新为 “已登录”
点击 “抓取”，选择时间范围 “2025-07-01 至 2025-07-09”，系统携带 Cookie + 时间参数调用 “https://qianfan.com/api/data”
从接口返回值中提取 “$.result.data” 对应的数据，生成 “千帆 - 日常账号 - ******** 至 ********.xlsx”
4. 数据存储设计
4.1 存储目录结构
系统本地数据统一存储在应用安装目录下的 “data” 文件夹，结构如下：

plaintext
data/  
├─ cookies.json  # 存储所有账号的Cookie（按账号ID关联）  
├─ platforms.json  # 平台配置信息  
└─ accounts.json  # 账号信息  
4.2 数据文件规范
4.2.1 cookies.json
json
{  
  "账号ID1": {  // 账号ID为accounts.json中账号的唯一标识（如UUID）  
    "cookie_str": "sessionid=xxx; token=yyy",  // 原始Cookie字符串  
    "expire_time": "2025-08-09 12:00:00",  // Cookie过期时间（可选，用于状态判断）  
    "update_time": "2025-07-09 10:30:00"  // 最后更新时间  
  },  
  "账号ID2": {...}  
}  
4.2.2 platforms.json
json
{  
  "平台ID1": {  // 平台ID为唯一标识（如UUID）  
    "platform_name": "千帆",  // 平台名称  
    "login_url": "https://qianfan.com/login",  // 登录URL  
    "data_api_url": "https://qianfan.com/api/data",  // 数据接口URL  
    "extract_rule": "$.data.list"  // 数据提取JSONPath规则  
  },  
  "平台ID2": {...}  
}  
4.2.3 accounts.json
json
{  
  "账号ID1": {  // 唯一标识（如UUID）  
    "account_name": "千帆-运营账号1",  // 账号名称  
    "platform_id": "平台ID1",  // 关联的平台ID（关联platforms.json）  
    "create_time": "2025-07-09 09:00:00"  // 创建时间  
  },  
  "账号ID2": {...}  
}  
5. 技术实现要求
5.1 开发框架要求
GUI：使用 PyQt5 开发界面，确保 Windows/Mac 下界面风格适配系统原生样式
网络请求：requests 库处理接口调用，支持设置 header（包含 Cookie）、请求参数（时间范围等）
数据提取：通过 jsonpath 库解析接口返回的 JSON 数据，按平台配置的规则提取目标字段
Excel 生成：openpyxl 库创建 Excel 文件，支持自动生成表头（基于提取数据的字段名）和内容
5.2 关键技术点
Cookie 获取：
Windows：监控浏览器 Cookie 存储路径（如 Chrome 的 “AppData\Local\Google\Chrome\User Data\Default\Cookies”），通过 SQLite 读取目标域名的 Cookie
Mac：监控浏览器 Cookie 存储路径（如 Chrome 的 “~/Library/Application Support/Google/Chrome/Default/Cookies”），同理读取
验证逻辑：获取 Cookie 后，调用平台的 “验证接口”（如首页接口），若返回 200 且包含登录标识（如用户名），则判定为有效
时间参数传递：用户选择的时间范围需转换为接口要求的格式（如时间戳或 “yyyy-MM-dd”），作为请求参数（query 或 body）传入
错误处理：
若 Cookie 无效（接口返回 401/403），提示 “登录已失效，请重新登录”
若接口返回数据为空，提示 “该时间范围内无数据”
若 Excel 生成失败，提示 “文件被占用，请关闭后重试”
6. 界面设计要求
6.1 整体风格
简洁直观：以 “功能分区” 为核心，减少冗余元素
交互友好：操作按钮明确（如 “保存”“登录”“抓取”），状态提示清晰（如登录中 / 抓取中 / 导出成功）
6.2 核心页面布局
主页面：顶部为功能标签页（“添加账号”“平台配置”“账号列表”），默认显示 “账号列表”
“添加账号” 页：左侧表单（账号名称输入框 + 平台下拉框），右侧 “保存”“取消” 按钮
“平台配置” 页：表格展示已配置平台，顶部 “新增” 按钮（弹窗填写配置字段），表格行尾 “编辑”“删除” 按钮
“账号列表” 页：表格展示账号信息（名称、平台、登录状态），表格行尾 “编辑”“开始登录”“删除”“抓取” 按钮；顶部 “全部抓取” 按钮
弹窗设计：
时间选择弹窗：开始时间 / 结束时间选择器（日历控件）+“确认”“取消” 按钮
编辑弹窗：同 “添加账号” 表单，预填当前账号信息
7. 打包与部署要求
7.1 打包格式
Windows：通过 PyInstaller 生成单文件.exe 安装包（支持 “下一步” 式安装，自动创建桌面快捷方式）
Mac：生成.dmg 镜像文件，包含应用程序图标，支持拖拽安装至 “应用程序” 文件夹
7.2 安装流程
安装时自动创建 “data” 目录（若不存在），初始化空的 cookies.json、platforms.json、accounts.json
安装完成后，首次打开提示 “请先配置平台信息”（引导至 “平台配置” 页）
8. 附录
8.1 术语定义
Cookie：用户登录平台后，服务器颁发的身份凭证，用于后续接口调用的身份验证
JSONPath：用于从 JSON 数据中提取指定字段的语法（类似 XPath），如 “$.data [*].name” 表示提取 data 数组中所有对象的 name 字段
8.2 示例配置文件
platforms.json 示例：
json
{  
  "p1": {  
    "platform_name": "千帆",  
    "login_url": "https://qianfan.com/login",  
    "data_api_url": "https://qianfan.com/api/operation/data",  
    "extract_rule": "$.data.operationList[*]"  
  }  
}  

accounts.json 示例：
json
{  
  "a1": {  
    "account_name": "千帆-运营1号",  
    "platform_id": "p1",  
    "create_time": "2025-07-09 09:30:00"  
  }  
}  