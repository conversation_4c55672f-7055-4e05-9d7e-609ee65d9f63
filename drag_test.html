
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽遮挡测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f0f0f0; min-width: 320px; min-height: 500px; }
        .header { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; z-index: 9999; }
        .content { position: fixed; top: 60px; left: 0; right: 0; bottom: 0; background: white; overflow: auto; }
        .test-iframe { width: 100%; height: 100%; border: none; }
        .info { position: fixed; bottom: 20px; left: 20px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 4px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <div>拖拽遮挡测试 - 调整窗口大小观察效果</div>
        <button onclick="toggleInfo()" style="background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">切换信息</button>
    </div>
    
    <div class="content">
        <iframe class="test-iframe" src="https://customer.xiaohongshu.com/login?service=https://ark.xiaohongshu.com/ark"></iframe>
    </div>
    
    <div class="info" id="info">
        窗口大小: <span id="size"></span><br>
        overflow: auto (允许滚动)<br>
        拖拽测试: 正常
    </div>
    
    <script>
        function updateSize() {
            document.getElementById('size').textContent = window.innerWidth + 'x' + window.innerHeight;
        }
        
        function toggleInfo() {
            const info = document.getElementById('info');
            info.style.display = info.style.display === 'none' ? 'block' : 'none';
        }
        
        window.addEventListener('resize', updateSize);
        window.addEventListener('load', updateSize);
    </script>
</body>
</html>
    