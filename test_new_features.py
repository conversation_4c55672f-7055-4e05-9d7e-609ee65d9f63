#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.platform_service import PlatformService
from src.services.account_service import AccountService
from src.services.shop_name_service import ShopNameService
from src.models.platform import Platform
from src.models.account import Account


def test_platform_with_shop_name():
    """测试平台配置包含店铺名称字段"""
    print("=== 测试平台配置包含店铺名称字段 ===")
    
    data_manager = DataManager()
    platform_service = PlatformService(data_manager)
    
    # 获取所有平台
    platforms = platform_service.get_all_platforms()
    
    for platform in platforms:
        print(f"平台: {platform.platform_name}")
        print(f"  登录URL: {platform.login_url}")
        print(f"  数据接口URL: {platform.data_api_url}")
        print(f"  店铺名称接口URL: {platform.shop_name_api_url}")
        print(f"  店铺名称字段: {platform.shop_name_field}")
        print()


def test_account_with_shop_name():
    """测试账号包含店铺名称字段"""
    print("=== 测试账号包含店铺名称字段 ===")
    
    data_manager = DataManager()
    account_service = AccountService(data_manager)
    
    # 获取所有账号
    accounts = account_service.get_all_accounts()
    
    for account in accounts:
        print(f"账号: {account.account_name}")
        print(f"  店铺名称: {account.shop_name or '未设置'}")
        print(f"  平台ID: {account.platform_id}")
        print(f"  创建时间: {account.create_time}")
        print()


def test_shop_name_service():
    """测试店铺名称服务"""
    print("=== 测试店铺名称服务 ===")
    
    shop_name_service = ShopNameService()
    
    # 测试配置验证
    valid_config = shop_name_service.validate_shop_name_config(
        "https://example.com/api/user/info",
        "data.shopName"
    )
    print(f"有效配置验证: {valid_config}")
    
    invalid_config = shop_name_service.validate_shop_name_config(
        "invalid-url",
        ""
    )
    print(f"无效配置验证: {invalid_config}")


def test_add_platform_with_shop_name():
    """测试添加包含店铺名称配置的平台"""
    print("=== 测试添加包含店铺名称配置的平台 ===")
    
    data_manager = DataManager()
    platform_service = PlatformService(data_manager)
    
    # 添加测试平台
    platform_id = platform_service.add_platform(
        platform_name="测试平台",
        login_url="https://test.com/login",
        data_api_url="https://test.com/api/data",
        shop_name_api_url="https://test.com/api/shop",
        shop_name_field="shopName"
    )
    
    print(f"添加的平台ID: {platform_id}")
    
    # 获取添加的平台
    platform = platform_service.get_platform_by_id(platform_id)
    if platform:
        print(f"平台名称: {platform.platform_name}")
        print(f"店铺名称接口URL: {platform.shop_name_api_url}")
        print(f"店铺名称字段: {platform.shop_name_field}")
    
    # 删除测试平台
    platform_service.delete_platform(platform_id)
    print("测试平台已删除")


def test_account_with_shop_name_field():
    """测试账号的店铺名称字段"""
    print("=== 测试账号的店铺名称字段 ===")
    
    data_manager = DataManager()
    account_service = AccountService(data_manager)
    
    # 获取第一个平台ID
    platforms = data_manager.get_platforms()
    if not platforms:
        print("没有可用的平台")
        return
    
    platform_id = list(platforms.keys())[0]
    
    # 添加测试账号
    account_id = account_service.add_account("测试账号", platform_id)
    print(f"添加的账号ID: {account_id}")
    
    # 获取账号
    account = account_service.get_account_by_id(account_id)
    if account:
        print(f"账号名称: {account.account_name}")
        print(f"店铺名称: {account.shop_name or '未设置'}")
        
        # 手动设置店铺名称
        accounts_data = data_manager.get_accounts()
        accounts_data[account_id]["shop_name"] = "测试店铺"
        data_manager.save_accounts(accounts_data)
        
        # 重新获取账号
        account = account_service.get_account_by_id(account_id)
        print(f"更新后的店铺名称: {account.shop_name}")
    
    # 删除测试账号
    account_service.delete_account(account_id)
    print("测试账号已删除")


def main():
    """主函数"""
    print("开始测试新功能...")
    print()
    
    try:
        test_platform_with_shop_name()
        test_account_with_shop_name()
        test_shop_name_service()
        test_add_platform_with_shop_name()
        test_account_with_shop_name_field()
        
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
