#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按钮改进功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.data_manager import DataManager
from src.services.cookie_service import CookieService
from src.services.account_service import AccountService


def test_button_persistence_and_alerts():
    """测试按钮持久性和提示框功能"""
    print("=== 测试按钮持久性和提示框功能 ===")
    
    # 初始化服务
    data_manager = DataManager()
    cookie_service = CookieService(data_manager)
    account_service = AccountService(data_manager)
    
    # 获取第一个账号
    accounts = account_service.get_all_accounts()
    if not accounts:
        print("没有可用的账号进行测试")
        return
    
    account = accounts[0]
    print(f"使用账号: {account.account_name}")
    
    # 获取平台信息
    account_platform = account_service.get_account_with_platform(account.account_id)
    if not account_platform:
        print("账号没有关联的平台")
        return
    
    account, platform = account_platform
    print(f"平台: {platform.platform_name}")
    print(f"登录URL: {platform.login_url}")
    
    try:
        print("\n开始测试按钮改进功能...")
        print("将打开无痕浏览器，请按以下步骤测试:")
        print()
        print("🔍 按钮持久性测试:")
        print("1. 观察右上角是否出现'我已完成登录'按钮")
        print("2. 在网站内进行页面跳转（点击链接、导航等）")
        print("3. 观察按钮是否在跳转后快速重新出现（200ms内）")
        print("4. 测试多次页面跳转，确认按钮始终存在")
        print()
        print("💬 提示框功能测试:")
        print("5. 在未完成登录的情况下点击按钮")
        print("6. 应该弹出确认对话框：'请确认您已完成登录操作'")
        print("7. 点击'取消'，按钮应该保持原状")
        print("8. 点击'确定'，按钮应该变为'正在检查登录状态...'")
        print("9. 如果未登录，应该弹出提示：'未获取到登录态，请先完成登录操作后再点击按钮'")
        print("10. 完成登录后点击按钮，应该弹出：'登录成功！Cookie已获取，浏览器将在3秒后自动关闭。'")
        print()
        print("🚀 高级测试:")
        print("11. 测试浏览器前进后退按钮")
        print("12. 测试页面刷新")
        print("13. 测试单页应用路由变化（如果适用）")
        print("14. 测试页面焦点变化")
        print()
        
        # 启动Cookie获取
        cookie_service.start_incognito_login(
            account.account_id, 
            platform.login_url, 
            None  # 不需要回调
        )
        
        # 等待用户测试
        print("等待用户测试...")
        print("按 Ctrl+C 结束测试")
        
        start_time = time.time()
        
        while True:
            try:
                # 检查登录结果
                result = cookie_service.get_login_result(account.account_id)
                if result is not None:
                    success, message = result
                    print(f"\n登录结果: {'成功' if success else '失败'} - {message}")
                    
                    if success:
                        print("✅ 按钮功能测试成功！")
                        print("✅ 提示框功能正常工作！")
                        print("✅ 按钮持久性机制有效！")
                        break
                    else:
                        print(f"❌ 登录失败: {message}")
                        break
                
                # 每30秒提示一次
                if int(time.time() - start_time) % 30 == 0:
                    print(".", end="", flush=True)
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                print("\n用户中断测试")
                break
        
        # 清理
        cookie_service.force_close_browser()
        print("\n测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_button_features_guide():
    """按钮功能测试指南"""
    print("\n=== 按钮功能改进说明 ===")
    print()
    print("🔧 技术改进:")
    print("1. 按钮检查频率提升到200ms，响应更快")
    print("2. 多重监听机制：定时器 + 页面观察器 + 事件监听")
    print("3. 强化的函数注入：forceCreateLoginButton")
    print("4. 页面焦点和可见性变化监听")
    print()
    print("💬 用户体验改进:")
    print("1. 点击按钮时弹出确认对话框")
    print("2. 登录失败时弹出明确提示")
    print("3. 登录成功时弹出成功提示并自动关闭浏览器")
    print("4. 按钮状态重置机制")
    print()
    print("🛡️ 稳定性改进:")
    print("1. 浏览器窗口关闭时优雅退出")
    print("2. 错误处理机制完善")
    print("3. 内存泄漏防护")
    print("4. 多重备份机制")


def main():
    """主函数"""
    print("按钮改进功能测试工具")
    print("=" * 50)
    
    try:
        test_button_features_guide()
        test_button_persistence_and_alerts()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
