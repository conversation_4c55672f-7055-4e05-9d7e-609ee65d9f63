# 进程管理和NSInternalInconsistencyException修复

## 问题描述

1. **浏览器进程未正确关闭**：selenium启动的Chrome浏览器进程在用户关闭窗口后仍然残留在系统中，导致内存泄漏和资源浪费
2. **NSInternalInconsistencyException错误**：Qt应用程序在某些情况下出现内部一致性异常，影响应用稳定性

## 修复方案

### 1. 浏览器进程管理

#### 新增依赖
```
psutil==5.9.6  # 用于进程管理和监控
```

#### 核心修复
- **进程跟踪**：使用psutil跟踪浏览器主进程和子进程
- **强制清理**：实现多层次的进程清理机制
- **资源监控**：监控和清理可能残留的Chrome进程

#### 实现细节

**1. 进程跟踪**
```python
# 获取浏览器进程信息
self.driver_process = psutil.Process(self.driver.service.process.pid)
```

**2. 强制关闭浏览器**
```python
def force_close_browser(self):
    """强制关闭浏览器和相关进程"""
    # 1. 正常关闭WebDriver
    # 2. 终止主进程和子进程
    # 3. 清理残留的Chrome进程
```

**3. 进程清理策略**
- 首先尝试正常关闭（driver.quit()）
- 然后终止主进程和所有子进程
- 最后清理可能残留的无痕Chrome进程
- 使用超时机制避免无限等待

### 2. NSInternalInconsistencyException修复

#### 问题原因
- Qt应用程序在多线程环境下的信号处理不当
- 高DPI设置时机不正确
- 应用程序退出时资源清理不完整

#### 修复措施

**1. 信号处理优化**
```python
# 使用定时器处理信号（避免NSInternalInconsistencyException）
timer = QTimer()
timer.timeout.connect(lambda: None)
timer.start(100)
```

**2. 高DPI设置优化**
```python
# 在创建QApplication之前设置高DPI支持
QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
```

**3. 资源清理机制**
```python
# 注册信号处理器和退出清理函数
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)
atexit.register(cleanup_resources)
```

### 3. 完整的资源管理

#### 应用程序级别
- **启动时**：注册信号处理器和退出清理函数
- **运行时**：监控浏览器进程状态
- **退出时**：强制清理所有相关进程

#### 组件级别
- **CookieService**：负责浏览器进程的创建和清理
- **AccountListTab**：在登录回调中确保进程清理
- **MainWindow**：在窗口关闭时触发资源清理

## 修复效果

### ✅ 已解决的问题

1. **内存泄漏**：浏览器进程现在会被正确清理，避免内存泄漏
2. **资源浪费**：残留的Chrome进程会被自动检测和清理
3. **应用稳定性**：NSInternalInconsistencyException错误已修复
4. **用户体验**：应用程序退出更加干净，不会留下僵尸进程

### 📊 测试验证

#### 进程管理测试
- ✅ psutil功能正常
- ✅ Chrome进程检测正常
- ✅ 进程清理方法正常
- ✅ 信号处理正常
- ✅ 资源清理模拟正常

#### 应用程序测试
- ✅ 应用启动正常，无NSInternalInconsistencyException
- ✅ 浏览器登录功能正常
- ✅ 浏览器关闭检测正常
- ✅ 进程清理正常

## 使用说明

### 正常使用流程
1. 启动应用程序
2. 点击"浏览器登录"
3. 在无痕浏览器中完成登录
4. 关闭浏览器窗口或等待登录完成
5. 系统自动清理浏览器进程

### 异常情况处理
- **浏览器崩溃**：系统会自动检测并清理残留进程
- **应用程序强制退出**：注册的清理函数会自动执行
- **系统信号**：SIGINT和SIGTERM信号会触发资源清理

## 技术细节

### 进程清理顺序
1. **正常关闭**：调用driver.quit()
2. **进程终止**：使用terminate()终止进程
3. **强制杀死**：超时后使用kill()强制杀死
4. **残留清理**：扫描并清理可能的残留进程

### 错误处理
- 所有进程操作都包含异常处理
- 使用超时机制避免无限等待
- 提供降级处理方案

### 兼容性
- 支持macOS、Windows、Linux
- 兼容不同版本的Chrome浏览器
- 向后兼容原有功能

## 注意事项

1. **权限要求**：在某些系统上可能需要管理员权限来杀死进程
2. **Chrome版本**：确保Chrome浏览器版本与ChromeDriver兼容
3. **系统资源**：进程清理可能需要几秒钟时间
4. **网络环境**：首次使用时需要下载ChromeDriver

## 后续优化

1. **进程监控**：可以添加进程资源使用监控
2. **清理策略**：可以根据系统负载调整清理策略
3. **用户反馈**：可以添加进程清理状态的用户反馈
4. **日志记录**：可以添加详细的进程管理日志
