
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe加载测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; height: 100vh; overflow: hidden; }
        .header-container { position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 9999; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; }
        .header-title { color: white; font-size: 16px; font-weight: 600; }
        .test-buttons { display: flex; gap: 10px; }
        .test-button { background: #52c41a; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; }
        .test-button:hover { background: #45a017; }
        .iframe-container { position: absolute; top: 60px; left: 0; right: 0; bottom: 0; background: white; }
        .login-iframe { width: 100%; height: 100%; border: none; background: white; }
        .loading-overlay { position: absolute; top: 60px; left: 0; right: 0; bottom: 0; background: rgba(255, 255, 255, 0.9); display: flex; align-items: center; justify-content: center; z-index: 1000; }
        .loading-spinner { width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #1890ff; border-radius: 50%; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .loading-text { margin-left: 15px; color: #666; font-size: 14px; }
        .log-area { position: fixed; bottom: 20px; left: 20px; right: 20px; height: 150px; background: #000; color: #0f0; font-family: monospace; font-size: 12px; padding: 10px; border-radius: 4px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="header-container">
        <div class="header-title">iframe加载测试</div>
        <div class="test-buttons">
            <button class="test-button" onclick="loadBaidu()">加载百度</button>
            <button class="test-button" onclick="loadGoogle()">加载Google</button>
            <button class="test-button" onclick="loadXiaohongshu()">加载小红书</button>
            <button class="test-button" onclick="clearLog()">清除日志</button>
        </div>
    </div>
    
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载页面...</div>
    </div>
    
    <div class="iframe-container">
        <iframe 
            class="login-iframe" 
            id="testIframe" 
            src="about:blank"
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation-by-user-activation"
            loading="eager"
            importance="high"
            allow="camera; microphone; geolocation"
            referrerpolicy="no-referrer-when-downgrade">
        </iframe>
    </div>
    
    <div class="log-area" id="logArea">
        <div>iframe加载测试日志：</div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div>[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '<div>iframe加载测试日志：</div>';
        }
        
        function loadPage(url, name) {
            const iframe = document.getElementById('testIframe');
            const loadingOverlay = document.getElementById('loadingOverlay');
            
            log(`开始加载 ${name}: ${url}`);
            loadingOverlay.style.display = 'flex';
            
            // 设置加载超时
            const loadTimeout = setTimeout(function() {
                log(`${name} 加载超时（15秒）`);
                loadingOverlay.style.display = 'none';
            }, 15000);
            
            // 设置iframe加载事件
            iframe.onload = function() {
                log(`${name} onload事件触发`);
                clearTimeout(loadTimeout);
                
                setTimeout(function() {
                    log(`${name} 加载完成，隐藏loading`);
                    loadingOverlay.style.display = 'none';
                }, 1000);
            };
            
            iframe.onerror = function() {
                log(`${name} onerror事件触发`);
                clearTimeout(loadTimeout);
                loadingOverlay.style.display = 'none';
            };
            
            // 加载页面
            iframe.src = url;
            
            // 备用检查机制
            let checkCount = 0;
            const checkInterval = setInterval(function() {
                checkCount++;
                log(`检查 ${name} 加载状态，第 ${checkCount} 次`);
                
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc && iframeDoc.readyState === 'complete') {
                        log(`通过定时器检测到 ${name} 加载完成`);
                        clearInterval(checkInterval);
                        clearTimeout(loadTimeout);
                        loadingOverlay.style.display = 'none';
                    }
                } catch (e) {
                    log(`跨域限制，无法检查 ${name} 状态`);
                }
                
                if (checkCount >= 30) {
                    log(`${name} 检查次数达到上限，停止检查`);
                    clearInterval(checkInterval);
                }
            }, 1000);
        }
        
        function loadBaidu() {
            loadPage('https://www.baidu.com', '百度');
        }
        
        function loadGoogle() {
            loadPage('https://www.google.com', 'Google');
        }
        
        function loadXiaohongshu() {
            loadPage('https://customer.xiaohongshu.com/login?service=https://ark.xiaohongshu.com/ark', '小红书');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，可以开始测试iframe加载');
            document.getElementById('loadingOverlay').style.display = 'none';
        });
    </script>
</body>
</html>
    