小红书千帆数据结构
请求路径：https://ark.xiaohongshu.com/api/edith/butterfly/data?type=sellerCarrierChannelOverall
请求参数：{"requestBody":{"blockElements":[{"blockKey":"sellerCarrierChannelOverall","filterMap":{"dateType":0,"dateSelectType":"custom","startDate":"2025-07-04","endDate":"2025-07-05","sellerManageType":"all"}},{"blockKey":"sellerCarrierChannelTrend","filterMap":{"dateType":0,"dateSelectType":"custom","startDate":"2025-07-04","endDate":"2025-07-05","sellerManageType":"all"}}]},"type":"sellerCarrierChannelOverall"}
返回参数：{
    "success": true,
    "msg": "成功",
    "data": [
        {
            "blockKey": "sellerCarrierChannelOverall",
            "blockName": "商家经营数据总览",
            "blockType": "single_data",
            "count": 0,
            "data": [
                {
                    "goodsUv": {
                        "proportion": 1,
                        "value": 15718,
                        "ratio": 0.0159
                    },
                    "noteGoodsUv": {
                        "proportion": 0.3353,
                        "value": 5270,
                        "ratio": -0.1349
                    },
                    "cartUserNum": {
                        "proportion": 1,
                        "value": 4412,
                        "ratio": -0.0204
                    },
                    "payUserNum": {
                        "proportion": 1,
                        "value": 1120,
                        "ratio": -0.0789
                    },
                    "refundPayGmv": {
                        "proportion": 0.1237,
                        "value": 8768.03,
                        "ratio": -0.2567
                    },
                    "cardUpr": {
                        "proportion": 1,
                        "value": 0.0257,
                        "ratio": -0.292
                    },
                    "liveGoodsUv": {
                        "proportion": 0.2101,
                        "value": 3302,
                        "ratio": 0.0201
                    },
                    "cartGoodsCnt": {
                        "proportion": 1,
                        "value": 5335,
                        "ratio": -0.0211
                    },
                    "noteUpr": {
                        "proportion": 1,
                        "value": 0.0791,
                        "ratio": 0.0381
                    },
                    "liveRefundPayGmv": {
                        "proportion": 0.5244,
                        "value": 4597.72,
                        "ratio": -0.1803
                    },
                    "liveUpr": {
                        "proportion": 1,
                        "value": 0.152,
                        "ratio": -0.0238
                    },
                    "livePayUserNum": {
                        "proportion": 0.4482,
                        "value": 502,
                        "ratio": -0.004
                    },
                    "livePayGmv": {
                        "proportion": 0.4848,
                        "value": 34357.6,
                        "ratio": -7.0E-4
                    },
                    "payGoodsCnt": {
                        "proportion": 1,
                        "value": 1201,
                        "ratio": -0.2394
                    },
                    "dtm": {
                        "value": 20250705
                    },
                    "livePayPkgCnt": {
                        "proportion": 0.4459,
                        "value": 519,
                        "ratio": -0.0095
                    },
                    "noteRefundPayGmv": {
                        "proportion": 0.2903,
                        "value": 2545.47,
                        "ratio": -0.329
                    },
                    "notePayUserNum": {
                        "proportion": 0.3723,
                        "value": 417,
                        "ratio": -0.1013
                    },
                    "cardPayGmv": {
                        "proportion": 0.1932,
                        "value": 13691.9,
                        "ratio": -0.1961
                    },
                    "cardGoodsUv": {
                        "proportion": 0.5394,
                        "value": 8478,
                        "ratio": 0.1283
                    },
                    "payGmv": {
                        "proportion": 1,
                        "value": 70873.2,
                        "ratio": -0.0955
                    },
                    "payPkgCnt": {
                        "proportion": 1,
                        "value": 1164,
                        "ratio": -0.0798
                    },
                    "notePayGmv": {
                        "proportion": 0.322,
                        "value": 22823.7,
                        "ratio": -0.1528
                    },
                    "cardPayPkgCnt": {
                        "proportion": 0.1942,
                        "value": 226,
                        "ratio": -0.218
                    },
                    "upr": {
                        "proportion": 1,
                        "value": 0.0713,
                        "ratio": -0.0929
                    },
                    "cardRefundPayGmv": {
                        "proportion": 0.1853,
                        "value": 1624.84,
                        "ratio": -0.3215
                    },
                    "cardPayUserNum": {
                        "proportion": 0.1946,
                        "value": 218,
                        "ratio": -0.2015
                    },
                    "notePayPkgCnt": {
                        "proportion": 0.3625,
                        "value": 422,
                        "ratio": -0.0964
                    }
                }
            ],
            "extra": {
                "downLoad": {
                    "columns": "payGmv,notePayGmv,livePayGmv,cardPayGmv,payPkgCnt,notePayPkgCnt,livePayPkgCnt,cardPayPkgCnt,payUserNum,notePayUserNum,livePayUserNum,cardPayUserNum,goodsUv,noteGoodsUv,liveGoodsUv,cardGoodsUv,refundPayGmv,noteRefundPayGmv,liveRefundPayGmv,cardRefundPayGmv,upr,noteUpr,liveUpr,cardUpr",
                    "fileNamePrefix": "商家经营数据总览"
                },
                "events": {
                    "SingleDataPostProcessor": "seller_carrier_channel_proportion"
                }
            },
            "fileName": "",
            "filePath": "",
            "meta": {
                "dimensions": [
                    {
                        "desc": "时间",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "dtm",
                        "name": "时间",
                        "valueType": "string"
                    }
                ],
                "metrics": [
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "refundPayGmv",
                        "name": "成功退款金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "noteRefundPayGmv",
                        "name": "笔记成功退款金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "liveRefundPayGmv",
                        "name": "直播成功退款金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "cardRefundPayGmv",
                        "name": "商卡成功退款金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio"
                        ],
                        "key": "payGoodsCnt",
                        "name": "支付件数",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio"
                        ],
                        "key": "cartUserNum",
                        "name": "加购人数",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio"
                        ],
                        "key": "cartGoodsCnt",
                        "name": "加购件数",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "payGmv",
                        "name": "支付金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "notePayGmv",
                        "name": "笔记支付金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "livePayGmv",
                        "name": "直播支付金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "cardPayGmv",
                        "name": "商卡支付金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "payPkgCnt",
                        "name": "支付订单数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "notePayPkgCnt",
                        "name": "笔记支付订单数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "livePayPkgCnt",
                        "name": "直播支付订单数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "cardPayPkgCnt",
                        "name": "商卡支付订单数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "payUserNum",
                        "name": "支付买家数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "notePayUserNum",
                        "name": "笔记支付买家数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "livePayUserNum",
                        "name": "直播支付买家数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "cardPayUserNum",
                        "name": "商卡支付买家数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "goodsUv",
                        "name": "商品访客数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "noteGoodsUv",
                        "name": "笔记商品访客数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "liveGoodsUv",
                        "name": "直播商品访客数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "cardGoodsUv",
                        "name": "商卡商品访客数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "upr",
                        "name": "支付转化率",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "noteUpr",
                        "name": "笔记支付转化率",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "liveUpr",
                        "name": "直播支付转化率",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": [
                            "ratio",
                            "proportion"
                        ],
                        "key": "cardUpr",
                        "name": "商卡支付转化率",
                        "valueType": "decimal"
                    }
                ]
            },
            "rpcResultJson": "",
            "statisticsDate": "20250705"
        },
        {
            "blockKey": "sellerCarrierChannelTrend",
            "blockName": "商家经营数据趋势",
            "blockType": "time_line",
            "count": 0,
            "data": [
                {
                    "goodsUv": {
                        "value": 7672
                    },
                    "noteGoodsUv": {
                        "value": 2654
                    },
                    "cartUserNum": {
                        "value": 2175
                    },
                    "payUserNum": {
                        "value": 506
                    },
                    "cardUpr": {
                        "value": 0.025
                    },
                    "refundPayGmv": {
                        "value": 4422.20
                    },
                    "liveGoodsUv": {
                        "value": 1528
                    },
                    "cartGoodsCnt": {
                        "value": 2598
                    },
                    "noteUpr": {
                        "value": 0.0708
                    },
                    "liveRefundPayGmv": {
                        "value": 2253.92
                    },
                    "liveUpr": {
                        "value": 0.1473
                    },
                    "livePayUserNum": {
                        "value": 225
                    },
                    "livePayGmv": {
                        "value": 15551.6
                    },
                    "payGoodsCnt": {
                        "value": 548
                    },
                    "dtm": {
                        "value": "2025-07-04"
                    },
                    "livePayPkgCnt": {
                        "value": 235
                    },
                    "notePayUserNum": {
                        "value": 188
                    },
                    "noteRefundPayGmv": {
                        "value": 1180.74
                    },
                    "cardPayGmv": {
                        "value": 6308.6
                    },
                    "cardGoodsUv": {
                        "value": 4126
                    },
                    "payGmv": {
                        "value": 32129.3
                    },
                    "payPkgCnt": {
                        "value": 530
                    },
                    "notePayGmv": {
                        "value": 10269.1
                    },
                    "cardPayPkgCnt": {
                        "value": 107
                    },
                    "upr": {
                        "value": 0.066
                    },
                    "cardRefundPayGmv": {
                        "value": 987.54
                    },
                    "cardPayUserNum": {
                        "value": 103
                    },
                    "notePayPkgCnt": {
                        "value": 190
                    }
                },
                {
                    "goodsUv": {
                        "value": 8046
                    },
                    "noteGoodsUv": {
                        "value": 2616
                    },
                    "cartUserNum": {
                        "value": 2237
                    },
                    "payUserNum": {
                        "value": 614
                    },
                    "cardUpr": {
                        "value": 0.0264
                    },
                    "refundPayGmv": {
                        "value": 4345.83
                    },
                    "liveGoodsUv": {
                        "value": 1774
                    },
                    "cartGoodsCnt": {
                        "value": 2737
                    },
                    "noteUpr": {
                        "value": 0.0875
                    },
                    "liveRefundPayGmv": {
                        "value": 2343.80
                    },
                    "liveUpr": {
                        "value": 0.1561
                    },
                    "livePayUserNum": {
                        "value": 277
                    },
                    "livePayGmv": {
                        "value": 18806.0
                    },
                    "payGoodsCnt": {
                        "value": 653
                    },
                    "dtm": {
                        "value": "2025-07-05"
                    },
                    "livePayPkgCnt": {
                        "value": 284
                    },
                    "notePayUserNum": {
                        "value": 229
                    },
                    "noteRefundPayGmv": {
                        "value": 1364.73
                    },
                    "cardPayGmv": {
                        "value": 7383.3
                    },
                    "cardGoodsUv": {
                        "value": 4352
                    },
                    "payGmv": {
                        "value": 38743.9
                    },
                    "payPkgCnt": {
                        "value": 634
                    },
                    "notePayGmv": {
                        "value": 12554.6
                    },
                    "cardPayPkgCnt": {
                        "value": 119
                    },
                    "upr": {
                        "value": 0.0763
                    },
                    "cardRefundPayGmv": {
                        "value": 637.30
                    },
                    "cardPayUserNum": {
                        "value": 115
                    },
                    "notePayPkgCnt": {
                        "value": 232
                    }
                }
            ],
            "extra": {},
            "fileName": "",
            "filePath": "",
            "meta": {
                "dimensions": [
                    {
                        "desc": "时间",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "dtm",
                        "name": "时间",
                        "valueType": "number"
                    }
                ],
                "metrics": [
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "payGoodsCnt",
                        "name": "支付件数",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "cartUserNum",
                        "name": "加购人数",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "cartGoodsCnt",
                        "name": "加购件数",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "payGmv",
                        "name": "支付金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "notePayGmv",
                        "name": "笔记支付金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "livePayGmv",
                        "name": "直播支付金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "cardPayGmv",
                        "name": "商卡支付金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "payPkgCnt",
                        "name": "支付订单数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "notePayPkgCnt",
                        "name": "笔记支付订单数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "livePayPkgCnt",
                        "name": "直播支付订单数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "cardPayPkgCnt",
                        "name": "商卡支付订单数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "payUserNum",
                        "name": "支付买家数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "notePayUserNum",
                        "name": "笔记支付买家数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "livePayUserNum",
                        "name": "直播支付买家数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "cardPayUserNum",
                        "name": "商卡支付买家数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "goodsUv",
                        "name": "商品访客数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "noteGoodsUv",
                        "name": "笔记商品访客数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "liveGoodsUv",
                        "name": "直播商品访客数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "cardGoodsUv",
                        "name": "商卡商品访客数",
                        "valueType": "number"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "upr",
                        "name": "支付转化率",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "noteUpr",
                        "name": "笔记支付转化率",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "liveUpr",
                        "name": "直播支付转化率",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "cardUpr",
                        "name": "商卡支付转化率",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "refundPayGmv",
                        "name": "成功退款金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "noteRefundPayGmv",
                        "name": "笔记成功退款金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "liveRefundPayGmv",
                        "name": "直播成功退款金额",
                        "valueType": "decimal"
                    },
                    {
                        "desc": "",
                        "extendAttributes": {},
                        "extendMetrics": null,
                        "key": "cardRefundPayGmv",
                        "name": "商卡成功退款金额",
                        "valueType": "decimal"
                    }
                ]
            },
            "rpcResultJson": "",
            "statisticsDate": "20250705"
        }
    ],
    "code": 0
}