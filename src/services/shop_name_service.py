#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
店铺名称获取服务
"""

import requests
from typing import Optional
from jsonpath_ng import parse


class ShopNameService:
    """店铺名称获取服务"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
    
    def get_shop_name(self, shop_name_api_url: str, shop_name_field: str, cookie_str: str) -> Optional[str]:
        """
        获取店铺名称

        Args:
            shop_name_api_url: 店铺名称接口URL
            shop_name_field: 店铺名称字段路径
            cookie_str: Cookie字符串

        Returns:
            店铺名称，获取失败返回None
        """
        try:
            print(f"正在获取店铺名称: {shop_name_api_url}")

            # 设置更完整的请求头
            headers = {
                'Cookie': cookie_str,
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': shop_name_api_url.split('/api')[0] if '/api' in shop_name_api_url else shop_name_api_url,
                'X-Requested-With': 'XMLHttpRequest',
                'Origin': shop_name_api_url.split('/api')[0] if '/api' in shop_name_api_url else shop_name_api_url,
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin'
            }
            print("shop_a------->",shop_name_api_url.split('/api')[0] if '/api' in shop_name_api_url else shop_name_api_url)
            # 尝试不同的请求方式
            response = None

            # 首先尝试GET请求
            try:
                print(f"尝试GET请求: {shop_name_api_url}")
                params = {"service":"https%3A%2F%2Fark.xiaohongshu.com%2Fapp-seller%2FsellerInfo%3Ffrom%3Dark-login"}
                response = self.session.get(shop_name_api_url, headers=headers, params=params, timeout=30)
                print(f"GET请求状态码: {response.status_code}")
            except Exception as e:
                print(f"GET请求失败: {e}")

            # 如果GET失败，尝试POST请求
            if not response or response.status_code == 406:
                try:
                    print(f"尝试POST请求: {shop_name_api_url}")
                    # 对于小红书千帆，可能需要POST请求
                    # post_data = {"service":"https%3A%2F%2Fark.xiaohongshu.com%2Fapp-seller%2FsellerInfo%3Ffrom%3Dark-login"}
                    post_data = {}
                    post_data["service"] = "https%3A%2F%2Fark.xiaohongshu.com%2Fapp-seller%2FsellerInfo%3Ffrom%3Dark-login"
                    response = self.session.post(shop_name_api_url, headers=headers, json=post_data, timeout=30)
                    print(f"POST请求状态码: {response.status_code}")
                except Exception as e:
                    print(f"POST请求失败: {e}")

            # 如果还是失败，尝试不同的接口地址
            if not response or response.status_code == 406:
                # 尝试其他可能的接口地址
                alternative_urls = [
                    "https://ark.xiaohongshu.com/api/edith/user/info",
                    "https://ark.xiaohongshu.com/api/user/profile",
                    "https://ark.xiaohongshu.com/api/seller/info"
                ]

                for alt_url in alternative_urls:
                    try:
                        print(f"尝试备用接口: {alt_url}")
                        response = self.session.get(alt_url, headers=headers, timeout=30)
                        print(f"备用接口状态码: {response.status_code}")
                        if response.status_code == 200:
                            shop_name_api_url = alt_url  # 更新URL用于后续处理
                            break
                    except Exception as e:
                        print(f"备用接口 {alt_url} 请求失败: {e}")
                        continue

            if response and response.status_code == 200:
                try:
                    data = response.json()
                    print(f"店铺名称接口返回数据: {data}")

                    # 使用JSONPath提取店铺名称
                    shop_name = self._extract_shop_name(data, shop_name_field)

                    if shop_name:
                        print(f"✅ 成功获取店铺名称: {shop_name}")
                        return shop_name
                    else:
                        print(f"❌ 未能从返回数据中提取店铺名称，字段路径: {shop_name_field}")
                        # 尝试其他可能的字段名
                        alternative_fields = [
                            "data.seller_name", "data.shop_name", "data.name",
                            "seller_name", "shop_name", "name", "nickname",
                            "data.nickname", "data.user_name", "user_name"
                        ]

                        for alt_field in alternative_fields:
                            alt_shop_name = self._extract_shop_name(data, alt_field)
                            if alt_shop_name:
                                print(f"✅ 使用备用字段 {alt_field} 获取店铺名称: {alt_shop_name}")
                                return alt_shop_name

                        print(f"❌ 尝试所有字段都未能提取店铺名称")
                        return None

                except ValueError as e:
                    print(f"❌ 解析JSON响应失败: {e}")
                    print(f"响应内容: {response.text[:500]}...")
                    return None
            else:
                if response:
                    print(f"❌ 店铺名称接口请求失败，状态码: {response.status_code}")
                    print(f"响应内容: {response.text[:500]}...")

                    # 如果是406错误，提供更详细的错误信息
                    if response.status_code == 406:
                        print("❌ 406 Not Acceptable - 服务器无法接受请求格式")
                        print("可能的原因:")
                        print("1. 接口需要特定的Content-Type")
                        print("2. 接口需要POST而不是GET请求")
                        print("3. 接口地址已经变更")
                        print("4. 缺少必要的请求参数")
                else:
                    print("❌ 所有请求方式都失败了")
                return None
                
        except Exception as e:
            print(f"❌ 获取店铺名称失败: {e}")
            return None
    
    def _extract_shop_name(self, data: dict, shop_name_field: str) -> Optional[str]:
        """
        从返回数据中提取店铺名称
        
        Args:
            data: 接口返回的数据
            shop_name_field: 店铺名称字段路径
            
        Returns:
            店铺名称
        """
        try:
            # 如果字段路径不是JSONPath格式，添加$前缀
            if not shop_name_field.startswith('$'):
                if '.' in shop_name_field:
                    shop_name_field = f"$.{shop_name_field}"
                else:
                    shop_name_field = f"$.{shop_name_field}"
            
            # 使用JSONPath提取数据
            jsonpath_expr = parse(shop_name_field)
            matches = jsonpath_expr.find(data)
            
            if matches:
                shop_name = matches[0].value
                if isinstance(shop_name, str) and shop_name.strip():
                    return shop_name.strip()
            
            # 如果JSONPath提取失败，尝试直接访问字段
            if '.' not in shop_name_field.replace('$.', ''):
                field_name = shop_name_field.replace('$.', '')
                if field_name in data:
                    shop_name = data[field_name]
                    if isinstance(shop_name, str) and shop_name.strip():
                        return shop_name.strip()
            
            return None
            
        except Exception as e:
            print(f"提取店铺名称失败: {e}")
            return None
    
    def validate_shop_name_config(self, shop_name_api_url: str, shop_name_field: str) -> bool:
        """
        验证店铺名称配置是否有效
        
        Args:
            shop_name_api_url: 店铺名称接口URL
            shop_name_field: 店铺名称字段路径
            
        Returns:
            配置是否有效
        """
        try:
            # 验证URL格式
            if not shop_name_api_url or not (shop_name_api_url.startswith('http://') or shop_name_api_url.startswith('https://')):
                return False
            
            # 验证字段路径
            if not shop_name_field or not shop_name_field.strip():
                return False
            
            return True
            
        except Exception:
            return False
