#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据抓取服务类
处理各平台数据的抓取逻辑
"""

import json
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from jsonpath_ng import parse
from src.utils.data_manager import DataManager
from src.services.account_service import AccountService
from src.services.platform_service import PlatformService
from src.services.cookie_service import CookieService


class DataScraper:
    """数据抓取器"""
    
    def __init__(self, data_manager: DataManager):
        """
        初始化数据抓取器
        
        Args:
            data_manager: 数据管理器实例
        """
        self.data_manager = data_manager
        self.account_service = AccountService(data_manager)
        self.platform_service = PlatformService(data_manager)
        self.cookie_service = CookieService(data_manager)
    
    def scrape_xiaohongshu_data(self, account_id: str, start_date: str, end_date: str, field_mappings: List[Dict] = None) -> Dict:
        """
        抓取小红书千帆数据
        
        Args:
            account_id: 账号ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            抓取结果
        """
        # 获取账号和平台信息
        account_platform = self.account_service.get_account_with_platform(account_id)
        if not account_platform:
            return {"success": False, "error": "账号或平台信息不存在"}
        
        account, platform = account_platform
        
        # 获取Cookie
        cookie_str = self.cookie_service.get_account_cookie(account_id)
        if not cookie_str:
            return {"success": False, "error": "账号未登录，请先获取Cookie"}
        
        try:
            # 构建请求参数（基于小红书千帆API结构）
            request_body = {
                "requestBody": {
                    "blockElements": [
                        {
                            "blockKey": "sellerCarrierChannelOverall",
                            "filterMap": {
                                "dateType": 0,
                                "dateSelectType": "custom",
                                "startDate": start_date,
                                "endDate": end_date,
                                "sellerManageType": "all"
                            }
                        },
                        {
                            "blockKey": "sellerCarrierChannelTrend",
                            "filterMap": {
                                "dateType": 0,
                                "dateSelectType": "custom",
                                "startDate": start_date,
                                "endDate": end_date,
                                "sellerManageType": "all"
                            }
                        }
                    ]
                },
                "type": "sellerCarrierChannelOverall"
            }
            
            # 设置请求头
            headers = {
                "Cookie": cookie_str,
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Referer": "https://ark.xiaohongshu.com/",
                "Origin": "https://ark.xiaohongshu.com"
            }
            
            # 发送请求
            response = requests.post(
                platform.data_api_url,
                json=request_body,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 使用JSONPath提取数据（支持字段映射）
                extracted_data = self.extract_data_with_jsonpath(data, platform.extract_rule, field_mappings)
                
                return {
                    "success": True,
                    "data": extracted_data,
                    "raw_data": data,
                    "account_name": account.account_name,
                    "platform_name": platform.platform_name,
                    "date_range": f"{start_date}至{end_date}"
                }
            else:
                return {
                    "success": False,
                    "error": f"请求失败，状态码: {response.status_code}",
                    "response_text": response.text
                }
                
        except Exception as e:
            return {"success": False, "error": f"抓取数据时出错: {str(e)}"}
    
    def extract_data_with_jsonpath(self, data: Dict, extract_rule: str, field_mappings: List[Dict] = None) -> List[Dict]:
        """
        使用JSONPath提取数据

        Args:
            data: 原始数据
            extract_rule: JSONPath提取规则
            field_mappings: 字段映射配置

        Returns:
            提取的数据列表
        """
        try:
            if field_mappings:
                # 使用字段映射进行精确提取
                return self.extract_data_with_field_mappings(data, field_mappings)
            else:
                # 使用传统的JSONPath提取
                return self.extract_data_with_traditional_jsonpath(data, extract_rule)

        except Exception as e:
            print(f"数据提取失败: {e}")
            return []

    def extract_data_with_field_mappings(self, data: Dict, field_mappings: List[Dict]) -> List[Dict]:
        """
        使用字段映射进行精确数据提取

        Args:
            data: 原始数据
            field_mappings: 字段映射配置

        Returns:
            提取的数据列表
        """
        try:
            extracted_records = []

            # 首先确定数据的结构，找到记录的数量
            record_count = self.determine_record_count(data, field_mappings)

            for i in range(record_count):
                record = {}

                for mapping in field_mappings:
                    field_name = mapping["field_name"]
                    json_path = mapping["json_path"]
                    data_type = mapping.get("data_type", "文本")
                    default_value = mapping.get("default_value", "")

                    # 提取字段值
                    value = self.extract_single_field(data, json_path, i)

                    # 如果没有提取到值，使用默认值
                    if value is None:
                        value = default_value

                    # 转换数据类型
                    value = self.convert_data_type(value, data_type)

                    record[field_name] = value

                extracted_records.append(record)

            return extracted_records

        except Exception as e:
            print(f"字段映射提取失败: {e}")
            return []

    def extract_data_with_traditional_jsonpath(self, data: Dict, extract_rule: str) -> List[Dict]:
        """
        使用传统JSONPath提取数据

        Args:
            data: 原始数据
            extract_rule: JSONPath提取规则

        Returns:
            提取的数据列表
        """
        try:
            jsonpath_expr = parse(extract_rule)
            matches = jsonpath_expr.find(data)

            extracted_data = []
            for match in matches:
                if isinstance(match.value, dict):
                    extracted_data.append(match.value)
                elif isinstance(match.value, list):
                    extracted_data.extend(match.value)

            return extracted_data

        except Exception as e:
            print(f"传统JSONPath提取失败: {e}")
            return []

    def determine_record_count(self, data: Dict, field_mappings: List[Dict]) -> int:
        """
        确定记录数量

        Args:
            data: 原始数据
            field_mappings: 字段映射配置

        Returns:
            记录数量
        """
        try:
            # 查找数组类型的字段来确定记录数量
            for mapping in field_mappings:
                json_path = mapping["json_path"]
                if "[*]" in json_path:
                    # 这是一个数组字段，用它来确定记录数量
                    array_path = json_path.split("[*]")[0]
                    jsonpath_expr = parse(array_path)
                    matches = jsonpath_expr.find(data)

                    if matches and isinstance(matches[0].value, list):
                        return len(matches[0].value)

            # 如果没有找到数组字段，默认返回1条记录
            return 1

        except Exception as e:
            print(f"确定记录数量失败: {e}")
            return 1

    def extract_single_field(self, data: Dict, json_path: str, index: int = 0):
        """
        提取单个字段的值

        Args:
            data: 原始数据
            json_path: JSONPath路径
            index: 记录索引

        Returns:
            字段值
        """
        try:
            # 处理数组索引
            if "[*]" in json_path:
                # 将[*]替换为具体的索引
                actual_path = json_path.replace("[*]", f"[{index}]")
            else:
                actual_path = json_path

            jsonpath_expr = parse(actual_path)
            matches = jsonpath_expr.find(data)

            if matches:
                return matches[0].value
            else:
                return None

        except Exception as e:
            print(f"提取字段失败 {json_path}: {e}")
            return None

    def convert_data_type(self, value, data_type: str):
        """
        转换数据类型

        Args:
            value: 原始值
            data_type: 目标数据类型

        Returns:
            转换后的值
        """
        try:
            if value is None or value == "":
                return ""

            if data_type == "数字":
                # 尝试转换为数字
                if isinstance(value, (int, float)):
                    return value
                else:
                    # 移除非数字字符
                    import re
                    numeric_str = re.sub(r'[^\d.-]', '', str(value))
                    if numeric_str:
                        return float(numeric_str) if '.' in numeric_str else int(numeric_str)
                    else:
                        return 0

            elif data_type == "日期":
                # 尝试转换为日期格式
                if isinstance(value, str):
                    import datetime
                    # 尝试多种日期格式
                    date_formats = [
                        "%Y-%m-%d",
                        "%Y/%m/%d",
                        "%Y-%m-%d %H:%M:%S",
                        "%Y/%m/%d %H:%M:%S"
                    ]

                    for fmt in date_formats:
                        try:
                            dt = datetime.datetime.strptime(value, fmt)
                            return dt.strftime("%Y-%m-%d")
                        except ValueError:
                            continue

                    # 如果都不匹配，返回原值
                    return str(value)
                else:
                    return str(value)

            else:  # 文本类型
                return str(value)

        except Exception as e:
            print(f"数据类型转换失败: {e}")
            return str(value) if value is not None else ""

    def scrape_single_account(self, account_id: str, start_date: str, end_date: str) -> Dict:
        """
        抓取单个账号的数据
        
        Args:
            account_id: 账号ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            抓取结果
        """
        # 获取平台信息
        account_platform = self.account_service.get_account_with_platform(account_id)
        if not account_platform:
            return {"success": False, "error": "账号或平台信息不存在"}
        
        account, platform = account_platform
        
        # 根据平台类型选择抓取方法
        if "xiaohongshu" in platform.platform_name.lower() or "千帆" in platform.platform_name:
            return self.scrape_xiaohongshu_data(account_id, start_date, end_date, platform.field_mappings)
        else:
            # 通用抓取方法
            return self.scrape_generic_data(account_id, start_date, end_date, platform.field_mappings)
    
    def scrape_generic_data(self, account_id: str, start_date: str, end_date: str, field_mappings: List[Dict] = None) -> Dict:
        """
        通用数据抓取方法
        
        Args:
            account_id: 账号ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            抓取结果
        """
        # 获取账号和平台信息
        account_platform = self.account_service.get_account_with_platform(account_id)
        if not account_platform:
            return {"success": False, "error": "账号或平台信息不存在"}
        
        account, platform = account_platform
        
        # 获取Cookie
        cookie_str = self.cookie_service.get_account_cookie(account_id)
        if not cookie_str:
            return {"success": False, "error": "账号未登录，请先获取Cookie"}
        
        try:
            # 构建请求参数
            params = {
                "start_date": start_date,
                "end_date": end_date
            }
            
            headers = {
                "Cookie": cookie_str,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            # 发送GET请求
            response = requests.get(
                platform.data_api_url,
                params=params,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 使用JSONPath提取数据（支持字段映射）
                extracted_data = self.extract_data_with_jsonpath(data, platform.extract_rule, field_mappings)
                
                return {
                    "success": True,
                    "data": extracted_data,
                    "raw_data": data,
                    "account_name": account.account_name,
                    "platform_name": platform.platform_name,
                    "date_range": f"{start_date}至{end_date}"
                }
            else:
                return {
                    "success": False,
                    "error": f"请求失败，状态码: {response.status_code}"
                }
                
        except Exception as e:
            return {"success": False, "error": f"抓取数据时出错: {str(e)}"}
    
    def scrape_all_accounts(self, start_date: str, end_date: str) -> List[Dict]:
        """
        批量抓取所有已登录账号的数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            所有账号的抓取结果列表
        """
        logged_in_accounts = self.account_service.get_logged_in_accounts()
        results = []
        
        for account in logged_in_accounts:
            result = self.scrape_single_account(account.account_id, start_date, end_date)
            results.append(result)
        
        return results
