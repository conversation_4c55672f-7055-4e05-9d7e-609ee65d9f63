#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据抓取服务类
处理各平台数据的抓取逻辑
"""

import json
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from jsonpath_ng import parse
from src.utils.data_manager import DataManager
from src.services.account_service import AccountService
from src.services.platform_service import PlatformService
from src.services.cookie_service import CookieService


class DataScraper:
    """数据抓取器"""
    
    def __init__(self, data_manager: DataManager):
        """
        初始化数据抓取器
        
        Args:
            data_manager: 数据管理器实例
        """
        self.data_manager = data_manager
        self.account_service = AccountService(data_manager)
        self.platform_service = PlatformService(data_manager)
        self.cookie_service = CookieService(data_manager)
    
    def scrape_xiaohongshu_data(self, account_id: str, start_date: str, end_date: str) -> Dict:
        """
        抓取小红书千帆数据
        
        Args:
            account_id: 账号ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            抓取结果
        """
        # 获取账号和平台信息
        account_platform = self.account_service.get_account_with_platform(account_id)
        if not account_platform:
            return {"success": False, "error": "账号或平台信息不存在"}
        
        account, platform = account_platform
        
        # 获取Cookie
        cookie_str = self.cookie_service.get_account_cookie(account_id)
        if not cookie_str:
            return {"success": False, "error": "账号未登录，请先获取Cookie"}
        
        try:
            # 构建请求参数（基于小红书千帆API结构）
            request_body = {
                "requestBody": {
                    "blockElements": [
                        {
                            "blockKey": "sellerCarrierChannelOverall",
                            "filterMap": {
                                "dateType": 0,
                                "dateSelectType": "custom",
                                "startDate": start_date,
                                "endDate": end_date,
                                "sellerManageType": "all"
                            }
                        },
                        {
                            "blockKey": "sellerCarrierChannelTrend",
                            "filterMap": {
                                "dateType": 0,
                                "dateSelectType": "custom",
                                "startDate": start_date,
                                "endDate": end_date,
                                "sellerManageType": "all"
                            }
                        }
                    ]
                },
                "type": "sellerCarrierChannelOverall"
            }
            
            # 设置请求头
            headers = {
                "Cookie": cookie_str,
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Referer": "https://ark.xiaohongshu.com/",
                "Origin": "https://ark.xiaohongshu.com"
            }
            
            # 发送请求
            response = requests.post(
                platform.data_api_url,
                json=request_body,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 使用JSONPath提取数据
                extracted_data = self.extract_data_with_jsonpath(data, platform.extract_rule)
                
                return {
                    "success": True,
                    "data": extracted_data,
                    "raw_data": data,
                    "account_name": account.account_name,
                    "platform_name": platform.platform_name,
                    "date_range": f"{start_date}至{end_date}"
                }
            else:
                return {
                    "success": False,
                    "error": f"请求失败，状态码: {response.status_code}",
                    "response_text": response.text
                }
                
        except Exception as e:
            return {"success": False, "error": f"抓取数据时出错: {str(e)}"}
    
    def extract_data_with_jsonpath(self, data: Dict, extract_rule: str) -> List[Dict]:
        """
        使用JSONPath提取数据
        
        Args:
            data: 原始数据
            extract_rule: JSONPath提取规则
            
        Returns:
            提取的数据列表
        """
        try:
            jsonpath_expr = parse(extract_rule)
            matches = jsonpath_expr.find(data)
            
            extracted_data = []
            for match in matches:
                if isinstance(match.value, dict):
                    extracted_data.append(match.value)
                elif isinstance(match.value, list):
                    extracted_data.extend(match.value)
            
            return extracted_data
            
        except Exception as e:
            print(f"JSONPath提取失败: {e}")
            return []
    
    def scrape_single_account(self, account_id: str, start_date: str, end_date: str) -> Dict:
        """
        抓取单个账号的数据
        
        Args:
            account_id: 账号ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            抓取结果
        """
        # 获取平台信息
        account_platform = self.account_service.get_account_with_platform(account_id)
        if not account_platform:
            return {"success": False, "error": "账号或平台信息不存在"}
        
        account, platform = account_platform
        
        # 根据平台类型选择抓取方法
        if "xiaohongshu" in platform.platform_name.lower() or "千帆" in platform.platform_name:
            return self.scrape_xiaohongshu_data(account_id, start_date, end_date)
        else:
            # 通用抓取方法
            return self.scrape_generic_data(account_id, start_date, end_date)
    
    def scrape_generic_data(self, account_id: str, start_date: str, end_date: str) -> Dict:
        """
        通用数据抓取方法
        
        Args:
            account_id: 账号ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            抓取结果
        """
        # 获取账号和平台信息
        account_platform = self.account_service.get_account_with_platform(account_id)
        if not account_platform:
            return {"success": False, "error": "账号或平台信息不存在"}
        
        account, platform = account_platform
        
        # 获取Cookie
        cookie_str = self.cookie_service.get_account_cookie(account_id)
        if not cookie_str:
            return {"success": False, "error": "账号未登录，请先获取Cookie"}
        
        try:
            # 构建请求参数
            params = {
                "start_date": start_date,
                "end_date": end_date
            }
            
            headers = {
                "Cookie": cookie_str,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            # 发送GET请求
            response = requests.get(
                platform.data_api_url,
                params=params,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 使用JSONPath提取数据
                extracted_data = self.extract_data_with_jsonpath(data, platform.extract_rule)
                
                return {
                    "success": True,
                    "data": extracted_data,
                    "raw_data": data,
                    "account_name": account.account_name,
                    "platform_name": platform.platform_name,
                    "date_range": f"{start_date}至{end_date}"
                }
            else:
                return {
                    "success": False,
                    "error": f"请求失败，状态码: {response.status_code}"
                }
                
        except Exception as e:
            return {"success": False, "error": f"抓取数据时出错: {str(e)}"}
    
    def scrape_all_accounts(self, start_date: str, end_date: str) -> List[Dict]:
        """
        批量抓取所有已登录账号的数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            所有账号的抓取结果列表
        """
        logged_in_accounts = self.account_service.get_logged_in_accounts()
        results = []
        
        for account in logged_in_accounts:
            result = self.scrape_single_account(account.account_id, start_date, end_date)
            results.append(result)
        
        return results
