#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie服务类
处理浏览器Cookie的获取和管理
"""

import os
import sqlite3
import platform
import webbrowser
import time
import threading
import glob
import signal
import subprocess
import psutil
from typing import Optional, Dict, List
from urllib.parse import urlparse
from src.utils.data_manager import DataManager
from src.services.shop_name_service import ShopNameService


try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from webdriver_manager.chrome import ChromeDriverManager
    from webdriver_manager.core.os_manager import ChromeType
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False


class CookieService:
    """Cookie服务类"""
    
    def __init__(self, data_manager: DataManager):
        """
        初始化Cookie服务

        Args:
            data_manager: 数据管理器实例
        """
        self.data_manager = data_manager
        self.monitoring = False
        self.monitor_thread = None
        self.driver = None
        self.driver_process = None  # 存储浏览器进程
        self.login_callbacks = {}  # 存储登录回调函数
        self.callback_results = {}  # 存储回调结果，避免直接调用UI
        self.shop_name_service = ShopNameService()  # 店铺名称服务

    def __del__(self):
        """对象销毁时清理资源"""
        self._cleanup_driver()
    
    def get_chrome_cookie_path(self) -> Optional[str]:
        """获取Chrome浏览器Cookie数据库路径"""
        system = platform.system()
        
        if system == "Windows":
            # Windows Chrome Cookie路径
            cookie_path = os.path.expanduser(
                "~\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cookies"
            )
        elif system == "Darwin":  # macOS
            # Mac Chrome Cookie路径
            cookie_path = os.path.expanduser(
                "~/Library/Application Support/Google/Chrome/Default/Cookies"
            )
        else:
            # Linux
            cookie_path = os.path.expanduser(
                "~/.config/google-chrome/Default/Cookies"
            )
        
        return cookie_path if os.path.exists(cookie_path) else None
    
    def extract_cookies_from_chrome(self, domain: str) -> List[Dict]:
        """
        从Chrome浏览器提取指定域名的Cookie
        
        Args:
            domain: 目标域名
            
        Returns:
            Cookie列表
        """
        cookie_path = self.get_chrome_cookie_path()
        if not cookie_path:
            return []
        
        cookies = []
        
        try:
            # 复制Cookie数据库文件（避免锁定问题）
            temp_cookie_path = cookie_path + ".temp"
            import shutil
            shutil.copy2(cookie_path, temp_cookie_path)
            
            # 连接SQLite数据库
            conn = sqlite3.connect(temp_cookie_path)
            cursor = conn.cursor()
            
            # 查询指定域名的Cookie
            query = """
                SELECT name, value, host_key, path, expires_utc, is_secure, is_httponly
                FROM cookies 
                WHERE host_key LIKE ?
            """
            
            cursor.execute(query, (f"%{domain}%",))
            rows = cursor.fetchall()
            
            for row in rows:
                cookie = {
                    "name": row[0],
                    "value": row[1],
                    "domain": row[2],
                    "path": row[3],
                    "expires": row[4],
                    "secure": bool(row[5]),
                    "httponly": bool(row[6])
                }
                cookies.append(cookie)
            
            conn.close()
            
            # 删除临时文件
            os.remove(temp_cookie_path)
            
        except Exception as e:
            print(f"提取Cookie失败: {e}")
        
        return cookies
    
    def _cleanup_driver(self):
        """安全地清理浏览器和驱动资源"""
        print("开始清理浏览器资源...")
        
        # 先关闭浏览器
        if self.driver:
            try:
                print("尝试关闭浏览器...")
                self.driver.quit()
                print("浏览器已关闭")
            except Exception as e:
                print(f"关闭浏览器失败: {e}")
            finally:
                self.driver = None
        
        # 再终止服务
        if hasattr(self, 'chrome_service') and self.chrome_service:
            try:
                print("尝试终止ChromeDriver服务...")
                if hasattr(self.chrome_service, 'process') and self.chrome_service.process:
                    # 先尝试正常终止
                    self.chrome_service.process.terminate()
                    try:
                        # 等待5秒
                        self.chrome_service.process.wait(timeout=5)
                        print("ChromeDriver服务已正常终止")
                    except Exception:
                        # 如果超时，使用SIGKILL
                        import os
                        os.kill(self.chrome_service.process.pid, signal.SIGKILL)
                        print("已使用SIGKILL终止ChromeDriver服务")
                else:
                    # 如果没有进程引用，尝试直接停止服务
                    self.chrome_service.stop()
            except Exception as e:
                print(f"终止ChromeDriver服务失败: {e}")
            finally:
                self.chrome_service = None
        
        print("浏览器资源清理完成")
    
    def cookies_to_string(self, cookies: List[Dict]) -> str:
        """将Cookie列表转换为字符串格式"""
        cookie_strings = []
        for cookie in cookies:
            cookie_strings.append(f"{cookie['name']}={cookie['value']}")
        return "; ".join(cookie_strings)

    def _create_login_wrapper_page(self, account_id: str, login_url: str) -> str:
        """创建登录包装页面HTML"""
        try:
            # 读取模板文件
            template_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'templates', 'login_wrapper.html')

            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()

                # 替换模板变量
                html_content = template_content.replace('{{ACCOUNT_ID}}', account_id)
                html_content = html_content.replace('{{LOGIN_URL}}', login_url)

                return html_content
            else:
                # 如果模板文件不存在，使用内嵌的简化版本
                return self._create_simple_wrapper_page(account_id, login_url)

        except Exception as e:
            print(f"创建包装页面失败: {e}")
            return self._create_simple_wrapper_page(account_id, login_url)

    def _create_simple_wrapper_page(self, account_id: str, login_url: str) -> str:
        """创建简化的登录包装页面"""
        return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面 - 数据采集工具</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; height: 100vh; overflow: hidden; }}
        .header-container {{ position: fixed; top: 0; left: 0; right: 0; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 9999; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; }}
        .header-title {{ color: white; font-size: 16px; font-weight: 600; }}
        .login-button {{ background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.3s ease; }}
        .login-button:hover {{ background: #45a017; }}
        .login-button:disabled {{ background: #d9d9d9; cursor: not-allowed; }}
        .iframe-container {{ position: absolute; top: 60px; left: 0; right: 0; bottom: 0; background: white; }}
        .login-iframe {{ width: 100%; height: 100%; border: none; background: white; }}
    </style>
</head>
<body>
    <div class="header-container">
        <div class="header-title">请完成登录操作</div>
        <button class="login-button" id="loginButton" onclick="checkLogin()">我已完成登录</button>
    </div>
    <div class="iframe-container">
        <iframe class="login-iframe" src="{login_url}" sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals"></iframe>
    </div>
    <script>
        window.accountId = '{account_id}';
        window.loginUrl = '{login_url}';
        window.loginCompleted = false;
        window.checkingLogin = false;
        window.userClosing = false;

        // 强化的防重定向机制
        function preventRedirect() {{
            console.log('启动强化防重定向机制');

            const originalLocation = window.location;
            const originalHref = originalLocation.href;

            // 防止beforeunload跳转
            window.addEventListener('beforeunload', function(e) {{
                if (!window.userClosing) {{
                    console.log('阻止beforeunload跳转');
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }}
            }});

            // 阻止历史记录变化
            window.addEventListener('popstate', function(e) {{
                console.log('阻止popstate变化');
                e.preventDefault();
                history.pushState(null, '', originalHref);
            }});

            // 重写location对象
            Object.defineProperty(window, 'location', {{
                get: function() {{
                    return {{
                        href: originalHref,
                        assign: function(url) {{ console.log('阻止location.assign:', url); }},
                        replace: function(url) {{ console.log('阻止location.replace:', url); }},
                        reload: function() {{ console.log('阻止location.reload'); }},
                        toString: function() {{ return originalHref; }}
                    }};
                }},
                set: function(value) {{
                    console.log('阻止location设置:', value);
                }}
            }});

            // 重写history方法
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;

            history.pushState = function(state, title, url) {{
                if (url && url !== originalHref && !url.startsWith('#')) {{
                    console.log('阻止history.pushState:', url);
                    return;
                }}
                return originalPushState.call(this, state, title, url);
            }};

            history.replaceState = function(state, title, url) {{
                if (url && url !== originalHref && !url.startsWith('#')) {{
                    console.log('阻止history.replaceState:', url);
                    return;
                }}
                return originalReplaceState.call(this, state, title, url);
            }};

            // 定期检查URL
            setInterval(function() {{
                if (window.location.href !== originalHref) {{
                    console.log('检测到URL被修改，强制恢复');
                    try {{
                        history.replaceState(null, '', originalHref);
                    }} catch (e) {{
                        console.log('无法恢复URL:', e);
                    }}
                }}
            }}, 1000);

            console.log('防重定向机制已启用');
        }}

        // 初始化防重定向
        preventRedirect();

        function checkLogin() {{
            if (window.checkingLogin) return;
            if (!confirm('请确认您已完成登录操作。\\n\\n点击"确定"检查登录状态\\n点击"取消"继续登录')) return;

            window.checkingLogin = true;
            const button = document.getElementById('loginButton');
            button.disabled = true;
            button.textContent = '正在检查登录状态...';
            window.loginCompleted = true;
        }}

        function onLoginSuccess() {{
            const button = document.getElementById('loginButton');
            button.textContent = '登录成功！';
            alert('登录成功！Cookie已获取，浏览器将在3秒后自动关闭。');
            window.userClosing = true;
            setTimeout(() => {{
                window.userClosing = true;
                window.close();
            }}, 3000);
        }}

        function onLoginFailure(message) {{
            const button = document.getElementById('loginButton');
            button.disabled = false;
            button.textContent = '我已完成登录';
            window.checkingLogin = false;
            window.loginCompleted = false;
            alert('未获取到登录态，请先完成登录操作后再点击按钮');
        }}
    </script>
</body>
</html>
        """

    def open_login_page(self, login_url: str):
        """打开登录页面（传统方式）"""
        # 在URL中添加隐私参数
        privacy_url = f"{login_url}?__privacy_mode=1"

        # 打开后自动执行清理脚本
        webbrowser.open(privacy_url)

    def open_incognito_browser(self, login_url: str, account_id: str, callback=None):
        try:
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument("--incognito")  # 无痕模式
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 创建包装页面
            wrapper_html = self._create_login_wrapper_page(account_id, login_url)

            # 保存包装页面到临时文件
            import tempfile
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.html')
            temp_file.write(wrapper_html)
            temp_file.close()

            # 创建WebDriver
            # 方法1：自动获取匹配的ChromeDriver版本（推荐）
            driver_path = ChromeDriverManager().install()
            # 启动浏览器
            driver = webdriver.Chrome(options=chrome_options)
            driver.get(f'file://{temp_file.name}')
            
            # 方法2：如果需要指定版本，可以使用以下方式（适用于旧版本webdriver_manager）
            # from webdriver_manager.core.utils import ChromeType
            # driver_path = ChromeDriverManager(chrome_type=ChromeType.GOOGLE).install()
            
            # 方法3：手动指定版本（适用于新版本webdriver_manager）
            # from webdriver_manager.core.os_manager import ChromeType
            # driver_path = ChromeDriverManager(version="138.0.7204.94", chrome_type=ChromeType.GOOGLE).install()
            
            # 修复macOS上的路径和权限问题
            if platform.system() == "Darwin":
                if "THIRD_PARTY_NOTICES" in driver_path:
                    driver_dir = os.path.dirname(driver_path)
                    # 查找实际的chromedriver可执行文件
                    actual_drivers = glob.glob(os.path.join(driver_dir, "chromedriver"))
                    if not actual_drivers:
                        # 如果没找到，尝试查找带扩展名的
                        actual_drivers = glob.glob(os.path.join(driver_dir, "chromedriver*"))
                        # 过滤掉非可执行文件
                        actual_drivers = [d for d in actual_drivers if not d.endswith('.txt') and not d.endswith('.md') and 'THIRD_PARTY' not in d]

                    if actual_drivers:
                        driver_path = actual_drivers[0]
                        print(f"修正ChromeDriver路径: {driver_path}")
                    else:
                        print("未找到有效的ChromeDriver可执行文件")
                        return
                
                # 确保文件有执行权限
                if not os.access(driver_path, os.X_OK):
                    os.chmod(driver_path, 0o755)
                    print(f"已设置ChromeDriver执行权限: {driver_path}")
            
            # 尝试启动ChromeDriver服务
            service = Service(driver_path)
            
            # 保存服务引用以便后续清理
            self.chrome_service = service
            
            # 启动浏览器
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.get(login_url)
            
            # 记录浏览器进程ID以便后续管理
            self.driver_process = self.driver.service.process
            
            if callback:
                self.login_callbacks[account_id] = callback

        except Exception as e:
            print(f"打开无痕浏览器失败: {e}")
            # 发生错误时尝试清理资源
            self._cleanup_driver()
        
    def _add_login_completion_button(self, account_id: str):
        """在浏览器右上角添加'我已完成登录'按钮"""
        try:
            # 强化的按钮注入脚本
            button_script = """
            // 清理之前的定时器和观察器
            if (window.buttonCheckInterval) {
                clearInterval(window.buttonCheckInterval);
            }
            if (window.buttonObserver) {
                window.buttonObserver.disconnect();
            }

            // 强制创建按钮的函数
            window.forceCreateLoginButton = function() {
                // 如果按钮已存在，不重复创建
                if (document.getElementById('login-completion-container')) {
                    return;
                }

                // 创建按钮容器
                var buttonContainer = document.createElement('div');
                buttonContainer.id = 'login-completion-container';
                buttonContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 999999;
                    background: white;
                    border: 2px solid #1890ff;
                    border-radius: 8px;
                    padding: 10px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                `;

                // 创建按钮
                var loginButton = document.createElement('button');
                loginButton.id = 'login-completion-button';
                loginButton.innerText = window.loginButtonText || '我已完成登录';
                loginButton.disabled = window.loginButtonDisabled || false;
                loginButton.style.cssText = `
                    background: ${window.loginButtonColor || '#1890ff'};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: ${window.loginButtonDisabled ? 'not-allowed' : 'pointer'};
                    font-size: 14px;
                    font-weight: bold;
                    margin-right: 8px;
                    opacity: ${window.loginButtonDisabled ? '0.6' : '1'};
                `;

                // 创建关闭按钮
                var closeButton = document.createElement('button');
                closeButton.innerText = '×';
                closeButton.style.cssText = `
                    background: #ff4d4f;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: bold;
                `;

                // 添加按钮到容器
                buttonContainer.appendChild(loginButton);
                buttonContainer.appendChild(closeButton);

                // 添加到页面
                document.body.appendChild(buttonContainer);

                // 设置按钮点击事件标记
                loginButton.onclick = function() {
                    if (!this.disabled) {
                        // 弹出确认对话框
                        if (confirm('请确认您已完成登录操作。\\n\\n点击"确定"检查登录状态\\n点击"取消"继续登录')) {
                            // 用户确认已完成登录，设置标记
                            window.loginCompleted = true;
                            window.loginButtonText = '正在检查登录状态...';
                            window.loginButtonColor = '#52c41a';
                            window.loginButtonDisabled = true;

                            this.style.background = '#52c41a';
                            this.innerText = '正在检查登录状态...';
                            this.disabled = true;
                            this.style.cursor = 'not-allowed';
                            this.style.opacity = '0.6';
                        }
                        // 如果用户点击取消，不做任何操作，继续显示按钮
                    }
                };

                // 关闭按钮事件
                closeButton.onclick = function() {
                    window.loginCancelled = true;
                    buttonContainer.remove();
                };

                // 鼠标悬停效果
                loginButton.onmouseover = function() {
                    if (!this.disabled) {
                        this.style.background = '#40a9ff';
                    }
                };
                loginButton.onmouseout = function() {
                    if (!this.disabled) {
                        this.style.background = window.loginButtonColor || '#1890ff';
                    }
                };
            };

            // 立即创建按钮
            window.forceCreateLoginButton();

            // 为了兼容性，也创建旧的函数名
            window.createLoginButton = window.forceCreateLoginButton;

            // 强化的按钮持久性机制

            // 智能按钮状态检查函数
            window.checkButtonStatus = function() {
                var container = document.getElementById('login-completion-container');
                var button = document.getElementById('login-completion-button');

                // 检查按钮是否完整存在
                if (!container || !button) {
                    return { exists: false, reason: '按钮元素不存在' };
                }

                // 检查按钮是否在DOM树中
                if (!document.body.contains(container)) {
                    return { exists: false, reason: '按钮不在DOM树中' };
                }

                // 检查按钮是否可见
                var containerStyle = window.getComputedStyle(container);
                var buttonStyle = window.getComputedStyle(button);

                if (containerStyle.display === 'none' ||
                    containerStyle.visibility === 'hidden' ||
                    buttonStyle.display === 'none' ||
                    buttonStyle.visibility === 'hidden' ||
                    container.hidden || button.hidden) {
                    return { exists: false, reason: '按钮被隐藏' };
                }

                // 检查按钮位置是否正确
                var rect = container.getBoundingClientRect();
                if (rect.width === 0 || rect.height === 0) {
                    return { exists: false, reason: '按钮尺寸异常' };
                }

                // 检查按钮是否在视口外（可能被移动了）
                if (rect.top < -1000 || rect.left < -1000 ||
                    rect.top > window.innerHeight + 1000 ||
                    rect.left > window.innerWidth + 1000) {
                    return { exists: false, reason: '按钮位置异常' };
                }

                return { exists: true, reason: '按钮状态正常' };
            };

            // 1. 高频智能检查定时器
            window.buttonCheckInterval = setInterval(function() {
                var status = window.checkButtonStatus();
                if (!status.exists) {
                    console.log('定时检查：' + status.reason + '，重新创建...');
                    window.forceCreateLoginButton();
                }
            }, 150); // 进一步提升到150ms，更快响应

            // 2. 智能页面变化观察器
            window.buttonObserver = new MutationObserver(function(mutations) {
                var needsRecreate = false;
                var buttonContainer = document.getElementById('login-completion-container');
                var buttonElement = document.getElementById('login-completion-button');

                // 检查按钮完整性
                var isButtonIntact = buttonContainer && buttonElement &&
                                   buttonContainer.contains(buttonElement) &&
                                   buttonContainer.parentNode &&
                                   document.body.contains(buttonContainer);

                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        // 检查是否有关键节点被移除
                        for (var i = 0; i < mutation.removedNodes.length; i++) {
                            var node = mutation.removedNodes[i];
                            if (node.nodeType === 1) { // Element node
                                // 检查是否移除了按钮相关元素
                                if (node.id === 'login-completion-container' ||
                                    node.id === 'login-completion-button' ||
                                    (node.querySelector && (
                                        node.querySelector('#login-completion-container') ||
                                        node.querySelector('#login-completion-button')
                                    ))) {
                                    needsRecreate = true;
                                    console.log('检测到按钮相关元素被移除');
                                    break;
                                }

                                // 检查是否移除了包含按钮的父容器
                                if (buttonContainer && !document.body.contains(buttonContainer)) {
                                    needsRecreate = true;
                                    console.log('检测到按钮父容器被移除');
                                    break;
                                }
                            }
                        }

                        // 检查是否有大量DOM变化（可能是页面重构）
                        if (mutation.addedNodes.length > 10 || mutation.removedNodes.length > 10) {
                            if (!isButtonIntact) {
                                needsRecreate = true;
                                console.log('检测到大量DOM变化，按钮可能受影响');
                            }
                        }
                    }

                    // 检查属性变化是否影响按钮显示
                    if (mutation.type === 'attributes' && mutation.target) {
                        if (mutation.target.id === 'login-completion-container' ||
                            mutation.target.id === 'login-completion-button') {
                            // 检查关键属性变化
                            if (mutation.attributeName === 'style' ||
                                mutation.attributeName === 'class' ||
                                mutation.attributeName === 'hidden') {
                                var element = mutation.target;
                                var computedStyle = window.getComputedStyle(element);
                                if (computedStyle.display === 'none' ||
                                    computedStyle.visibility === 'hidden' ||
                                    element.hidden) {
                                    console.log('检测到按钮被隐藏，重新创建');
                                    needsRecreate = true;
                                }
                            }
                        }
                    }
                });

                // 最终检查：按钮是否完整存在且可见
                if (needsRecreate || !isButtonIntact) {
                    console.log('页面变化观察器：按钮需要重建');
                    setTimeout(function() {
                        window.forceCreateLoginButton();
                    }, 30); // 减少延迟到30ms
                }
            });

            // 开始观察整个文档 - 增强配置
            window.buttonObserver.observe(document, {
                childList: true,        // 监听子节点变化
                subtree: true,          // 监听所有后代节点
                attributes: true,       // 监听属性变化
                attributeOldValue: true, // 记录属性旧值
                characterData: false,   // 不监听文本内容变化
                attributeFilter: ['style', 'class', 'hidden', 'id'] // 只监听关键属性
            });

            // 额外观察body元素的直接变化
            if (document.body && window.buttonObserver) {
                window.buttonBodyObserver = new MutationObserver(function(mutations) {
                    var bodyChanged = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' &&
                            (mutation.addedNodes.length > 5 || mutation.removedNodes.length > 5)) {
                            bodyChanged = true;
                        }
                    });

                    if (bodyChanged && !document.getElementById('login-completion-container')) {
                        console.log('Body结构大幅变化，检查按钮...');
                        setTimeout(function() {
                            window.forceCreateLoginButton();
                        }, 20);
                    }
                });

                window.buttonBodyObserver.observe(document.body, {
                    childList: true,
                    subtree: false // 只观察body的直接子元素
                });
            }

            // 3. 页面事件监听器
            var recreateButton = function(eventName) {
                console.log(eventName + '：重新创建按钮...');
                setTimeout(function() {
                    window.forceCreateLoginButton();
                }, 100);
            };

            // 页面加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    recreateButton('DOMContentLoaded');
                });
            }

            // 页面显示（前进后退）
            window.addEventListener('pageshow', function() {
                recreateButton('pageshow');
            });

            // Hash变化（单页应用）
            window.addEventListener('hashchange', function() {
                recreateButton('hashchange');
            });

            // 历史状态变化
            window.addEventListener('popstate', function() {
                recreateButton('popstate');
            });

            // 4. 页面焦点事件
            window.addEventListener('focus', function() {
                setTimeout(function() {
                    if (!document.getElementById('login-completion-container')) {
                        recreateButton('window focus');
                    }
                }, 100);
            });

            // 5. 页面可见性变化
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    setTimeout(function() {
                        var status = window.checkButtonStatus();
                        if (!status.exists) {
                            recreateButton('visibility change: ' + status.reason);
                        }
                    }, 100);
                }
            });

            // 6. 页面内容变化检测（针对动态内容网站）
            window.lastPageSignature = document.title + '|' + (document.querySelector('h1') ? document.querySelector('h1').textContent : '') + '|' + window.location.href;

            window.pageChangeDetector = setInterval(function() {
                var currentSignature = document.title + '|' + (document.querySelector('h1') ? document.querySelector('h1').textContent : '') + '|' + window.location.href;

                if (currentSignature !== window.lastPageSignature) {
                    console.log('检测到页面内容变化');
                    window.lastPageSignature = currentSignature;

                    setTimeout(function() {
                        var status = window.checkButtonStatus();
                        if (!status.exists) {
                            console.log('页面内容变化后按钮丢失：' + status.reason);
                            window.forceCreateLoginButton();
                        }
                    }, 100);
                }
            }, 300); // 每300ms检查页面特征变化

            // 7. 窗口大小变化检测（可能影响按钮位置）
            window.addEventListener('resize', function() {
                setTimeout(function() {
                    var status = window.checkButtonStatus();
                    if (!status.exists) {
                        console.log('窗口大小变化后按钮异常：' + status.reason);
                        window.forceCreateLoginButton();
                    }
                }, 200);
            });

            // 8. 滚动事件检测（某些网站可能在滚动时修改DOM）
            var scrollCheckCount = 0;
            window.addEventListener('scroll', function() {
                scrollCheckCount++;
                if (scrollCheckCount % 10 === 0) { // 每10次滚动检查一次
                    var status = window.checkButtonStatus();
                    if (!status.exists) {
                        console.log('滚动过程中按钮丢失：' + status.reason);
                        window.forceCreateLoginButton();
                    }
                }
            });

            // 9. 页面卸载时清理资源
            window.addEventListener('beforeunload', function() {
                console.log('页面即将卸载，清理按钮相关资源...');

                if (window.buttonCheckInterval) {
                    clearInterval(window.buttonCheckInterval);
                }
                if (window.pageChangeDetector) {
                    clearInterval(window.pageChangeDetector);
                }
                if (window.buttonObserver) {
                    window.buttonObserver.disconnect();
                }
                if (window.buttonBodyObserver) {
                    window.buttonBodyObserver.disconnect();
                }
            });

            // 10. 页面加载完成后的最终检查
            if (document.readyState === 'complete') {
                setTimeout(function() {
                    var status = window.checkButtonStatus();
                    if (!status.exists) {
                        console.log('页面加载完成后最终检查：' + status.reason);
                        window.forceCreateLoginButton();
                    }
                }, 500);
            } else {
                window.addEventListener('load', function() {
                    setTimeout(function() {
                        var status = window.checkButtonStatus();
                        if (!status.exists) {
                            console.log('页面加载完成后最终检查：' + status.reason);
                            window.forceCreateLoginButton();
                        }
                    }, 500);
                });
            }
            """

            self.driver.execute_script(button_script)

            # 启动按钮监控
            self._start_button_monitoring(account_id)

        except Exception as e:
            print(f"添加登录完成按钮失败: {e}")

    def _start_button_monitoring(self, account_id: str):
        """启动按钮点击监控"""
        def monitor_button():
            try:
                while self.driver and not self.callback_results.get(account_id):
                    try:
                        # 检查浏览器窗口是否还存在
                        try:
                            # 首先处理可能的alert弹窗
                            try:
                                alert = self.driver.switch_to.alert
                                alert_text = alert.text
                                if "请确认您已完成登录操作" in alert_text:
                                    # 这是我们的确认对话框，自动点击确定
                                    alert.accept()
                                    print("自动确认登录完成对话框")
                                elif "未获取到登录态" in alert_text:
                                    # 这是失败提示，点击确定
                                    alert.accept()
                                    print("确认登录失败提示")
                                elif "登录成功" in alert_text:
                                    # 这是成功提示，点击确定
                                    alert.accept()
                                    print("确认登录成功提示")
                                else:
                                    # 其他alert，也点击确定
                                    alert.accept()
                                    print(f"处理其他alert: {alert_text}")
                            except:
                                # 没有alert，继续正常流程
                                pass

                            self.driver.current_url
                            # 检查按钮是否存在，如果不存在则跳过这次检查
                            button_exists = self.driver.execute_script("return !!document.getElementById('login-completion-container');")
                            if not button_exists:
                                print(f"按钮不存在，跳过本次检查...")
                                time.sleep(1)
                                continue

                            # 检查用户是否点击了完成登录按钮
                            login_completed = self.driver.execute_script("return window.loginCompleted;")
                            login_cancelled = self.driver.execute_script("return window.loginCancelled;")
                        except Exception as e:
                            # 浏览器窗口已关闭或其他错误
                            error_msg = str(e)
                            if "target window already closed" in error_msg or "web view not found" in error_msg:
                                print(f"浏览器窗口已关闭，停止监控账号 {account_id}")
                            elif "unexpected alert open" in error_msg:
                                print(f"检测到未处理的alert，尝试处理...")
                                try:
                                    alert = self.driver.switch_to.alert
                                    alert.accept()
                                    print("已处理alert，继续监控")
                                    continue
                                except:
                                    print("无法处理alert，停止监控")
                                    break
                            else:
                                print(f"浏览器访问错误: {error_msg}")
                            break

                        if login_cancelled:
                            # 用户取消了登录
                            self.callback_results[account_id] = (False, "用户取消登录")
                            print(f"用户取消了账号 {account_id} 的登录")
                            break

                        if login_completed:
                            # 用户点击了完成登录按钮，检查Cookie
                            cookies = self.driver.get_cookies()

                            if cookies:
                                # 检查是否包含有效的认证Cookie
                                auth_cookies = []
                                for cookie in cookies:
                                    # 检查常见的认证字段
                                    if any(field in cookie['name'].lower() for field in
                                          ['session', 'token', 'auth', 'login', 'user']):
                                        auth_cookies.append(cookie)

                                if auth_cookies:
                                    # 转换为Cookie字符串
                                    cookie_str = "; ".join([f"{c['name']}={c['value']}" for c in cookies])

                                    # 保存Cookie
                                    self.data_manager.save_cookie(account_id, cookie_str)

                                    print(f"✅ 成功获取账号 {account_id} 的Cookie")

                                    # 获取店铺名称
                                    self._fetch_and_save_shop_name(account_id, cookie_str)

                                    # 存储回调结果
                                    self.callback_results[account_id] = (True, "登录成功")
                                    print(f"登录成功回调结果已存储: {account_id}")

                                    # 更新按钮状态并弹出成功提示
                                    self.driver.execute_script("""
                                        // 弹出成功提示
                                        alert('登录成功！Cookie已获取，浏览器将在3秒后自动关闭。');

                                        window.loginButtonText = '登录成功！';
                                        window.loginButtonColor = '#52c41a';
                                        window.loginButtonDisabled = true;

                                        var button = document.getElementById('login-completion-button');
                                        if (button) {
                                            button.innerText = '登录成功！';
                                            button.style.background = '#52c41a';
                                            button.disabled = true;
                                            button.style.cursor = 'not-allowed';
                                            button.style.opacity = '0.6';
                                        }

                                        // 3秒后自动关闭浏览器
                                        setTimeout(function() {
                                            window.close();
                                        }, 3000);
                                    """)

                                    break
                                else:
                                    # 没有找到有效的认证Cookie
                                    self.driver.execute_script("""
                                        // 弹出提示框告知用户
                                        alert('未获取到登录态，请先完成登录操作后再点击按钮');

                                        // 重置按钮状态
                                        window.loginButtonText = '我已完成登录';
                                        window.loginButtonColor = '#1890ff';
                                        window.loginButtonDisabled = false;

                                        var button = document.getElementById('login-completion-button');
                                        if (button) {
                                            button.innerText = '我已完成登录';
                                            button.style.background = '#1890ff';
                                            button.disabled = false;
                                            button.style.cursor = 'pointer';
                                            button.style.opacity = '1';
                                        }
                                        window.loginCompleted = false;
                                    """)
                                    print(f"❌ 未获取到账号 {account_id} 的有效登录态，请先完成登录")
                            else:
                                # 没有Cookie
                                self.driver.execute_script("""
                                    // 弹出提示框告知用户
                                    alert('未获取到登录态，请先完成登录操作后再点击按钮');

                                    // 重置按钮状态
                                    window.loginButtonText = '我已完成登录';
                                    window.loginButtonColor = '#1890ff';
                                    window.loginButtonDisabled = false;

                                    var button = document.getElementById('login-completion-button');
                                    if (button) {
                                        button.innerText = '我已完成登录';
                                        button.style.background = '#1890ff';
                                        button.disabled = false;
                                        button.style.cursor = 'pointer';
                                        button.style.opacity = '1';
                                    }
                                    window.loginCompleted = false;
                                """)
                                print(f"❌ 未获取到账号 {account_id} 的Cookie，请先完成登录")

                        time.sleep(1)  # 每秒检查一次

                    except Exception as e:
                        print(f"监控按钮时出错: {e}")
                        time.sleep(1)

            except Exception as e:
                print(f"按钮监控线程出错: {e}")
            finally:
                # 确保在监控结束时设置默认结果
                if account_id not in self.callback_results or self.callback_results[account_id] is None:
                    self.callback_results[account_id] = (False, "登录超时或取消")

        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_button, daemon=True)
        monitor_thread.start()
    
    def start_cookie_monitoring(self, account_id: str, domain: str, timeout: int = 600):
        """
        开始监控Cookie获取
        
        Args:
            account_id: 账号ID
            domain: 目标域名
            timeout: 超时时间（秒）
        """
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_cookies,
            args=(account_id, domain, timeout)
        )
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_cookie_monitoring(self):
        """停止Cookie监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
    
    def _monitor_cookies(self, account_id: str, domain: str, timeout: int):
        """
        监控Cookie获取（后台线程）
        
        Args:
            account_id: 账号ID
            domain: 目标域名
            timeout: 超时时间（秒）
        """
        start_time = time.time()
        
        while self.monitoring and (time.time() - start_time) < timeout:
            try:
                # 提取Cookie
                cookies = self.extract_cookies_from_chrome(domain)
                
                if cookies:
                    # 转换为字符串格式
                    cookie_str = self.cookies_to_string(cookies)
                    
                    # 保存Cookie
                    self.data_manager.save_cookie(account_id, cookie_str)
                    
                    print(f"成功获取账号 {account_id} 的Cookie")
                    break
                
                # 等待一段时间再检查
                time.sleep(5)
                
            except Exception as e:
                print(f"监控Cookie时出错: {e}")
                time.sleep(5)
        
        self.monitoring = False

    def start_selenium_cookie_monitoring(self, account_id: str, login_url: str, timeout: int = 300):
        """
        启动selenium Cookie监控

        Args:
            account_id: 账号ID
            login_url: 登录URL
            timeout: 超时时间（秒）
        """
        if not self.driver:
            return

        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._selenium_monitor_cookies,
            args=(account_id, login_url, timeout)
        )
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

    def _selenium_monitor_cookies(self, account_id: str, login_url: str, timeout: int):
        """
        selenium Cookie监控线程

        Args:
            account_id: 账号ID
            login_url: 登录URL
            timeout: 超时时间（秒）
        """
        start_time = time.time()
        domain = urlparse(login_url).netloc

        print(f"开始监控账号 {account_id} 的登录状态...")

        try:
            while self.monitoring and (time.time() - start_time) < timeout and self.driver:
                try:
                    # 获取当前页面的Cookie
                    cookies = self.driver.get_cookies()

                    if cookies:
                        # 检查是否包含有效的认证Cookie
                        auth_cookies = []
                        for cookie in cookies:
                            # 检查常见的认证字段
                            if any(field in cookie['name'].lower() for field in
                                  ['session', 'token', 'auth', 'login', 'user']):
                                auth_cookies.append(cookie)

                        if auth_cookies:
                            # 转换为Cookie字符串
                            cookie_str = "; ".join([f"{c['name']}={c['value']}" for c in cookies])

                            # 保存Cookie
                            self.data_manager.save_cookie(account_id, cookie_str)

                            print(f"✅ 成功获取账号 {account_id} 的Cookie")

                            # 获取店铺名称
                            self._fetch_and_save_shop_name(account_id, cookie_str)

                            # 存储回调结果，避免直接调用UI
                            self.callback_results[account_id] = (True, "登录成功")
                            print(f"登录成功回调结果已存储: {account_id}")

                            break

                    # 检查是否已经跳转到登录后的页面
                    current_url = self.driver.current_url
                    if current_url != login_url and "login" not in current_url.lower():
                        # 可能已经登录成功，再次尝试获取Cookie
                        time.sleep(2)
                        continue

                    time.sleep(3)  # 每3秒检查一次

                except Exception as e:
                    print(f"监控Cookie时出错: {e}")
                    time.sleep(3)

            # 超时或监控结束
            if self.callback_results.get(account_id) is None:
                self.callback_results[account_id] = (False, "登录超时或取消")
                print(f"登录超时回调结果已存储: {account_id}")

            print(f"账号 {account_id} 的Cookie监控结束")

        except Exception as e:
            print(f"Cookie监控出错: {e}")
        finally:
            self.monitoring = False
            # 强制关闭浏览器和相关进程
            self.force_close_browser()

    def force_close_browser(self):
        """强制关闭浏览器和相关进程"""
        try:
            # 首先尝试正常关闭
            if self.driver:
                try:
                    self.driver.quit()
                except Exception as e:
                    print(f"正常关闭浏览器失败: {e}")

                self.driver = None

            # 强制杀死浏览器进程
            if self.driver_process:
                try:
                    # 获取所有子进程
                    children = self.driver_process.children(recursive=True)

                    # 先杀死子进程
                    for child in children:
                        try:
                            child.terminate()
                            child.wait(timeout=3)
                        except (psutil.NoSuchProcess, psutil.TimeoutExpired):
                            try:
                                child.kill()
                            except psutil.NoSuchProcess:
                                pass

                    # 再杀死主进程
                    if self.driver_process.is_running():
                        self.driver_process.terminate()
                        self.driver_process.wait(timeout=3)

                except (psutil.NoSuchProcess, psutil.TimeoutExpired):
                    try:
                        if self.driver_process.is_running():
                            self.driver_process.kill()
                    except psutil.NoSuchProcess:
                        pass
                except Exception as e:
                    print(f"强制关闭进程失败: {e}")

                self.driver_process = None

            # 额外清理：查找并杀死可能残留的Chrome进程
            self.cleanup_chrome_processes()

        except Exception as e:
            print(f"强制关闭浏览器时出错: {e}")

    def cleanup_chrome_processes(self):
        """清理可能残留的Chrome进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # 查找Chrome相关进程
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any('--incognito' in arg for arg in cmdline):
                            # 这是我们启动的无痕Chrome进程
                            proc.terminate()
                            proc.wait(timeout=3)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    pass
        except Exception as e:
            print(f"清理Chrome进程时出错: {e}")

    def stop_cookie_monitoring(self):
        """停止Cookie监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)

        # 强制关闭浏览器
        self.force_close_browser()

    def _fetch_and_save_shop_name(self, account_id: str, cookie_str: str):
        """获取并保存店铺名称"""
        try:
            # 获取账号信息
            accounts = self.data_manager.get_accounts()
            if account_id not in accounts:
                print(f"账号 {account_id} 不存在")
                return

            account_data = accounts[account_id]
            platform_id = account_data.get("platform_id")

            if not platform_id:
                print(f"账号 {account_id} 没有关联的平台")
                return

            # 获取平台信息
            platforms = self.data_manager.get_platforms()
            if platform_id not in platforms:
                print(f"平台 {platform_id} 不存在")
                return

            platform_data = platforms[platform_id]
            shop_name_api_url = platform_data.get("shop_name_api_url")
            shop_name_field = platform_data.get("shop_name_field")

            if not shop_name_api_url or not shop_name_field:
                print(f"平台 {platform_id} 未配置店铺名称接口")
                return

            # 获取店铺名称
            shop_name = self.shop_name_service.get_shop_name(
                shop_name_api_url, shop_name_field, cookie_str
            )

            if shop_name:
                # 保存店铺名称到账号信息
                account_data["shop_name"] = shop_name
                accounts[account_id] = account_data
                self.data_manager.save_accounts(accounts)
                print(f"✅ 成功保存账号 {account_id} 的店铺名称: {shop_name}")
            else:
                print(f"❌ 未能获取账号 {account_id} 的店铺名称")

        except Exception as e:
            print(f"获取店铺名称失败: {e}")

    def get_login_result(self, account_id: str):
        """获取登录结果（线程安全）"""
        if account_id in self.callback_results:
            result = self.callback_results[account_id]
            # 清理结果
            del self.callback_results[account_id]
            if account_id in self.login_callbacks:
                del self.login_callbacks[account_id]
            return result
        return None

    def validate_cookie(self, cookie_str: str, validation_url: str) -> bool:
        """
        验证Cookie是否有效
        
        Args:
            cookie_str: Cookie字符串
            validation_url: 验证URL
            
        Returns:
            Cookie是否有效
        """
        try:
            import requests
            
            headers = {
                "Cookie": cookie_str,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            response = requests.get(validation_url, headers=headers, timeout=10)
            
            # 简单验证：如果返回200且不是登录页面，则认为有效
            if response.status_code == 200:
                # 可以根据具体平台的响应内容进行更精确的验证
                return "login" not in response.url.lower()
            
            return False
            
        except Exception as e:
            print(f"验证Cookie失败: {e}")
            return False
    
    def get_account_cookie(self, account_id: str) -> Optional[str]:
        """获取账号的Cookie字符串"""
        cookie_data = self.data_manager.get_cookie(account_id)
        return cookie_data.get("cookie_str") if cookie_data else None
    
    def save_account_cookie(self, account_id: str, cookie_str: str):
        """保存账号Cookie"""
        self.data_manager.save_cookie(account_id, cookie_str)
    
    def delete_account_cookie(self, account_id: str):
        """删除账号Cookie"""
        self.data_manager.delete_cookie(account_id)

    def _start_wrapper_monitoring(self, account_id: str, login_url: str, callback=None):
        """启动包装页面监控"""
        def monitor_wrapper():
            try:
                print(f"开始监控包装页面中的登录状态...")
                start_time = time.time()
                timeout = 600  # 10分钟超时

                while time.time() - start_time < timeout:
                    try:
                        # 处理可能的alert弹窗
                        try:
                            alert = self.driver.switch_to.alert
                            alert_text = alert.text
                            if "请确认您已完成登录操作" in alert_text:
                                alert.accept()
                                print("自动确认登录完成对话框")
                            elif "未获取到登录态" in alert_text:
                                alert.accept()
                                print("确认登录失败提示")
                            elif "登录成功" in alert_text:
                                alert.accept()
                                print("确认登录成功提示")
                            else:
                                alert.accept()
                                print(f"处理其他alert: {alert_text}")
                        except:
                            pass

                        # 检查浏览器窗口是否还存在
                        try:
                            self.driver.current_url
                        except Exception as e:
                            error_msg = str(e)
                            if "target window already closed" in error_msg or "web view not found" in error_msg:
                                print(f"浏览器窗口已关闭，停止监控账号 {account_id}")
                                break
                            else:
                                print(f"浏览器访问错误: {error_msg}")
                                break

                        # 检查用户是否点击了完成登录按钮
                        login_completed = self.driver.execute_script("return window.loginCompleted;")

                        if login_completed:
                            print(f"用户点击了完成登录按钮，开始检查Cookie...")

                            # 切换到iframe检查Cookie
                            try:
                                # 等待iframe加载
                                time.sleep(2)

                                # 尝试切换到iframe
                                iframe = self.driver.find_element("css selector", ".login-iframe")
                                self.driver.switch_to.frame(iframe)

                                # 获取iframe中的Cookie
                                cookies = self.driver.get_cookies()

                                # 切换回主页面
                                self.driver.switch_to.default_content()

                                if cookies:
                                    # 过滤有效的认证Cookie
                                    auth_cookies = self._filter_auth_cookies(cookies)

                                    if auth_cookies:
                                        # 找到有效Cookie
                                        cookie_str = self.cookies_to_string(auth_cookies)

                                        # 保存Cookie
                                        self.data_manager.save_cookie(account_id, cookie_str)

                                        # 获取并保存店铺名称
                                        self._fetch_and_save_shop_name(account_id, cookie_str)

                                        # 调用成功回调
                                        self.driver.execute_script("onLoginSuccess();")

                                        # 设置结果
                                        self.callback_results[account_id] = (True, "登录成功")

                                        if callback:
                                            callback(True, "登录成功")

                                        print(f"✅ 成功获取账号 {account_id} 的登录态")
                                        break
                                    else:
                                        # 没有找到有效的认证Cookie
                                        self.driver.execute_script("onLoginFailure('未获取到有效的认证Cookie');")
                                        print(f"❌ 未获取到账号 {account_id} 的有效登录态")
                                else:
                                    # 没有Cookie
                                    self.driver.execute_script("onLoginFailure('未获取到Cookie');")
                                    print(f"❌ 未获取到账号 {account_id} 的Cookie")

                            except Exception as e:
                                print(f"检查iframe Cookie时出错: {e}")
                                self.driver.execute_script("onLoginFailure('检查登录状态时出错');")

                        time.sleep(1)  # 每秒检查一次

                    except Exception as e:
                        print(f"监控包装页面时出错: {e}")
                        time.sleep(1)

                # 超时处理
                if time.time() - start_time >= timeout:
                    print(f"监控超时，停止监控账号 {account_id}")
                    self.callback_results[account_id] = (False, "登录超时")
                    if callback:
                        callback(False, "登录超时")

            except Exception as e:
                print(f"包装页面监控线程出错: {e}")
                self.callback_results[account_id] = (False, f"监控出错: {e}")
                if callback:
                    callback(False, f"监控出错: {e}")
            finally:
                # 清理临时文件
                if hasattr(self, 'wrapper_file_path') and os.path.exists(self.wrapper_file_path):
                    try:
                        os.unlink(self.wrapper_file_path)
                        print("已清理临时包装页面文件")
                    except:
                        pass

        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_wrapper, daemon=True)
        monitor_thread.start()
