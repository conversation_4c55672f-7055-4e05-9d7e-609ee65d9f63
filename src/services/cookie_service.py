#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie服务类
处理浏览器Cookie的获取和管理
"""

import os
import sqlite3
import platform
import webbrowser
import time
import threading
from typing import Optional, Dict, List
from urllib.parse import urlparse
from src.utils.data_manager import DataManager


class CookieService:
    """Cookie服务类"""
    
    def __init__(self, data_manager: DataManager):
        """
        初始化Cookie服务
        
        Args:
            data_manager: 数据管理器实例
        """
        self.data_manager = data_manager
        self.monitoring = False
        self.monitor_thread = None
    
    def get_chrome_cookie_path(self) -> Optional[str]:
        """获取Chrome浏览器Cookie数据库路径"""
        system = platform.system()
        
        if system == "Windows":
            # Windows Chrome Cookie路径
            cookie_path = os.path.expanduser(
                "~\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cookies"
            )
        elif system == "Darwin":  # macOS
            # Mac Chrome Cookie路径
            cookie_path = os.path.expanduser(
                "~/Library/Application Support/Google/Chrome/Default/Cookies"
            )
        else:
            # Linux
            cookie_path = os.path.expanduser(
                "~/.config/google-chrome/Default/Cookies"
            )
        
        return cookie_path if os.path.exists(cookie_path) else None
    
    def extract_cookies_from_chrome(self, domain: str) -> List[Dict]:
        """
        从Chrome浏览器提取指定域名的Cookie
        
        Args:
            domain: 目标域名
            
        Returns:
            Cookie列表
        """
        cookie_path = self.get_chrome_cookie_path()
        if not cookie_path:
            return []
        
        cookies = []
        
        try:
            # 复制Cookie数据库文件（避免锁定问题）
            temp_cookie_path = cookie_path + ".temp"
            import shutil
            shutil.copy2(cookie_path, temp_cookie_path)
            
            # 连接SQLite数据库
            conn = sqlite3.connect(temp_cookie_path)
            cursor = conn.cursor()
            
            # 查询指定域名的Cookie
            query = """
                SELECT name, value, host_key, path, expires_utc, is_secure, is_httponly
                FROM cookies 
                WHERE host_key LIKE ?
            """
            
            cursor.execute(query, (f"%{domain}%",))
            rows = cursor.fetchall()
            
            for row in rows:
                cookie = {
                    "name": row[0],
                    "value": row[1],
                    "domain": row[2],
                    "path": row[3],
                    "expires": row[4],
                    "secure": bool(row[5]),
                    "httponly": bool(row[6])
                }
                cookies.append(cookie)
            
            conn.close()
            
            # 删除临时文件
            os.remove(temp_cookie_path)
            
        except Exception as e:
            print(f"提取Cookie失败: {e}")
        
        return cookies
    
    def cookies_to_string(self, cookies: List[Dict]) -> str:
        """将Cookie列表转换为字符串格式"""
        cookie_strings = []
        for cookie in cookies:
            cookie_strings.append(f"{cookie['name']}={cookie['value']}")
        return "; ".join(cookie_strings)
    
    def open_login_page(self, login_url: str):
        """打开登录页面"""
        webbrowser.open(login_url)
    
    def start_cookie_monitoring(self, account_id: str, domain: str, timeout: int = 600):
        """
        开始监控Cookie获取
        
        Args:
            account_id: 账号ID
            domain: 目标域名
            timeout: 超时时间（秒）
        """
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_cookies,
            args=(account_id, domain, timeout)
        )
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_cookie_monitoring(self):
        """停止Cookie监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
    
    def _monitor_cookies(self, account_id: str, domain: str, timeout: int):
        """
        监控Cookie获取（后台线程）
        
        Args:
            account_id: 账号ID
            domain: 目标域名
            timeout: 超时时间（秒）
        """
        start_time = time.time()
        
        while self.monitoring and (time.time() - start_time) < timeout:
            try:
                # 提取Cookie
                cookies = self.extract_cookies_from_chrome(domain)
                
                if cookies:
                    # 转换为字符串格式
                    cookie_str = self.cookies_to_string(cookies)
                    
                    # 保存Cookie
                    self.data_manager.save_cookie(account_id, cookie_str)
                    
                    print(f"成功获取账号 {account_id} 的Cookie")
                    break
                
                # 等待一段时间再检查
                time.sleep(5)
                
            except Exception as e:
                print(f"监控Cookie时出错: {e}")
                time.sleep(5)
        
        self.monitoring = False
    
    def validate_cookie(self, cookie_str: str, validation_url: str) -> bool:
        """
        验证Cookie是否有效
        
        Args:
            cookie_str: Cookie字符串
            validation_url: 验证URL
            
        Returns:
            Cookie是否有效
        """
        try:
            import requests
            
            headers = {
                "Cookie": cookie_str,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            response = requests.get(validation_url, headers=headers, timeout=10)
            
            # 简单验证：如果返回200且不是登录页面，则认为有效
            if response.status_code == 200:
                # 可以根据具体平台的响应内容进行更精确的验证
                return "login" not in response.url.lower()
            
            return False
            
        except Exception as e:
            print(f"验证Cookie失败: {e}")
            return False
    
    def get_account_cookie(self, account_id: str) -> Optional[str]:
        """获取账号的Cookie字符串"""
        cookie_data = self.data_manager.get_cookie(account_id)
        return cookie_data.get("cookie_str") if cookie_data else None
    
    def save_account_cookie(self, account_id: str, cookie_str: str):
        """保存账号Cookie"""
        self.data_manager.save_cookie(account_id, cookie_str)
    
    def delete_account_cookie(self, account_id: str):
        """删除账号Cookie"""
        self.data_manager.delete_cookie(account_id)
