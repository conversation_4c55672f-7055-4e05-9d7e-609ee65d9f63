#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie服务类
处理浏览器Cookie的获取和管理
"""

import os
import sqlite3
import platform
import webbrowser
import time
import threading
import signal
import subprocess
import psutil
from typing import Optional, Dict, List
from urllib.parse import urlparse
from src.utils.data_manager import DataManager
from src.services.shop_name_service import ShopNameService


try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False


class CookieService:
    """Cookie服务类"""
    
    def __init__(self, data_manager: DataManager):
        """
        初始化Cookie服务

        Args:
            data_manager: 数据管理器实例
        """
        self.data_manager = data_manager
        self.monitoring = False
        self.monitor_thread = None
        self.driver = None
        self.driver_process = None  # 存储浏览器进程
        self.login_callbacks = {}  # 存储登录回调函数
        self.callback_results = {}  # 存储回调结果，避免直接调用UI
        self.shop_name_service = ShopNameService()  # 店铺名称服务
    
    def get_chrome_cookie_path(self) -> Optional[str]:
        """获取Chrome浏览器Cookie数据库路径"""
        system = platform.system()
        
        if system == "Windows":
            # Windows Chrome Cookie路径
            cookie_path = os.path.expanduser(
                "~\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cookies"
            )
        elif system == "Darwin":  # macOS
            # Mac Chrome Cookie路径
            cookie_path = os.path.expanduser(
                "~/Library/Application Support/Google/Chrome/Default/Cookies"
            )
        else:
            # Linux
            cookie_path = os.path.expanduser(
                "~/.config/google-chrome/Default/Cookies"
            )
        
        return cookie_path if os.path.exists(cookie_path) else None
    
    def extract_cookies_from_chrome(self, domain: str) -> List[Dict]:
        """
        从Chrome浏览器提取指定域名的Cookie
        
        Args:
            domain: 目标域名
            
        Returns:
            Cookie列表
        """
        cookie_path = self.get_chrome_cookie_path()
        if not cookie_path:
            return []
        
        cookies = []
        
        try:
            # 复制Cookie数据库文件（避免锁定问题）
            temp_cookie_path = cookie_path + ".temp"
            import shutil
            shutil.copy2(cookie_path, temp_cookie_path)
            
            # 连接SQLite数据库
            conn = sqlite3.connect(temp_cookie_path)
            cursor = conn.cursor()
            
            # 查询指定域名的Cookie
            query = """
                SELECT name, value, host_key, path, expires_utc, is_secure, is_httponly
                FROM cookies 
                WHERE host_key LIKE ?
            """
            
            cursor.execute(query, (f"%{domain}%",))
            rows = cursor.fetchall()
            
            for row in rows:
                cookie = {
                    "name": row[0],
                    "value": row[1],
                    "domain": row[2],
                    "path": row[3],
                    "expires": row[4],
                    "secure": bool(row[5]),
                    "httponly": bool(row[6])
                }
                cookies.append(cookie)
            
            conn.close()
            
            # 删除临时文件
            os.remove(temp_cookie_path)
            
        except Exception as e:
            print(f"提取Cookie失败: {e}")
        
        return cookies
    
    def cookies_to_string(self, cookies: List[Dict]) -> str:
        """将Cookie列表转换为字符串格式"""
        cookie_strings = []
        for cookie in cookies:
            cookie_strings.append(f"{cookie['name']}={cookie['value']}")
        return "; ".join(cookie_strings)
    
    def open_login_page(self, login_url: str):
        """打开登录页面（传统方式）"""
        # 在URL中添加隐私参数
        privacy_url = f"{login_url}?__privacy_mode=1"

        # 打开后自动执行清理脚本
        webbrowser.open(privacy_url)

    def open_incognito_browser(self, login_url: str, account_id: str, callback=None):
        """
        使用selenium打开无痕浏览器进行登录

        Args:
            login_url: 登录URL
            account_id: 账号ID
            callback: 登录状态回调函数
        """
        if not SELENIUM_AVAILABLE:
            print("Selenium未安装，使用传统浏览器方式")
            self.open_login_page(login_url)
            return

        try:
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument("--incognito")  # 无痕模式
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 创建WebDriver
            driver_path = ChromeDriverManager().install()

            # 修复macOS上的路径和权限问题
            if platform.system() == "Darwin":
                if "THIRD_PARTY_NOTICES" in driver_path:
                    import glob
                    driver_dir = os.path.dirname(driver_path)
                    actual_drivers = glob.glob(os.path.join(driver_dir, "chromedriver*"))
                    if actual_drivers:
                        driver_path = actual_drivers[0]

                # 确保ChromeDriver有执行权限
                import stat
                current_permissions = os.stat(driver_path).st_mode
                os.chmod(driver_path, current_permissions | stat.S_IEXEC)

            self.driver = webdriver.Chrome(
                service=webdriver.chrome.service.Service(driver_path),
                options=chrome_options
            )

            # 获取浏览器进程信息
            self.driver_process = psutil.Process(self.driver.service.process.pid)

            # 隐藏自动化标识
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 打开登录页面
            self.driver.get(login_url)

            # 添加"我已完成登录"按钮
            self._add_login_completion_button(account_id)

            # 存储回调函数（避免直接调用UI）
            if callback:
                self.login_callbacks[account_id] = callback

            # 初始化回调结果（无论是否有回调都初始化）
            self.callback_results[account_id] = None

            print(f"已打开无痕浏览器，请在浏览器中完成登录，然后点击右上角的'我已完成登录'按钮")

        except Exception as e:
            print(f"打开无痕浏览器失败: {e}")
            # 降级到传统方式
            self.open_login_page(login_url)

    def _add_login_completion_button(self, account_id: str):
        """在浏览器右上角添加'我已完成登录'按钮"""
        try:
            # 注入CSS和JavaScript代码，包含页面变化监听
            button_script = """
            // 全局函数：创建登录按钮
            window.createLoginButton = function() {
                // 如果按钮已存在，不重复创建
                if (document.getElementById('login-completion-container')) {
                    return;
                }

                // 创建按钮容器
                var buttonContainer = document.createElement('div');
                buttonContainer.id = 'login-completion-container';
                buttonContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 999999;
                    background: white;
                    border: 2px solid #1890ff;
                    border-radius: 8px;
                    padding: 10px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                `;

                // 创建按钮
                var loginButton = document.createElement('button');
                loginButton.id = 'login-completion-button';
                loginButton.innerText = window.loginButtonText || '我已完成登录';
                loginButton.disabled = window.loginButtonDisabled || false;
                loginButton.style.cssText = `
                    background: ${window.loginButtonColor || '#1890ff'};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: ${window.loginButtonDisabled ? 'not-allowed' : 'pointer'};
                    font-size: 14px;
                    font-weight: bold;
                    margin-right: 8px;
                    opacity: ${window.loginButtonDisabled ? '0.6' : '1'};
                `;

                // 创建关闭按钮
                var closeButton = document.createElement('button');
                closeButton.innerText = '×';
                closeButton.style.cssText = `
                    background: #ff4d4f;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: bold;
                `;

                // 添加按钮到容器
                buttonContainer.appendChild(loginButton);
                buttonContainer.appendChild(closeButton);

                // 添加到页面
                document.body.appendChild(buttonContainer);

                // 设置按钮点击事件标记
                loginButton.onclick = function() {
                    if (!this.disabled) {
                        // 设置一个标记，表示用户点击了完成登录按钮
                        window.loginCompleted = true;
                        window.loginButtonText = '正在检查登录状态...';
                        window.loginButtonColor = '#52c41a';
                        window.loginButtonDisabled = true;

                        this.style.background = '#52c41a';
                        this.innerText = '正在检查登录状态...';
                        this.disabled = true;
                        this.style.cursor = 'not-allowed';
                        this.style.opacity = '0.6';
                    }
                };

                // 关闭按钮事件
                closeButton.onclick = function() {
                    window.loginCancelled = true;
                    buttonContainer.remove();
                };

                // 鼠标悬停效果
                loginButton.onmouseover = function() {
                    if (!this.disabled) {
                        this.style.background = '#40a9ff';
                    }
                };
                loginButton.onmouseout = function() {
                    if (!this.disabled) {
                        this.style.background = window.loginButtonColor || '#1890ff';
                    }
                };
            };

            // 立即创建按钮
            window.createLoginButton();

            // 监听页面变化，在页面跳转后重新创建按钮
            var observer = new MutationObserver(function(mutations) {
                // 检查是否需要重新创建按钮
                if (!document.getElementById('login-completion-container')) {
                    setTimeout(function() {
                        window.createLoginButton();
                    }, 500); // 延迟500ms确保页面加载完成
                }
            });

            // 开始观察
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // 监听页面加载事件
            window.addEventListener('load', function() {
                setTimeout(function() {
                    window.createLoginButton();
                }, 1000);
            });

            // 监听页面显示事件（处理前进后退）
            window.addEventListener('pageshow', function() {
                setTimeout(function() {
                    window.createLoginButton();
                }, 500);
            });
            """

            self.driver.execute_script(button_script)

            # 启动按钮监控
            self._start_button_monitoring(account_id)

        except Exception as e:
            print(f"添加登录完成按钮失败: {e}")

    def _start_button_monitoring(self, account_id: str):
        """启动按钮点击监控"""
        def monitor_button():
            try:
                while self.driver and not self.callback_results.get(account_id):
                    try:
                        # 检查浏览器窗口是否还存在
                        try:
                            self.driver.current_url
                        except Exception:
                            # 浏览器窗口已关闭
                            print(f"浏览器窗口已关闭，停止监控账号 {account_id}")
                            break

                        # 检查用户是否点击了完成登录按钮
                        login_completed = self.driver.execute_script("return window.loginCompleted;")
                        login_cancelled = self.driver.execute_script("return window.loginCancelled;")

                        if login_cancelled:
                            # 用户取消了登录
                            self.callback_results[account_id] = (False, "用户取消登录")
                            print(f"用户取消了账号 {account_id} 的登录")
                            break

                        if login_completed:
                            # 用户点击了完成登录按钮，检查Cookie
                            cookies = self.driver.get_cookies()

                            if cookies:
                                # 检查是否包含有效的认证Cookie
                                auth_cookies = []
                                for cookie in cookies:
                                    # 检查常见的认证字段
                                    if any(field in cookie['name'].lower() for field in
                                          ['session', 'token', 'auth', 'login', 'user']):
                                        auth_cookies.append(cookie)

                                if auth_cookies:
                                    # 转换为Cookie字符串
                                    cookie_str = "; ".join([f"{c['name']}={c['value']}" for c in cookies])

                                    # 保存Cookie
                                    self.data_manager.save_cookie(account_id, cookie_str)

                                    print(f"✅ 成功获取账号 {account_id} 的Cookie")

                                    # 获取店铺名称
                                    self._fetch_and_save_shop_name(account_id, cookie_str)

                                    # 存储回调结果
                                    self.callback_results[account_id] = (True, "登录成功")
                                    print(f"登录成功回调结果已存储: {account_id}")

                                    # 更新按钮状态
                                    self.driver.execute_script("""
                                        window.loginButtonText = '登录成功！';
                                        window.loginButtonColor = '#52c41a';
                                        window.loginButtonDisabled = true;

                                        var button = document.getElementById('login-completion-button');
                                        if (button) {
                                            button.innerText = '登录成功！';
                                            button.style.background = '#52c41a';
                                            button.disabled = true;
                                            button.style.cursor = 'not-allowed';
                                            button.style.opacity = '0.6';
                                        }
                                    """)

                                    break
                                else:
                                    # 没有找到有效的认证Cookie
                                    self.driver.execute_script("""
                                        window.loginButtonText = '未获取到登录态，请先完成登录';
                                        window.loginButtonColor = '#ff4d4f';
                                        window.loginButtonDisabled = false;

                                        var button = document.getElementById('login-completion-button');
                                        if (button) {
                                            button.innerText = '未获取到登录态，请先完成登录';
                                            button.style.background = '#ff4d4f';
                                            button.disabled = false;
                                            button.style.cursor = 'pointer';
                                            button.style.opacity = '1';
                                            setTimeout(function() {
                                                window.loginButtonText = '我已完成登录';
                                                window.loginButtonColor = '#1890ff';
                                                if (button) {
                                                    button.innerText = '我已完成登录';
                                                    button.style.background = '#1890ff';
                                                }
                                            }, 3000);
                                        }
                                        window.loginCompleted = false;
                                    """)
                                    print(f"❌ 未获取到账号 {account_id} 的有效登录态，请先完成登录")
                            else:
                                # 没有Cookie
                                self.driver.execute_script("""
                                    window.loginButtonText = '未获取到登录态，请先完成登录';
                                    window.loginButtonColor = '#ff4d4f';
                                    window.loginButtonDisabled = false;

                                    var button = document.getElementById('login-completion-button');
                                    if (button) {
                                        button.innerText = '未获取到登录态，请先完成登录';
                                        button.style.background = '#ff4d4f';
                                        button.disabled = false;
                                        button.style.cursor = 'pointer';
                                        button.style.opacity = '1';
                                        setTimeout(function() {
                                            window.loginButtonText = '我已完成登录';
                                            window.loginButtonColor = '#1890ff';
                                            if (button) {
                                                button.innerText = '我已完成登录';
                                                button.style.background = '#1890ff';
                                            }
                                        }, 3000);
                                    }
                                    window.loginCompleted = false;
                                """)
                                print(f"❌ 未获取到账号 {account_id} 的Cookie，请先完成登录")

                        time.sleep(1)  # 每秒检查一次

                    except Exception as e:
                        print(f"监控按钮时出错: {e}")
                        time.sleep(1)

            except Exception as e:
                print(f"按钮监控线程出错: {e}")
            finally:
                # 确保在监控结束时设置默认结果
                if account_id not in self.callback_results or self.callback_results[account_id] is None:
                    self.callback_results[account_id] = (False, "登录超时或取消")

        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_button, daemon=True)
        monitor_thread.start()
    
    def start_cookie_monitoring(self, account_id: str, domain: str, timeout: int = 600):
        """
        开始监控Cookie获取
        
        Args:
            account_id: 账号ID
            domain: 目标域名
            timeout: 超时时间（秒）
        """
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_cookies,
            args=(account_id, domain, timeout)
        )
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_cookie_monitoring(self):
        """停止Cookie监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
    
    def _monitor_cookies(self, account_id: str, domain: str, timeout: int):
        """
        监控Cookie获取（后台线程）
        
        Args:
            account_id: 账号ID
            domain: 目标域名
            timeout: 超时时间（秒）
        """
        start_time = time.time()
        
        while self.monitoring and (time.time() - start_time) < timeout:
            try:
                # 提取Cookie
                cookies = self.extract_cookies_from_chrome(domain)
                
                if cookies:
                    # 转换为字符串格式
                    cookie_str = self.cookies_to_string(cookies)
                    
                    # 保存Cookie
                    self.data_manager.save_cookie(account_id, cookie_str)
                    
                    print(f"成功获取账号 {account_id} 的Cookie")
                    break
                
                # 等待一段时间再检查
                time.sleep(5)
                
            except Exception as e:
                print(f"监控Cookie时出错: {e}")
                time.sleep(5)
        
        self.monitoring = False

    def start_selenium_cookie_monitoring(self, account_id: str, login_url: str, timeout: int = 300):
        """
        启动selenium Cookie监控

        Args:
            account_id: 账号ID
            login_url: 登录URL
            timeout: 超时时间（秒）
        """
        if not self.driver:
            return

        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._selenium_monitor_cookies,
            args=(account_id, login_url, timeout)
        )
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

    def _selenium_monitor_cookies(self, account_id: str, login_url: str, timeout: int):
        """
        selenium Cookie监控线程

        Args:
            account_id: 账号ID
            login_url: 登录URL
            timeout: 超时时间（秒）
        """
        start_time = time.time()
        domain = urlparse(login_url).netloc

        print(f"开始监控账号 {account_id} 的登录状态...")

        try:
            while self.monitoring and (time.time() - start_time) < timeout and self.driver:
                try:
                    # 获取当前页面的Cookie
                    cookies = self.driver.get_cookies()

                    if cookies:
                        # 检查是否包含有效的认证Cookie
                        auth_cookies = []
                        for cookie in cookies:
                            # 检查常见的认证字段
                            if any(field in cookie['name'].lower() for field in
                                  ['session', 'token', 'auth', 'login', 'user']):
                                auth_cookies.append(cookie)

                        if auth_cookies:
                            # 转换为Cookie字符串
                            cookie_str = "; ".join([f"{c['name']}={c['value']}" for c in cookies])

                            # 保存Cookie
                            self.data_manager.save_cookie(account_id, cookie_str)

                            print(f"✅ 成功获取账号 {account_id} 的Cookie")

                            # 获取店铺名称
                            self._fetch_and_save_shop_name(account_id, cookie_str)

                            # 存储回调结果，避免直接调用UI
                            self.callback_results[account_id] = (True, "登录成功")
                            print(f"登录成功回调结果已存储: {account_id}")

                            break

                    # 检查是否已经跳转到登录后的页面
                    current_url = self.driver.current_url
                    if current_url != login_url and "login" not in current_url.lower():
                        # 可能已经登录成功，再次尝试获取Cookie
                        time.sleep(2)
                        continue

                    time.sleep(3)  # 每3秒检查一次

                except Exception as e:
                    print(f"监控Cookie时出错: {e}")
                    time.sleep(3)

            # 超时或监控结束
            if self.callback_results.get(account_id) is None:
                self.callback_results[account_id] = (False, "登录超时或取消")
                print(f"登录超时回调结果已存储: {account_id}")

            print(f"账号 {account_id} 的Cookie监控结束")

        except Exception as e:
            print(f"Cookie监控出错: {e}")
        finally:
            self.monitoring = False
            # 强制关闭浏览器和相关进程
            self.force_close_browser()

    def force_close_browser(self):
        """强制关闭浏览器和相关进程"""
        try:
            # 首先尝试正常关闭
            if self.driver:
                try:
                    self.driver.quit()
                except Exception as e:
                    print(f"正常关闭浏览器失败: {e}")

                self.driver = None

            # 强制杀死浏览器进程
            if self.driver_process:
                try:
                    # 获取所有子进程
                    children = self.driver_process.children(recursive=True)

                    # 先杀死子进程
                    for child in children:
                        try:
                            child.terminate()
                            child.wait(timeout=3)
                        except (psutil.NoSuchProcess, psutil.TimeoutExpired):
                            try:
                                child.kill()
                            except psutil.NoSuchProcess:
                                pass

                    # 再杀死主进程
                    if self.driver_process.is_running():
                        self.driver_process.terminate()
                        self.driver_process.wait(timeout=3)

                except (psutil.NoSuchProcess, psutil.TimeoutExpired):
                    try:
                        if self.driver_process.is_running():
                            self.driver_process.kill()
                    except psutil.NoSuchProcess:
                        pass
                except Exception as e:
                    print(f"强制关闭进程失败: {e}")

                self.driver_process = None

            # 额外清理：查找并杀死可能残留的Chrome进程
            self.cleanup_chrome_processes()

        except Exception as e:
            print(f"强制关闭浏览器时出错: {e}")

    def cleanup_chrome_processes(self):
        """清理可能残留的Chrome进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # 查找Chrome相关进程
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any('--incognito' in arg for arg in cmdline):
                            # 这是我们启动的无痕Chrome进程
                            proc.terminate()
                            proc.wait(timeout=3)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    pass
        except Exception as e:
            print(f"清理Chrome进程时出错: {e}")

    def stop_cookie_monitoring(self):
        """停止Cookie监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)

        # 强制关闭浏览器
        self.force_close_browser()

    def _fetch_and_save_shop_name(self, account_id: str, cookie_str: str):
        """获取并保存店铺名称"""
        try:
            # 获取账号信息
            accounts = self.data_manager.get_accounts()
            if account_id not in accounts:
                print(f"账号 {account_id} 不存在")
                return

            account_data = accounts[account_id]
            platform_id = account_data.get("platform_id")

            if not platform_id:
                print(f"账号 {account_id} 没有关联的平台")
                return

            # 获取平台信息
            platforms = self.data_manager.get_platforms()
            if platform_id not in platforms:
                print(f"平台 {platform_id} 不存在")
                return

            platform_data = platforms[platform_id]
            shop_name_api_url = platform_data.get("shop_name_api_url")
            shop_name_field = platform_data.get("shop_name_field")

            if not shop_name_api_url or not shop_name_field:
                print(f"平台 {platform_id} 未配置店铺名称接口")
                return

            # 获取店铺名称
            shop_name = self.shop_name_service.get_shop_name(
                shop_name_api_url, shop_name_field, cookie_str
            )

            if shop_name:
                # 保存店铺名称到账号信息
                account_data["shop_name"] = shop_name
                accounts[account_id] = account_data
                self.data_manager.save_accounts(accounts)
                print(f"✅ 成功保存账号 {account_id} 的店铺名称: {shop_name}")
            else:
                print(f"❌ 未能获取账号 {account_id} 的店铺名称")

        except Exception as e:
            print(f"获取店铺名称失败: {e}")

    def get_login_result(self, account_id: str):
        """获取登录结果（线程安全）"""
        if account_id in self.callback_results:
            result = self.callback_results[account_id]
            # 清理结果
            del self.callback_results[account_id]
            if account_id in self.login_callbacks:
                del self.login_callbacks[account_id]
            return result
        return None

    def validate_cookie(self, cookie_str: str, validation_url: str) -> bool:
        """
        验证Cookie是否有效
        
        Args:
            cookie_str: Cookie字符串
            validation_url: 验证URL
            
        Returns:
            Cookie是否有效
        """
        try:
            import requests
            
            headers = {
                "Cookie": cookie_str,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            response = requests.get(validation_url, headers=headers, timeout=10)
            
            # 简单验证：如果返回200且不是登录页面，则认为有效
            if response.status_code == 200:
                # 可以根据具体平台的响应内容进行更精确的验证
                return "login" not in response.url.lower()
            
            return False
            
        except Exception as e:
            print(f"验证Cookie失败: {e}")
            return False
    
    def get_account_cookie(self, account_id: str) -> Optional[str]:
        """获取账号的Cookie字符串"""
        cookie_data = self.data_manager.get_cookie(account_id)
        return cookie_data.get("cookie_str") if cookie_data else None
    
    def save_account_cookie(self, account_id: str, cookie_str: str):
        """保存账号Cookie"""
        self.data_manager.save_cookie(account_id, cookie_str)
    
    def delete_account_cookie(self, account_id: str):
        """删除账号Cookie"""
        self.data_manager.delete_cookie(account_id)
