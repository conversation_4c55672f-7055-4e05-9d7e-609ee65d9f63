#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel导出服务类
处理数据导出为Excel文件
"""

import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter


class ExcelExporter:
    """Excel导出器"""
    
    def __init__(self):
        """初始化Excel导出器"""
        pass
    
    def export_single_account_data(self, scrape_result: Dict, output_dir: str = "exports") -> str:
        """
        导出单个账号的数据到Excel
        
        Args:
            scrape_result: 抓取结果
            output_dir: 输出目录
            
        Returns:
            生成的Excel文件路径
        """
        print("scrape_result",scrape_result)
        if not scrape_result.get("success"):
            raise ValueError(f"抓取失败: {scrape_result.get('error', '未知错误')}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        filename = self._generate_filename(
            scrape_result.get("platform_name", "未知平台"),
            scrape_result.get("account_name", "未知账号"),
            scrape_result.get("date_range", "")
        )
        
        filepath = os.path.join(output_dir, filename)
        
        # 创建工作簿
        wb = Workbook()
        
        # 处理数据
        data = scrape_result.get("data", [])
        raw_data = scrape_result.get("raw_data", {})
        
        if isinstance(raw_data, dict) and "data" in raw_data:
            # 处理小红书千帆数据结构
            self._export_xiaohongshu_data(wb, raw_data["data"])
        else:
            # 处理通用数据结构
            self._export_generic_data(wb, data)
        
        # 保存文件
        wb.save(filepath)
        return filepath
    
    def export_multiple_accounts_data(self, scrape_results: List[Dict], 
                                    output_dir: str = "exports", 
                                    merge_files: bool = False) -> List[str]:
        """
        导出多个账号的数据
        
        Args:
            scrape_results: 抓取结果列表
            output_dir: 输出目录
            merge_files: 是否合并为一个文件
            
        Returns:
            生成的Excel文件路径列表
        """
        if merge_files:
            return [self._export_merged_data(scrape_results, output_dir)]
        else:
            filepaths = []
            for result in scrape_results:
                if result.get("success"):
                    filepath = self.export_single_account_data(result, output_dir)
                    filepaths.append(filepath)
            return filepaths
    
    def _export_xiaohongshu_data(self, wb: Workbook, data_blocks: List[Dict]):
        """导出小红书千帆数据"""
        # 删除默认工作表
        if "Sheet" in wb.sheetnames:
            wb.remove(wb["Sheet"])
        
        for block in data_blocks:
            block_name = block.get("blockName", "数据")
            block_type = block.get("blockType", "unknown")
            block_data = block.get("data", [])
            meta = block.get("meta", {})
            
            # 创建工作表
            ws = wb.create_sheet(title=block_name[:31])  # Excel工作表名称限制31字符
            
            if block_type == "single_data":
                self._export_single_data_block(ws, block_data, meta)
            elif block_type == "time_line":
                self._export_timeline_data_block(ws, block_data, meta)
            else:
                self._export_generic_data_block(ws, block_data)
    
    def _export_single_data_block(self, ws, data: List[Dict], meta: Dict):
        """导出单数据块（总览数据）"""
        if not data:
            return
        
        # 获取指标信息
        metrics = meta.get("metrics", [])
        metric_map = {metric["key"]: metric["name"] for metric in metrics}
        
        # 设置表头
        headers = ["指标名称", "数值", "比例", "环比"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 填充数据
        row = 2
        for item in data:
            for key, value in item.items():
                if key == "dtm":  # 跳过时间字段
                    continue
                
                if isinstance(value, dict):
                    metric_name = metric_map.get(key, key)
                    metric_value = value.get("value", "")
                    proportion = value.get("proportion", "")
                    ratio = value.get("ratio", "")
                    
                    ws.cell(row=row, column=1, value=metric_name)
                    ws.cell(row=row, column=2, value=metric_value)
                    ws.cell(row=row, column=3, value=proportion)
                    ws.cell(row=row, column=4, value=ratio)
                    row += 1
        
        # 调整列宽
        for col in range(1, 5):
            ws.column_dimensions[get_column_letter(col)].width = 20
    
    def _export_timeline_data_block(self, ws, data: List[Dict], meta: Dict):
        """导出时间线数据块（趋势数据）"""
        if not data:
            return
        
        # 获取指标信息
        metrics = meta.get("metrics", [])
        metric_map = {metric["key"]: metric["name"] for metric in metrics}
        
        # 获取所有指标键
        all_keys = set()
        for item in data:
            all_keys.update(item.keys())
        
        # 排序指标键，时间字段放在第一列
        sorted_keys = ["dtm"] + [key for key in sorted(all_keys) if key != "dtm"]
        
        # 设置表头
        for col, key in enumerate(sorted_keys, 1):
            header = metric_map.get(key, key)
            if key == "dtm":
                header = "时间"
            
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 填充数据
        for row, item in enumerate(data, 2):
            for col, key in enumerate(sorted_keys, 1):
                value = item.get(key, "")
                if isinstance(value, dict):
                    value = value.get("value", "")
                
                ws.cell(row=row, column=col, value=value)
        
        # 调整列宽
        for col in range(1, len(sorted_keys) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 15
    
    def _export_generic_data_block(self, ws, data: List[Dict]):
        """导出通用数据块"""
        if not data:
            return
        
        # 获取所有字段
        all_keys = set()
        for item in data:
            all_keys.update(item.keys())
        
        sorted_keys = sorted(all_keys)
        
        # 设置表头
        for col, key in enumerate(sorted_keys, 1):
            cell = ws.cell(row=1, column=col, value=key)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 填充数据
        for row, item in enumerate(data, 2):
            for col, key in enumerate(sorted_keys, 1):
                value = item.get(key, "")
                ws.cell(row=row, column=col, value=value)
        
        # 调整列宽
        for col in range(1, len(sorted_keys) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 15
    
    def _export_generic_data(self, wb: Workbook, data: List[Dict]):
        """导出通用数据"""
        ws = wb.active
        ws.title = "数据"
        
        self._export_generic_data_block(ws, data)
    
    def _export_merged_data(self, scrape_results: List[Dict], output_dir: str) -> str:
        """导出合并的数据文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成合并文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"批量抓取数据_{timestamp}.xlsx"
        filepath = os.path.join(output_dir, filename)
        
        wb = Workbook()
        
        # 删除默认工作表
        if "Sheet" in wb.sheetnames:
            wb.remove(wb["Sheet"])
        
        for i, result in enumerate(scrape_results):
            if not result.get("success"):
                continue
            
            account_name = result.get("account_name", f"账号{i+1}")
            platform_name = result.get("platform_name", "未知平台")
            
            # 创建工作表
            sheet_name = f"{platform_name}_{account_name}"[:31]
            ws = wb.create_sheet(title=sheet_name)
            
            # 导出数据
            data = result.get("data", [])
            raw_data = result.get("raw_data", {})
            
            if isinstance(raw_data, dict) and "data" in raw_data:
                # 小红书数据结构，只取第一个数据块
                if raw_data["data"]:
                    first_block = raw_data["data"][0]
                    block_data = first_block.get("data", [])
                    meta = first_block.get("meta", {})
                    
                    if first_block.get("blockType") == "single_data":
                        self._export_single_data_block(ws, block_data, meta)
                    else:
                        self._export_timeline_data_block(ws, block_data, meta)
            else:
                self._export_generic_data_block(ws, data)
        
        wb.save(filepath)
        return filepath
    
    def _generate_filename(self, platform_name: str, account_name: str, date_range: str) -> str:
        """生成文件名"""
        # 清理文件名中的非法字符
        safe_platform = self._sanitize_filename(platform_name)
        safe_account = self._sanitize_filename(account_name)
        safe_date = self._sanitize_filename(date_range)
        
        if safe_date:
            filename = f"{safe_platform}-{safe_account}-{safe_date}.xlsx"
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{safe_platform}-{safe_account}-{timestamp}.xlsx"
        
        return filename
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名中的非法字符"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, "_")
        return filename.strip()
