#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台服务类
处理平台相关的业务逻辑
"""

from typing import List, Optional
from src.utils.data_manager import DataManager
from src.models.platform import Platform


class PlatformService:
    """平台服务类"""
    
    def __init__(self, data_manager: DataManager):
        """
        初始化平台服务
        
        Args:
            data_manager: 数据管理器实例
        """
        self.data_manager = data_manager
    
    def get_all_platforms(self) -> List[Platform]:
        """获取所有平台配置"""
        platforms_data = self.data_manager.get_platforms()
        platforms = []
        
        for platform_id, data in platforms_data.items():
            platform = Platform.from_dict(platform_id, data)
            platforms.append(platform)
        
        return platforms
    
    def get_platform_by_id(self, platform_id: str) -> Optional[Platform]:
        """根据ID获取平台配置"""
        platforms_data = self.data_manager.get_platforms()
        
        if platform_id in platforms_data:
            return Platform.from_dict(platform_id, platforms_data[platform_id])
        
        return None
    
    def add_platform(self, platform_name: str, login_url: str,
                    data_api_url: str, shop_name_api_url: str, shop_name_field: str,
                    extract_rule: str = None) -> str:
        """
        添加平台配置

        Returns:
            新创建的平台ID
        """
        return self.data_manager.add_platform(
            platform_name, login_url, data_api_url, shop_name_api_url, shop_name_field, extract_rule
        )
    
    def update_platform(self, platform_id: str, platform_name: str,
                        login_url: str, data_api_url: str, shop_name_api_url: str, shop_name_field: str,
                        extract_rule: str = None) -> bool:
        """
        更新平台配置

        Returns:
            是否更新成功
        """
        try:
            self.data_manager.update_platform(
                platform_id, platform_name, login_url, data_api_url, shop_name_api_url, shop_name_field, extract_rule
            )
            return True
        except Exception:
            return False
    
    def delete_platform(self, platform_id: str) -> bool:
        """
        删除平台配置
        
        Returns:
            是否删除成功
        """
        try:
            self.data_manager.delete_platform(platform_id)
            return True
        except Exception:
            return False
    
    def get_platform_names(self) -> List[tuple]:
        """
        获取平台名称列表（用于下拉框）
        
        Returns:
            [(platform_id, platform_name), ...]
        """
        platforms = self.get_all_platforms()
        return [(platform.platform_id, platform.platform_name) for platform in platforms]
