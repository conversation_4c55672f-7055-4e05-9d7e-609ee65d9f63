#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号服务类
处理账号相关的业务逻辑
"""

from typing import List, Optional, Tuple
from src.utils.data_manager import DataManager
from src.models.account import Account
from src.models.platform import Platform
from src.services.platform_service import PlatformService


class AccountService:
    """账号服务类"""
    
    def __init__(self, data_manager: DataManager):
        """
        初始化账号服务
        
        Args:
            data_manager: 数据管理器实例
        """
        self.data_manager = data_manager
        self.platform_service = PlatformService(data_manager)
    
    def get_all_accounts(self) -> List[Account]:
        """获取所有账号"""
        accounts_data = self.data_manager.get_accounts()
        accounts = []
        
        for account_id, data in accounts_data.items():
            account = Account.from_dict(account_id, data)
            accounts.append(account)
        
        return accounts
    
    def get_account_by_id(self, account_id: str) -> Optional[Account]:
        """根据ID获取账号"""
        accounts_data = self.data_manager.get_accounts()
        
        if account_id in accounts_data:
            return Account.from_dict(account_id, accounts_data[account_id])
        
        return None
    
    def add_account(self, account_name: str, platform_id: str) -> str:
        """
        添加账号
        
        Returns:
            新创建的账号ID
        """
        return self.data_manager.add_account(account_name, platform_id)
    
    def update_account(self, account_id: str, account_name: str, platform_id: str) -> bool:
        """
        更新账号信息
        
        Returns:
            是否更新成功
        """
        try:
            self.data_manager.update_account(account_id, account_name, platform_id)
            return True
        except Exception:
            return False
    
    def delete_account(self, account_id: str) -> bool:
        """
        删除账号
        
        Returns:
            是否删除成功
        """
        try:
            self.data_manager.delete_account(account_id)
            return True
        except Exception:
            return False
    
    def get_account_with_platform(self, account_id: str) -> Optional[Tuple[Account, Platform]]:
        """
        获取账号及其关联的平台信息
        
        Returns:
            (Account, Platform) 或 None
        """
        account = self.get_account_by_id(account_id)
        if not account:
            return None
        
        platform = self.platform_service.get_platform_by_id(account.platform_id)
        if not platform:
            return None
        
        return account, platform
    
    def get_accounts_with_platforms(self) -> List[Tuple[Account, Platform]]:
        """
        获取所有账号及其关联的平台信息
        
        Returns:
            [(Account, Platform), ...]
        """
        accounts = self.get_all_accounts()
        result = []
        
        for account in accounts:
            platform = self.platform_service.get_platform_by_id(account.platform_id)
            if platform:
                result.append((account, platform))
        
        return result
    
    def is_account_logged_in(self, account_id: str) -> bool:
        """检查账号是否已登录"""
        return self.data_manager.is_account_logged_in(account_id)

    def update_account_login_status(self, account_id: str, is_logged_in: bool):
        """更新账号登录状态"""
        if is_logged_in:
            # 如果设置为已登录，需要确保有Cookie
            cookie = self.data_manager.get_cookie(account_id)
            if not cookie:
                print(f"警告: 账号 {account_id} 没有Cookie，无法设置为已登录状态")
                return False
        else:
            # 如果设置为未登录，清除Cookie
            self.data_manager.clear_cookie(account_id)

        return True
    
    def get_logged_in_accounts(self) -> List[Account]:
        """获取所有已登录的账号"""
        accounts = self.get_all_accounts()
        logged_in_accounts = []
        
        for account in accounts:
            if self.is_account_logged_in(account.account_id):
                logged_in_accounts.append(account)
        
        return logged_in_accounts
