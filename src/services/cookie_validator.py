#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie验证服务
"""

import time
import requests
from typing import Op<PERSON>, Tuple
from src.utils.data_manager import DataManager
from src.services.data_scraper import DataScraper


class CookieValidator:
    """Cookie验证服务"""
    
    def __init__(self, data_manager: DataManager):
        self.data_manager = data_manager
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
    
    def validate_cookie(self, account_id: str) -> Tuple[bool, str]:
        """
        验证Cookie是否有效
        
        Args:
            account_id: 账号ID
            
        Returns:
            (is_valid, message): 验证结果和消息
        """
        try:
            # 获取Cookie
            cookie_data = self.data_manager.get_cookie(account_id)
            if not cookie_data:
                return False, "Cookie不存在"
            
            cookie_str = cookie_data.get("cookie_str")
            if not cookie_str:
                return False, "Cookie为空"
            
            # 获取账号和平台信息
            accounts = self.data_manager.get_accounts()
            if account_id not in accounts:
                return False, "账号不存在"
            
            account_data = accounts[account_id]
            platform_id = account_data.get("platform_id")
            
            if not platform_id:
                return False, "账号未关联平台"
            
            platforms = self.data_manager.get_platforms()
            if platform_id not in platforms:
                return False, "平台不存在"
            
            platform_data = platforms[platform_id]
            data_api_url = platform_data.get("data_api_url")
            
            if not data_api_url:
                return False, "平台未配置数据接口"
            
            print("validate_cookie请求参数:",cookie_str,data_api_url)
            # 使用Cookie请求数据接口验证
            return self._test_cookie_with_api(account_id)
            
        except Exception as e:
            return False, f"验证过程出错: {e}"
    
    def _test_cookie_with_api(self, account_id: str) -> Tuple[bool, str]:
        """
        使用数据抓取器测试Cookie有效性

        Args:
            account_id: 账号ID

        Returns:
            (is_valid, message): 验证结果和消息
        """
        try:
            print(f"开始验证账号 {account_id} 的Cookie有效性...")

            # 初始化数据抓取器
            data_scraper = DataScraper(self.data_manager)

            # 使用数据抓取器的scrape_xiaohongshu_data方法来验证cookie
            print(f"调用scrape_xiaohongshu_data方法验证cookie...")
            result = data_scraper.scrape_xiaohongshu_data(account_id)

            print(f"数据抓取结果: {result}")

            # 根据抓取结果判断cookie有效性
            if result and isinstance(result, dict):
                # 检查是否有错误信息
                if 'error' in result or 'err' in result:
                    error_msg = result.get('error') or result.get('err', '未知错误')
                    print(f"❌ Cookie验证失败: {error_msg}")
                    return False, f"Cookie无效: {error_msg}"

                # 检查是否有数据
                if 'data' in result and result['data']:
                    print(f"✅ Cookie验证成功，获取到数据")
                    return True, "Cookie有效"
                elif result:
                    # 有返回结果但格式可能不同
                    print(f"✅ Cookie验证成功，接口正常响应")
                    return True, "Cookie有效"
                else:
                    print(f"⚠️ Cookie可能有效，但未获取到数据")
                    return False, "Cookie可能已过期或权限不足"
            elif result is None:
                print(f"❌ Cookie验证失败，接口无响应")
                return False, "Cookie无效或接口无法访问"
            else:
                print(f"⚠️ Cookie验证结果不确定: {result}")
                return False, "Cookie验证结果不确定"
        except requests.exceptions.Timeout:
            return False, "请求超时"
        except requests.exceptions.ConnectionError:
            return False, "网络连接错误"
        except Exception as e:
            return False, f"请求失败: {e}"
    
    def validate_and_update_status(self, account_id: str) -> bool:
        """
        验证Cookie并更新账号登录状态
        
        Args:
            account_id: 账号ID
            
        Returns:
            Cookie是否有效
        """
        is_valid, message = self.validate_cookie(account_id)
        print("登录cookie验证：",is_valid,message)
        
        if not is_valid:
            # Cookie无效，清除Cookie
            self.data_manager.clear_cookie(account_id)
            print(f"账号 {account_id} Cookie验证失败: {message}")
        else:
            print(f"账号 {account_id} Cookie验证成功: {message}")
        
        return is_valid
