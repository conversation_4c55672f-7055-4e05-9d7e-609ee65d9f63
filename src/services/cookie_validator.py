#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie验证服务
"""

import requests
from typing import Op<PERSON>, Tuple
from src.utils.data_manager import DataManager


class CookieValidator:
    """Cookie验证服务"""
    
    def __init__(self, data_manager: DataManager):
        self.data_manager = data_manager
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
    
    def validate_cookie(self, account_id: str) -> Tuple[bool, str]:
        """
        验证Cookie是否有效
        
        Args:
            account_id: 账号ID
            
        Returns:
            (is_valid, message): 验证结果和消息
        """
        try:
            # 获取Cookie
            cookie_data = self.data_manager.get_cookie(account_id)
            if not cookie_data:
                return False, "Cookie不存在"
            
            cookie_str = cookie_data.get("cookie_str")
            if not cookie_str:
                return False, "Cookie为空"
            
            # 获取账号和平台信息
            accounts = self.data_manager.get_accounts()
            if account_id not in accounts:
                return False, "账号不存在"
            
            account_data = accounts[account_id]
            platform_id = account_data.get("platform_id")
            
            if not platform_id:
                return False, "账号未关联平台"
            
            platforms = self.data_manager.get_platforms()
            if platform_id not in platforms:
                return False, "平台不存在"
            
            platform_data = platforms[platform_id]
            data_api_url = platform_data.get("data_api_url")
            
            if not data_api_url:
                return False, "平台未配置数据接口"
            
            # 使用Cookie请求数据接口验证
            return self._test_cookie_with_api(cookie_str, data_api_url)
            
        except Exception as e:
            return False, f"验证过程出错: {e}"
    
    def _test_cookie_with_api(self, cookie_str: str, api_url: str) -> Tuple[bool, str]:
        """
        使用API测试Cookie有效性
        
        Args:
            cookie_str: Cookie字符串
            api_url: API地址
            
        Returns:
            (is_valid, message): 验证结果和消息
        """
        try:
            # 设置请求头
            headers = {
                'Cookie': cookie_str,
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': api_url.split('/api')[0] if '/api' in api_url else api_url,
                'X-Requested-With': 'XMLHttpRequest'
            }
            print("a------->",api_url.split('/api')[0] if '/api' in api_url else api_url)

            # 发送测试请求，添加正确的service参数
            params = {"service": "https%3A%2F%2Fark.xiaohongshu.com%2Fapp-seller%2FsellerInfo%3Ffrom%3Dark-login"}
            response = self.session.get(api_url, headers=headers, params=params, timeout=10)
            
            # 检查响应状态
            if response.status_code == 200:
                # 尝试解析JSON响应
                try:
                    data = response.json()
                    # 检查是否有错误信息
                    if isinstance(data, dict):
                        # 常见的错误标识
                        error_indicators = ['error', 'err', 'code', 'status']
                        for indicator in error_indicators:
                            if indicator in data:
                                error_value = data[indicator]
                                # 检查是否是错误状态
                                if (isinstance(error_value, int) and error_value != 0) or \
                                   (isinstance(error_value, str) and error_value.lower() in ['error', 'fail', 'unauthorized']):
                                    return False, f"API返回错误: {error_value}"
                    
                    return True, "Cookie有效"
                except ValueError:
                    # 不是JSON响应，但状态码200，可能仍然有效
                    return True, "Cookie可能有效（非JSON响应）"
            
            elif response.status_code == 401:
                return False, "Cookie已过期（401未授权）"
            elif response.status_code == 403:
                return False, "Cookie无权限（403禁止访问）"
            else:
                return False, f"API请求失败（状态码: {response.status_code}）"
                
        except requests.exceptions.Timeout:
            return False, "请求超时"
        except requests.exceptions.ConnectionError:
            return False, "网络连接错误"
        except Exception as e:
            return False, f"请求失败: {e}"
    
    def validate_and_update_status(self, account_id: str) -> bool:
        """
        验证Cookie并更新账号登录状态
        
        Args:
            account_id: 账号ID
            
        Returns:
            Cookie是否有效
        """
        is_valid, message = self.validate_cookie(account_id)
        
        if not is_valid:
            # Cookie无效，清除Cookie
            self.data_manager.clear_cookie(account_id)
            print(f"账号 {account_id} Cookie验证失败: {message}")
        else:
            print(f"账号 {account_id} Cookie验证成功: {message}")
        
        return is_valid
