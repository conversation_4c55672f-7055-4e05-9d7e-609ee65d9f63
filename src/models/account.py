#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号数据模型
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class Account:
    """账号数据模型"""
    account_id: str
    account_name: str
    platform_id: str
    create_time: str
    shop_name: Optional[str] = None  # 店铺名称
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {
            "account_name": self.account_name,
            "platform_id": self.platform_id,
            "create_time": self.create_time
        }
        if self.shop_name:
            result["shop_name"] = self.shop_name
        return result
    
    @classmethod
    def from_dict(cls, account_id: str, data: dict) -> 'Account':
        """从字典创建Account对象"""
        return cls(
            account_id=account_id,
            account_name=data.get("account_name", ""),
            platform_id=data.get("platform_id", ""),
            create_time=data.get("create_time", ""),
            shop_name=data.get("shop_name", None)
        )
