#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台数据模型
"""

from dataclasses import dataclass
from typing import Optional, List, Dict


@dataclass
class Platform:
    """平台配置数据模型"""
    platform_id: str
    platform_name: str
    login_url: str
    data_api_url: str
    shop_name_api_url: str  # 店铺名称接口URL
    shop_name_field: str    # 店铺名称字段
    field_mappings: Optional[List[Dict]] = None  # 字段映射配置
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {
            "platform_name": self.platform_name,
            "login_url": self.login_url,
            "data_api_url": self.data_api_url,
            "shop_name_api_url": self.shop_name_api_url,
            "shop_name_field": self.shop_name_field
        }
        if self.field_mappings:
            result["field_mappings"] = self.field_mappings
        return result
    
    @classmethod
    def from_dict(cls, platform_id: str, data: dict) -> 'Platform':
        """从字典创建Platform对象"""
        return cls(
            platform_id=platform_id,
            platform_name=data.get("platform_name", ""),
            login_url=data.get("login_url", ""),
            data_api_url=data.get("data_api_url", ""),
            shop_name_api_url=data.get("shop_name_api_url", ""),
            shop_name_field=data.get("shop_name_field", ""),
            field_mappings=data.get("field_mappings", None)
        )
