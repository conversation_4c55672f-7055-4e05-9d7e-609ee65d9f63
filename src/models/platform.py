#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台数据模型
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class Platform:
    """平台配置数据模型"""
    platform_id: str
    platform_name: str
    login_url: str
    data_api_url: str
    extract_rule: str
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "platform_name": self.platform_name,
            "login_url": self.login_url,
            "data_api_url": self.data_api_url,
            "extract_rule": self.extract_rule
        }
    
    @classmethod
    def from_dict(cls, platform_id: str, data: dict) -> 'Platform':
        """从字典创建Platform对象"""
        return cls(
            platform_id=platform_id,
            platform_name=data.get("platform_name", ""),
            login_url=data.get("login_url", ""),
            data_api_url=data.get("data_api_url", ""),
            extract_rule=data.get("extract_rule", "")
        )
