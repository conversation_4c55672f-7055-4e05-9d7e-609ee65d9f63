<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面 - 数据采集工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            min-height: 500px;
            min-width: 320px;  /* 降低最小宽度，支持更小的窗口 */
            overflow: hidden;
            position: relative;
        }
        
        /* 顶部按钮区域 */
        .header-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .header-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
        }
        
        .header-buttons {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .login-button {
            background: #52c41a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .login-button:hover {
            background: #45a017;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .login-button:active {
            transform: translateY(0);
        }
        
        .login-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff4d4f;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.success {
            background: #52c41a;
            animation: none;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        /* iframe容器 */
        .iframe-container {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            overflow: auto;  /* 改为auto，允许滚动而不是隐藏 */
            z-index: 1;
        }

        .login-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
            display: block;
            /* 移除最小宽度限制，让iframe自适应容器 */
        }
        
        /* 加载状态 */
        .loading-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            min-width: 800px;
            min-height: 540px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-left: 15px;
            color: #666;
            font-size: 14px;
        }
        
        /* 提示信息 */
        .info-panel {
            position: fixed;
            top: 70px;
            right: 20px;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 300px;
            z-index: 9998;
            border-left: 4px solid #1890ff;
        }
        
        .info-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .info-content {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
        }
        
        .close-info {
            position: absolute;
            top: 5px;
            right: 8px;
            background: none;
            border: none;
            font-size: 16px;
            color: #999;
            cursor: pointer;
        }

        /* 响应式设计和拖拽优化 */
        @media (max-width: 1200px) {
            .header-container {
                padding: 0 15px;
            }

            .header-title {
                font-size: 14px;
            }

            .login-button {
                padding: 6px 12px;
                font-size: 13px;
            }

            .info-panel {
                max-width: 250px;
                right: 15px;
            }
        }

        @media (max-width: 900px) {
            .header-container {
                padding: 0 10px;
            }

            .header-title {
                font-size: 13px;
            }

            .login-button {
                padding: 5px 10px;
                font-size: 12px;
            }

            .info-panel {
                max-width: 200px;
                right: 10px;
                top: 65px;
            }
        }

        /* 拖拽和缩放优化 */
        html {
            min-width: 320px;  /* 降低最小宽度，支持更小的窗口 */
            min-height: 500px;
        }

        body {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            text-size-adjust: 100%;
        }

        .iframe-container,
        .login-iframe {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        /* 防止内容溢出和错位 */
        * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
        }

        .header-container {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            will-change: transform;
        }
    </style>
</head>
<body>
    <!-- 顶部按钮区域 -->
    <div class="header-container">
        <div class="header-title">
            <span class="status-indicator" id="statusIndicator"></span>
            <span id="headerTitle">请完成登录操作</span>
        </div>
        <div class="header-buttons">
            <button class="login-button" id="loginButton" onclick="checkLogin()">
                我已完成登录
            </button>
        </div>
    </div>
    
    <!-- 信息提示面板 -->
    <div class="info-panel" id="infoPanel">
        <button class="close-info" onclick="closeInfo()">&times;</button>
        <div class="info-title">操作提示</div>
        <div class="info-content">
            1. 在下方页面中完成登录操作<br>
            2. 登录成功后点击"我已完成登录"按钮<br>
            3. 系统将自动检测并保存登录状态
        </div>
    </div>
    
    <!-- 加载状态 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载登录页面...</div>
    </div>
    
    <!-- iframe容器 -->
    <div class="iframe-container">
        <iframe
            class="login-iframe"
            id="loginIframe"
            src="about:blank"
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation allow-navigation allow-storage-access-by-user-activation allow-downloads"
            loading="eager"
            importance="high"
            allow="camera; microphone; geolocation; payment; encrypted-media"
            referrerpolicy="no-referrer-when-downgrade"
            credentialless="false">
        </iframe>
    </div>

    <script>
        // 全局变量
        window.accountId = '{{ACCOUNT_ID}}';
        window.loginUrl = '{{LOGIN_URL}}';
        window.loginCompleted = false;
        window.checkingLogin = false;
        
        // 强化的防重定向机制
        function preventRedirect() {
            console.log('启动强化防重定向机制');

            // 保存原始location对象
            const originalLocation = window.location;
            const originalHref = originalLocation.href;

            // 1. 防止beforeunload事件导致的跳转
            window.addEventListener('beforeunload', function(e) {
                if (!window.userClosing) {
                    console.log('阻止beforeunload跳转');
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }
            });

            // 2. 监听并阻止历史记录变化
            window.addEventListener('popstate', function(e) {
                console.log('检测到popstate，阻止历史记录变化');
                e.preventDefault();
                e.stopPropagation();
                // 强制回到原始状态
                history.pushState(null, '', originalHref);
            });

            // 3. 重写location对象的所有属性
            Object.defineProperty(window, 'location', {
                get: function() {
                    return {
                        href: originalHref,
                        protocol: originalLocation.protocol,
                        host: originalLocation.host,
                        hostname: originalLocation.hostname,
                        port: originalLocation.port,
                        pathname: originalLocation.pathname,
                        search: originalLocation.search,
                        hash: originalLocation.hash,
                        origin: originalLocation.origin,
                        assign: function(url) {
                            console.log('阻止location.assign重定向到:', url);
                        },
                        replace: function(url) {
                            console.log('阻止location.replace重定向到:', url);
                        },
                        reload: function() {
                            console.log('阻止location.reload');
                        },
                        toString: function() {
                            return originalHref;
                        }
                    };
                },
                set: function(value) {
                    console.log('阻止location设置重定向到:', value);
                }
            });

            // 4. 重写top和parent的location（防止iframe突破）
            try {
                Object.defineProperty(window, 'top', {
                    get: function() {
                        return {
                            location: window.location,
                            document: window.document,
                            window: window
                        };
                    }
                });

                Object.defineProperty(window, 'parent', {
                    get: function() {
                        return {
                            location: window.location,
                            document: window.document,
                            window: window
                        };
                    }
                });
            } catch (e) {
                console.log('无法重写top/parent对象:', e);
            }

            // 5. 重写history对象
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;

            history.pushState = function(state, title, url) {
                if (url && url !== originalHref && !url.startsWith('#')) {
                    console.log('阻止history.pushState重定向到:', url);
                    return;
                }
                return originalPushState.call(this, state, title, url);
            };

            history.replaceState = function(state, title, url) {
                if (url && url !== originalHref && !url.startsWith('#')) {
                    console.log('阻止history.replaceState重定向到:', url);
                    return;
                }
                return originalReplaceState.call(this, state, title, url);
            };

            // 6. 监听hashchange事件
            window.addEventListener('hashchange', function(e) {
                console.log('检测到hash变化，允许继续');
            });

            // 7. 定期检查URL是否被修改
            setInterval(function() {
                if (window.location.href !== originalHref) {
                    console.log('检测到URL被修改，强制恢复');
                    try {
                        history.replaceState(null, '', originalHref);
                    } catch (e) {
                        console.log('无法恢复URL:', e);
                    }
                }
            }, 1000);

            console.log('防重定向机制已启用，原始URL:', originalHref);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DOMContentLoaded 事件触发 ===');
            preventRedirect();
            initializePage();
        });

        // 备用初始化方案：如果DOMContentLoaded没有触发
        window.addEventListener('load', function() {
            console.log('=== window load 事件触发 ===');
            // 检查是否已经初始化
            const iframe = document.getElementById('loginIframe');
            if (iframe && iframe.src === 'about:blank' && window.loginUrl) {
                console.log('检测到iframe未加载，执行备用初始化');
                initializePage();
            }
        });

        // 立即执行初始化（如果DOM已经准备好）
        if (document.readyState === 'loading') {
            console.log('DOM正在加载，等待DOMContentLoaded');
        } else {
            console.log('DOM已准备好，立即初始化');
            preventRedirect();
            initializePage();
        }
        
        function initializePage() {
            console.log('=== 初始化登录包装页面 ===');
            console.log('账号ID:', window.accountId);
            console.log('登录URL:', window.loginUrl);

            // 检查必要的元素
            const iframe = document.getElementById('loginIframe');
            const button = document.getElementById('loginButton');
            const loading = document.getElementById('loadingOverlay');

            console.log('iframe元素:', iframe ? '✅ 存在' : '❌ 不存在');
            console.log('button元素:', button ? '✅ 存在' : '❌ 不存在');
            console.log('loading元素:', loading ? '✅ 存在' : '❌ 不存在');

            if (!window.loginUrl) {
                console.error('❌ 登录URL未定义，无法加载目标页面');
                updateStatus('error', '配置错误：登录URL未定义');
                return;
            }

            // 加载目标登录页面
            console.log('调用 loadLoginPage...');
            loadLoginPage();

            // 5秒后隐藏提示面板
            setTimeout(function() {
                closeInfo();
            }, 5000);

            console.log('=== 页面初始化完成 ===');
        }
        
        function loadLoginPage() {
            console.log('=== loadLoginPage 函数开始执行 ===');

            const iframe = document.getElementById('loginIframe');
            const loadingOverlay = document.getElementById('loadingOverlay');

            if (!iframe) {
                console.error('❌ 找不到iframe元素');
                return;
            }

            if (!loadingOverlay) {
                console.error('❌ 找不到loading元素');
                return;
            }

            console.log('✅ 找到iframe和loading元素');
            console.log('开始加载登录页面:', window.loginUrl);

            if (!window.loginUrl) {
                console.error('❌ window.loginUrl 未定义');
                return;
            }

            // 设置加载超时
            const loadTimeout = setTimeout(function() {
                console.log('页面加载超时，尝试隐藏loading');
                loadingOverlay.style.display = 'none';
                updateStatus('ready', '页面加载超时，请检查网络连接');
            }, 15000); // 15秒超时

            // 设置iframe加载事件
            iframe.onload = function() {
                console.log('✅ iframe onload事件触发');
                console.log('✅ iframe当前src:', iframe.src);
                clearTimeout(loadTimeout);

                // 延迟隐藏loading，确保页面内容已渲染
                setTimeout(function() {
                    console.log('✅ 登录页面加载完成，隐藏loading');
                    loadingOverlay.style.display = 'none';
                    updateStatus('ready', '请在下方页面完成登录');
                }, 1000);

                // 防止iframe内的页面跳转影响父页面
                try {
                    iframe.contentWindow.addEventListener('beforeunload', function(e) {
                        console.log('iframe页面即将跳转');
                    });
                } catch (e) {
                    console.log('iframe跨域，无法监听内部事件');
                }
            };

            iframe.onerror = function() {
                console.error('❌ iframe onerror事件触发');
                clearTimeout(loadTimeout);
                loadingOverlay.innerHTML = '<div style="color: #ff4d4f;">页面加载失败，请刷新重试</div>';
                updateStatus('error', '页面加载失败');
            };

            // 设置iframe沙箱限制
            console.log('设置iframe沙箱属性...');
            iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation-by-user-activation allow-storage-access-by-user-activation allow-downloads');

            // 设置iframe属性以提高兼容性
            iframe.setAttribute('loading', 'eager');
            iframe.setAttribute('importance', 'high');

            // 加载目标页面
            console.log('=== 设置iframe src ===');
            console.log('目标URL:', window.loginUrl);
            console.log('设置前iframe.src:', iframe.src);

            iframe.src = window.loginUrl;

            console.log('设置后iframe.src:', iframe.src);
            console.log('=== iframe src设置完成 ===');

            // 备用方案：如果onload事件没有触发，使用定时器检查
            let checkCount = 0;
            const checkInterval = setInterval(function() {
                checkCount++;
                console.log('检查iframe加载状态，第', checkCount, '次，当前src:', iframe.src);

                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc && iframeDoc.readyState === 'complete') {
                        console.log('通过定时器检测到页面加载完成');
                        clearInterval(checkInterval);
                        clearTimeout(loadTimeout);
                        loadingOverlay.style.display = 'none';
                        updateStatus('ready', '请在下方页面完成登录');
                    }
                } catch (e) {
                    console.log('跨域限制，无法检查iframe状态');
                }

                // 最多检查30次（30秒）
                if (checkCount >= 30) {
                    console.log('检查次数达到上限，停止检查');
                    clearInterval(checkInterval);
                }
            }, 1000);

            console.log('=== loadLoginPage 函数执行完成 ===');
        }


        
        function updateStatus(status, message) {
            const indicator = document.getElementById('statusIndicator');
            const title = document.getElementById('headerTitle');
            
            indicator.className = 'status-indicator';
            
            switch(status) {
                case 'ready':
                    indicator.classList.add('ready');
                    break;
                case 'checking':
                    indicator.classList.add('checking');
                    break;
                case 'success':
                    indicator.classList.add('success');
                    break;
                case 'error':
                    indicator.classList.add('error');
                    break;
            }
            
            title.textContent = message;
        }
        
        function checkLogin() {
            if (window.checkingLogin) {
                return;
            }

            // 弹出确认对话框
            if (!confirm('请确认您已完成登录操作。\n\n点击"确定"检查登录状态\n点击"取消"继续登录')) {
                return;
            }

            window.checkingLogin = true;
            const button = document.getElementById('loginButton');

            // 更新按钮状态
            button.disabled = true;
            button.textContent = '正在检查登录状态...';
            updateStatus('checking', '正在检查登录状态...');

            // 检查当前页面的cookie
            console.log('=== 开始检查当前页面Cookie ===');
            checkCurrentPageCookies();

            // 尝试从iframe中获取最新的签名头和cookie
            try {
                extractSignatureHeaders();
                extractIframeCookies();
            } catch (e) {
                console.log('无法从iframe获取签名头:', e);
            }

            // 设置完成标记，让Python端检测
            window.loginCompleted = true;

            console.log('用户点击完成登录按钮');
        }

        function checkCurrentPageCookies() {
            console.log('检查当前页面Cookie...');

            // 获取当前页面的所有cookie
            const cookies = document.cookie;
            console.log('当前页面Cookie:', cookies);

            if (cookies) {
                // 解析cookie
                const cookieArray = cookies.split(';').map(cookie => cookie.trim());
                console.log('Cookie数组:', cookieArray);

                // 查找重要的认证cookie
                const authCookies = cookieArray.filter(cookie => {
                    const cookieName = cookie.split('=')[0].toLowerCase();
                    return cookieName.includes('session') ||
                           cookieName.includes('token') ||
                           cookieName.includes('auth') ||
                           cookieName.includes('login') ||
                           cookieName.includes('user');
                });

                if (authCookies.length > 0) {
                    console.log('✅ 找到认证相关Cookie:', authCookies);
                    updateStatus('success', `找到 ${authCookies.length} 个认证Cookie`);

                    // 保存到全局变量供Python端获取
                    window.detectedCookies = {
                        all: cookies,
                        auth: authCookies,
                        timestamp: new Date().toISOString()
                    };
                } else {
                    console.log('⚠️ 未找到明显的认证Cookie');
                    updateStatus('warning', '未检测到明显的认证Cookie');

                    // 仍然保存所有cookie
                    window.detectedCookies = {
                        all: cookies,
                        auth: [],
                        timestamp: new Date().toISOString()
                    };
                }
            } else {
                console.log('❌ 当前页面没有Cookie');
                updateStatus('error', '当前页面没有检测到Cookie');
                window.detectedCookies = {
                    all: '',
                    auth: [],
                    timestamp: new Date().toISOString()
                };
            }
        }

        function extractIframeCookies() {
            console.log('尝试从iframe提取Cookie...');

            try {
                const iframe = document.getElementById('loginIframe');
                if (!iframe || !iframe.contentWindow) {
                    console.log('无法访问iframe');
                    return;
                }

                // 尝试获取iframe的cookie
                try {
                    const iframeCookies = iframe.contentDocument.cookie;
                    console.log('iframe Cookie:', iframeCookies);

                    if (iframeCookies) {
                        // 合并iframe的cookie到检测结果中
                        if (!window.detectedCookies) {
                            window.detectedCookies = { all: '', auth: [], timestamp: new Date().toISOString() };
                        }

                        window.detectedCookies.iframe = iframeCookies;
                        console.log('✅ 成功获取iframe Cookie');
                    }
                } catch (e) {
                    console.log('无法访问iframe Cookie（跨域限制）:', e);
                }

                // 尝试获取iframe的localStorage
                try {
                    const iframeLocalStorage = iframe.contentWindow.localStorage;
                    const storageData = {};

                    for (let i = 0; i < iframeLocalStorage.length; i++) {
                        const key = iframeLocalStorage.key(i);
                        storageData[key] = iframeLocalStorage.getItem(key);
                    }

                    if (Object.keys(storageData).length > 0) {
                        if (!window.detectedCookies) {
                            window.detectedCookies = { all: '', auth: [], timestamp: new Date().toISOString() };
                        }

                        window.detectedCookies.localStorage = storageData;
                        console.log('✅ 成功获取iframe localStorage:', storageData);
                    }
                } catch (e) {
                    console.log('无法访问iframe localStorage（跨域限制）:', e);
                }

            } catch (e) {
                console.log('提取iframe Cookie失败:', e);
            }
        }

        function extractSignatureHeaders() {
            console.log('尝试提取签名头...');

            try {
                const iframe = document.getElementById('loginIframe');
                if (!iframe || !iframe.contentWindow) {
                    console.log('无法访问iframe');
                    return;
                }

                // 尝试从iframe的localStorage或sessionStorage中获取签名信息
                const iframeWindow = iframe.contentWindow;

                // 检查是否可以访问iframe的存储
                try {
                    const localStorage = iframeWindow.localStorage;
                    const sessionStorage = iframeWindow.sessionStorage;

                    // 查找可能的签名相关数据
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && (key.includes('sign') || key.includes('token') || key.includes('auth'))) {
                            const value = localStorage.getItem(key);
                            console.log(`localStorage[${key}]:`, value);
                            window.extractedSignatures = window.extractedSignatures || {};
                            window.extractedSignatures[key] = value;
                        }
                    }

                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        if (key && (key.includes('sign') || key.includes('token') || key.includes('auth'))) {
                            const value = sessionStorage.getItem(key);
                            console.log(`sessionStorage[${key}]:`, value);
                            window.extractedSignatures = window.extractedSignatures || {};
                            window.extractedSignatures[key] = value;
                        }
                    }

                } catch (storageError) {
                    console.log('无法访问iframe存储:', storageError);
                }

                // 尝试从iframe的全局变量中获取签名信息
                try {
                    const iframeDoc = iframeWindow.document;
                    const scripts = iframeDoc.getElementsByTagName('script');

                    for (let script of scripts) {
                        if (script.textContent && (script.textContent.includes('x-s') || script.textContent.includes('x-t'))) {
                            console.log('找到可能包含签名的脚本');
                            // 这里可以进一步解析脚本内容
                        }
                    }
                } catch (docError) {
                    console.log('无法访问iframe文档:', docError);
                }

            } catch (e) {
                console.log('提取签名头失败:', e);
            }
        }
        
        function onLoginSuccess() {
            console.log('登录成功');
            const button = document.getElementById('loginButton');

            button.textContent = '登录成功！';
            button.style.background = '#52c41a';
            updateStatus('success', '登录成功！Cookie已保存');

            // 显示成功提示
            alert('登录成功！Cookie已获取，浏览器将在3秒后自动关闭。');

            // 标记用户主动关闭
            window.userClosing = true;

            // 3秒后关闭窗口
            setTimeout(function() {
                window.userClosing = true;
                window.close();
            }, 3000);
        }
        
        function onLoginFailure(message) {
            console.log('登录失败:', message);
            const button = document.getElementById('loginButton');
            
            // 重置按钮状态
            button.disabled = false;
            button.textContent = '我已完成登录';
            button.style.background = '#52c41a';
            updateStatus('ready', '请完成登录操作');
            
            window.checkingLogin = false;
            window.loginCompleted = false;
            
            // 显示失败提示
            alert('未获取到登录态，请先完成登录操作后再点击按钮');
        }
        
        function closeInfo() {
            const infoPanel = document.getElementById('infoPanel');
            infoPanel.style.display = 'none';
        }
        
        // 监听iframe内的页面变化（如果可能）
        setInterval(function() {
            try {
                const iframe = document.getElementById('loginIframe');
                if (iframe.contentWindow && iframe.contentWindow.location) {
                    // 可以访问iframe内容，说明同域
                    console.log('当前iframe URL:', iframe.contentWindow.location.href);
                }
            } catch (e) {
                // 跨域限制，无法访问iframe内容
                // 这是正常情况
            }
        }, 2000);

        // 最后的保险措施：定时检查iframe是否需要设置
        let initCheckCount = 0;
        const initCheckInterval = setInterval(function() {
            initCheckCount++;
            const iframe = document.getElementById('loginIframe');

            if (iframe && iframe.src === 'about:blank' && window.loginUrl) {
                console.log(`第${initCheckCount}次检查：iframe需要设置，执行设置`);
                iframe.src = window.loginUrl;
                console.log('强制设置iframe src为:', window.loginUrl);
            }

            // 检查10次后停止（10秒）
            if (initCheckCount >= 10) {
                clearInterval(initCheckInterval);
                console.log('iframe初始化检查结束');
            }
        }, 1000);
    </script>
</body>
</html>
