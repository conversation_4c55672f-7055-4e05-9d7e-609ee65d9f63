#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射配置对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QLineEdit, QComboBox,
                            QGroupBox, QTextEdit, QSplitter)
from PyQt5.QtCore import Qt
from typing import List, Dict, Optional


class FieldMappingDialog(QDialog):
    """字段映射配置对话框"""
    
    def __init__(self, platform_name: str, current_mappings: Optional[List[Dict]] = None, parent=None):
        """
        初始化字段映射配置对话框
        
        Args:
            platform_name: 平台名称
            current_mappings: 当前字段映射配置
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.platform_name = platform_name
        self.field_mappings = current_mappings or []
        
        self.init_ui()
        self.load_mappings()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"字段映射配置 - {self.platform_name}")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：字段映射配置
        left_widget = self.create_mapping_widget()
        splitter.addWidget(left_widget)
        
        # 右侧：预览和说明
        right_widget = self.create_preview_widget()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([500, 300])
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 添加字段按钮
        self.add_field_btn = QPushButton("添加字段")
        self.add_field_btn.clicked.connect(self.add_field_mapping)
        button_layout.addWidget(self.add_field_btn)
        
        # 删除字段按钮
        self.remove_field_btn = QPushButton("删除字段")
        self.remove_field_btn.clicked.connect(self.remove_field_mapping)
        button_layout.addWidget(self.remove_field_btn)
        
        button_layout.addStretch()
        
        # 保存按钮
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.save_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def create_mapping_widget(self):
        """创建字段映射配置组件"""
        group = QGroupBox("字段映射配置")
        layout = QVBoxLayout(group)
        
        # 字段映射表格
        self.mapping_table = QTableWidget()
        self.mapping_table.setColumnCount(4)
        self.mapping_table.setHorizontalHeaderLabels([
            "字段名称", "JSONPath路径", "数据类型", "默认值"
        ])
        
        # 设置表格样式
        header = self.mapping_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.mapping_table)
        
        return group
    
    def create_preview_widget(self):
        """创建预览和说明组件"""
        group = QGroupBox("配置说明")
        layout = QVBoxLayout(group)
        
        # 说明文本
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setMaximumHeight(200)
        help_text.setPlainText("""
字段映射配置说明：

1. 字段名称：导出到Excel中的列名
2. JSONPath路径：从API返回数据中提取该字段的路径
3. 数据类型：字段的数据类型（文本/数字/日期）
4. 默认值：当字段不存在时使用的默认值

JSONPath示例：
- $.data.name - 提取data对象中的name字段
- $.data.list[*].value - 提取list数组中所有元素的value字段
- $.data.stats.total - 提取嵌套对象中的total字段

配置完成后，系统将只提取配置的字段，而不是所有数据。
        """)
        layout.addWidget(help_text)
        
        # 预览区域
        preview_label = QLabel("配置预览：")
        layout.addWidget(preview_label)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setMaximumHeight(150)
        layout.addWidget(self.preview_text)
        
        return group
    
    def load_mappings(self):
        """加载现有的字段映射"""
        self.mapping_table.setRowCount(len(self.field_mappings))
        
        for row, mapping in enumerate(self.field_mappings):
            # 字段名称
            name_item = QTableWidgetItem(mapping.get("field_name", ""))
            self.mapping_table.setItem(row, 0, name_item)
            
            # JSONPath路径
            path_item = QTableWidgetItem(mapping.get("json_path", ""))
            self.mapping_table.setItem(row, 1, path_item)
            
            # 数据类型
            type_combo = QComboBox()
            type_combo.addItems(["文本", "数字", "日期"])
            type_combo.setCurrentText(mapping.get("data_type", "文本"))
            self.mapping_table.setCellWidget(row, 2, type_combo)
            
            # 默认值
            default_item = QTableWidgetItem(mapping.get("default_value", ""))
            self.mapping_table.setItem(row, 3, default_item)
        
        self.update_preview()
    
    def add_field_mapping(self):
        """添加字段映射"""
        row = self.mapping_table.rowCount()
        self.mapping_table.insertRow(row)
        
        # 设置默认值
        name_item = QTableWidgetItem("新字段")
        self.mapping_table.setItem(row, 0, name_item)
        
        path_item = QTableWidgetItem("$.data.")
        self.mapping_table.setItem(row, 1, path_item)
        
        type_combo = QComboBox()
        type_combo.addItems(["文本", "数字", "日期"])
        self.mapping_table.setCellWidget(row, 2, type_combo)
        
        default_item = QTableWidgetItem("")
        self.mapping_table.setItem(row, 3, default_item)
        
        self.update_preview()
    
    def remove_field_mapping(self):
        """删除字段映射"""
        current_row = self.mapping_table.currentRow()
        if current_row >= 0:
            self.mapping_table.removeRow(current_row)
            self.update_preview()
        else:
            QMessageBox.warning(self, "提示", "请选择要删除的字段")
    
    def update_preview(self):
        """更新预览"""
        mappings = self.get_field_mappings()
        
        preview_lines = []
        for mapping in mappings:
            line = f"• {mapping['field_name']} ← {mapping['json_path']} ({mapping['data_type']})"
            if mapping['default_value']:
                line += f" [默认: {mapping['default_value']}]"
            preview_lines.append(line)
        
        if preview_lines:
            self.preview_text.setPlainText("\n".join(preview_lines))
        else:
            self.preview_text.setPlainText("暂无字段配置")
    
    def get_field_mappings(self) -> List[Dict]:
        """获取字段映射配置"""
        mappings = []
        
        for row in range(self.mapping_table.rowCount()):
            name_item = self.mapping_table.item(row, 0)
            path_item = self.mapping_table.item(row, 1)
            type_widget = self.mapping_table.cellWidget(row, 2)
            default_item = self.mapping_table.item(row, 3)
            
            if name_item and path_item:
                mapping = {
                    "field_name": name_item.text().strip(),
                    "json_path": path_item.text().strip(),
                    "data_type": type_widget.currentText() if type_widget else "文本",
                    "default_value": default_item.text().strip() if default_item else ""
                }
                
                # 只添加有效的映射
                if mapping["field_name"] and mapping["json_path"]:
                    mappings.append(mapping)
        
        return mappings
    
    def accept(self):
        """确认按钮点击事件"""
        mappings = self.get_field_mappings()
        
        # 验证字段名称唯一性
        field_names = [m["field_name"] for m in mappings]
        if len(field_names) != len(set(field_names)):
            QMessageBox.warning(self, "配置错误", "字段名称不能重复")
            return
        
        # 验证JSONPath格式
        for mapping in mappings:
            if not mapping["json_path"].startswith("$"):
                QMessageBox.warning(self, "配置错误", 
                                  f"字段 '{mapping['field_name']}' 的JSONPath格式不正确，应以$开头")
                return
        
        self.field_mappings = mappings
        super().accept()
