#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号详情对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QTextEdit, QPushButton, QGroupBox)
from PyQt5.QtCore import Qt


class AccountDetailDialog(QDialog):
    """账号详情对话框"""
    
    def __init__(self, account_name: str, detail_info: str, parent=None):
        """
        初始化账号详情对话框
        
        Args:
            account_name: 账号名称
            detail_info: 详情信息
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.account_name = account_name
        self.detail_info = detail_info
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"账号详情 - {self.account_name}")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 详情信息显示区域
        detail_group = QGroupBox("详细信息")
        detail_layout = QVBoxLayout(detail_group)
        
        self.detail_text = QTextEdit()
        self.detail_text.setPlainText(self.detail_info)
        self.detail_text.setReadOnly(True)
        self.detail_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                padding: 10px;
                font-family: monospace;
                font-size: 12px;
                background-color: #fafafa;
            }
        """)
        detail_layout.addWidget(self.detail_text)
        
        layout.addWidget(detail_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        button_layout.addStretch()
        
        # 关闭按钮
        self.close_btn = QPushButton("关闭")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
