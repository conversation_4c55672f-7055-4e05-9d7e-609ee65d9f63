#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出选项对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QRadioButton, QGroupBox, QTextEdit,
                            QCheckBox, QButtonGroup)
from PyQt5.QtCore import Qt


class ExportOptionsDialog(QDialog):
    """导出选项对话框"""
    
    def __init__(self, has_field_mappings: bool = False, parent=None):
        """
        初始化导出选项对话框
        
        Args:
            has_field_mappings: 是否有字段映射配置
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.has_field_mappings = has_field_mappings
        self.export_mode = "all"  # 默认导出全部数据
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("导出选项")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("选择数据导出方式")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 导出模式选择组
        mode_group = QGroupBox("导出模式")
        mode_layout = QVBoxLayout(mode_group)
        
        # 创建按钮组
        self.mode_button_group = QButtonGroup()
        
        # 全部数据导出选项
        self.all_data_radio = QRadioButton("导出全部数据")
        self.all_data_radio.setChecked(True)
        self.mode_button_group.addButton(self.all_data_radio, 0)
        mode_layout.addWidget(self.all_data_radio)
        
        # 全部数据说明
        all_data_desc = QLabel("• 导出API返回的所有字段和数据\n• 包含完整的原始数据信息\n• 适合需要完整数据分析的场景")
        all_data_desc.setStyleSheet("color: #666; margin-left: 20px; margin-bottom: 10px;")
        mode_layout.addWidget(all_data_desc)
        
        # 精确字段导出选项
        self.field_mapping_radio = QRadioButton("导出精确字段数据")
        self.mode_button_group.addButton(self.field_mapping_radio, 1)
        mode_layout.addWidget(self.field_mapping_radio)
        
        # 精确字段说明
        if self.has_field_mappings:
            field_desc = QLabel("• 只导出平台配置中指定的字段\n• 数据经过字段映射和类型转换\n• 文件更小，数据更精确")
            field_desc.setStyleSheet("color: #666; margin-left: 20px; margin-bottom: 10px;")
        else:
            field_desc = QLabel("• 该平台暂未配置字段映射\n• 请先在平台配置中设置字段映射\n• 此选项暂不可用")
            field_desc.setStyleSheet("color: #999; margin-left: 20px; margin-bottom: 10px;")
            self.field_mapping_radio.setEnabled(False)
        
        mode_layout.addWidget(field_desc)
        
        layout.addWidget(mode_group)
        
        # 附加选项组
        options_group = QGroupBox("附加选项")
        options_layout = QVBoxLayout(options_group)
        
        # 包含原始数据选项
        self.include_raw_data_checkbox = QCheckBox("同时包含原始数据工作表")
        self.include_raw_data_checkbox.setToolTip("在Excel文件中添加一个包含完整原始数据的工作表")
        options_layout.addWidget(self.include_raw_data_checkbox)
        
        # 数据统计选项
        self.include_stats_checkbox = QCheckBox("包含数据统计信息")
        self.include_stats_checkbox.setChecked(True)
        self.include_stats_checkbox.setToolTip("在Excel文件中添加数据统计信息（记录数量、导出时间等）")
        options_layout.addWidget(self.include_stats_checkbox)
        
        layout.addWidget(options_group)
        
        # 预览区域
        preview_group = QGroupBox("导出预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setMaximumHeight(100)
        self.update_preview()
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 确定按钮
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        button_layout.addWidget(self.ok_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #333;
                border: 1px solid #d9d9d9;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
                border-color: #1890ff;
            }
        """)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.mode_button_group.buttonClicked.connect(self.update_preview)
        self.include_raw_data_checkbox.toggled.connect(self.update_preview)
        self.include_stats_checkbox.toggled.connect(self.update_preview)
    
    def update_preview(self):
        """更新导出预览"""
        if self.all_data_radio.isChecked():
            mode_text = "导出模式: 全部数据"
            detail_text = "将导出API返回的所有字段和数据"
            self.export_mode = "all"
        else:
            mode_text = "导出模式: 精确字段数据"
            detail_text = "将只导出平台配置中指定的字段"
            self.export_mode = "field_mapping"
        
        options = []
        if self.include_raw_data_checkbox.isChecked():
            options.append("包含原始数据工作表")
        if self.include_stats_checkbox.isChecked():
            options.append("包含数据统计信息")
        
        preview_lines = [
            mode_text,
            detail_text,
            ""
        ]
        
        if options:
            preview_lines.append("附加选项:")
            for option in options:
                preview_lines.append(f"• {option}")
        
        self.preview_text.setPlainText("\n".join(preview_lines))
    
    def get_export_options(self) -> dict:
        """获取导出选项"""
        return {
            "mode": self.export_mode,
            "include_raw_data": self.include_raw_data_checkbox.isChecked(),
            "include_stats": self.include_stats_checkbox.isChecked()
        }
