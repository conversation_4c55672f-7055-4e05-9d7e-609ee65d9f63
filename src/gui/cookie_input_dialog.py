#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie输入对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QTextEdit, QPushButton, QGroupBox, QMessageBox)
from PyQt5.QtCore import Qt


class CookieInputDialog(QDialog):
    """Cookie输入对话框"""
    
    def __init__(self, account_name: str, current_cookie: str = "", parent=None):
        """
        初始化Cookie输入对话框
        
        Args:
            account_name: 账号名称
            current_cookie: 当前Cookie值
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.account_name = account_name
        self.current_cookie = current_cookie
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"设置Cookie - {self.account_name}")
        self.setModal(True)
        self.resize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # 说明信息
        info_group = QGroupBox("使用说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QLabel("""
1. 请在浏览器中登录对应平台账号
2. 按F12打开开发者工具，切换到Network标签页
3. 刷新页面，找到任意请求，查看Request Headers中的Cookie
4. 复制完整的Cookie字符串粘贴到下方文本框中
5. Cookie格式示例：sessionid=xxx; token=yyy; userid=zzz

注意：Cookie包含敏感信息，请确保在安全环境下操作
        """)
        info_text.setStyleSheet("color: #666; font-size: 12px; padding: 10px;")
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        
        # Cookie输入区域
        cookie_group = QGroupBox("Cookie设置")
        cookie_layout = QVBoxLayout(cookie_group)
        
        cookie_label = QLabel("Cookie字符串:")
        cookie_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        cookie_layout.addWidget(cookie_label)
        
        self.cookie_edit = QTextEdit()
        self.cookie_edit.setPlaceholderText("请粘贴完整的Cookie字符串...")
        self.cookie_edit.setPlainText(self.current_cookie)
        self.cookie_edit.setStyleSheet("""
            QTextEdit {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                padding: 8px;
                font-family: monospace;
                font-size: 12px;
            }
        """)
        cookie_layout.addWidget(self.cookie_edit)
        
        layout.addWidget(cookie_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 验证按钮
        self.validate_btn = QPushButton("验证Cookie")
        self.validate_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        self.validate_btn.clicked.connect(self.validate_cookie)
        button_layout.addWidget(self.validate_btn)
        
        button_layout.addStretch()
        
        # 保存按钮
        self.save_btn = QPushButton("保存")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.save_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.save_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #333;
                border: 1px solid #d9d9d9;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
                border-color: #1890ff;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def validate_cookie(self):
        """验证Cookie格式"""
        cookie_str = self.cookie_edit.toPlainText().strip()
        
        if not cookie_str:
            QMessageBox.warning(self, "验证失败", "Cookie不能为空")
            return
        
        # 简单验证Cookie格式
        if "=" not in cookie_str:
            QMessageBox.warning(self, "验证失败", "Cookie格式不正确，应包含键值对")
            return
        
        # 检查是否包含常见的Cookie字段
        common_fields = ["sessionid", "token", "userid", "session", "auth"]
        has_common_field = any(field in cookie_str.lower() for field in common_fields)
        
        if has_common_field:
            QMessageBox.information(self, "验证成功", "Cookie格式看起来正确")
        else:
            reply = QMessageBox.question(
                self, 
                "验证警告", 
                "Cookie中未发现常见的认证字段，确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return
    
    def get_cookie(self) -> str:
        """获取输入的Cookie字符串"""
        return self.cookie_edit.toPlainText().strip()
    
    def accept(self):
        """确认按钮点击事件"""
        cookie_str = self.get_cookie()
        
        if not cookie_str:
            QMessageBox.warning(self, "输入错误", "请输入Cookie字符串")
            return
        
        super().accept()
