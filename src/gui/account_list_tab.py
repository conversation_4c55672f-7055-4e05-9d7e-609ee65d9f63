#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号列表标签页
"""

import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QHeaderView,
                            QMessageBox, QDialog, QFileDialog, QProgressDialog,
                            QApplication)
from PyQt5.QtCore import Qt, QThread, pyqtSignal

from src.utils.data_manager import DataManager
from src.services.account_service import AccountService
from src.services.platform_service import PlatformService
from src.services.cookie_service import CookieService
from src.services.data_scraper import DataScraper
from src.services.excel_exporter import ExcelExporter
from src.gui.date_range_dialog import DateRangeDialog
from src.gui.account_edit_dialog import AccountEditDialog


class ScrapeWorker(QThread):
    """数据抓取工作线程"""
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, scraper, account_id, start_date, end_date):
        super().__init__()
        self.scraper = scraper
        self.account_id = account_id
        self.start_date = start_date
        self.end_date = end_date
    
    def run(self):
        try:
            result = self.scraper.scrape_single_account(
                self.account_id, self.start_date, self.end_date
            )
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))


class BatchScrapeWorker(QThread):
    """批量抓取工作线程"""
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    progress = pyqtSignal(int, str)
    
    def __init__(self, scraper, start_date, end_date):
        super().__init__()
        self.scraper = scraper
        self.start_date = start_date
        self.end_date = end_date
    
    def run(self):
        try:
            # 获取已登录账号
            logged_in_accounts = self.scraper.account_service.get_logged_in_accounts()
            total = len(logged_in_accounts)
            results = []
            
            for i, account in enumerate(logged_in_accounts):
                self.progress.emit(i + 1, f"正在抓取: {account.account_name}")
                
                result = self.scraper.scrape_single_account(
                    account.account_id, self.start_date, self.end_date
                )
                results.append(result)
            
            self.finished.emit(results)
        except Exception as e:
            self.error.emit(str(e))


class AccountListTab(QWidget):
    """账号列表标签页"""
    
    def __init__(self, data_manager: DataManager):
        """初始化账号列表标签页"""
        super().__init__()
        
        self.data_manager = data_manager
        self.account_service = AccountService(data_manager)
        self.platform_service = PlatformService(data_manager)
        self.cookie_service = CookieService(data_manager)
        self.data_scraper = DataScraper(data_manager)
        self.excel_exporter = ExcelExporter()
        
        self.init_ui()
        self.refresh_accounts()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 顶部按钮区域
        button_layout = QHBoxLayout()
        
        self.batch_scrape_btn = QPushButton("全部抓取")
        self.batch_scrape_btn.clicked.connect(self.batch_scrape)
        button_layout.addWidget(self.batch_scrape_btn)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 账号列表表格
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "账号名称", "关联平台", "登录状态", "编辑", "登录", "抓取"
        ])
        
        # 设置表格属性
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.table)
    
    def refresh_accounts(self):
        """刷新账号列表"""
        accounts_with_platforms = self.account_service.get_accounts_with_platforms()
        
        self.table.setRowCount(len(accounts_with_platforms))
        
        for row, (account, platform) in enumerate(accounts_with_platforms):
            # 账号名称
            self.table.setItem(row, 0, QTableWidgetItem(account.account_name))
            
            # 关联平台
            self.table.setItem(row, 1, QTableWidgetItem(platform.platform_name))
            
            # 登录状态
            is_logged_in = self.account_service.is_account_logged_in(account.account_id)
            status_text = "已登录" if is_logged_in else "未登录"
            self.table.setItem(row, 2, QTableWidgetItem(status_text))
            
            # 编辑按钮
            edit_btn = QPushButton("编辑")
            edit_btn.clicked.connect(lambda checked, aid=account.account_id: self.edit_account(aid))
            self.table.setCellWidget(row, 3, edit_btn)
            
            # 登录按钮
            login_btn = QPushButton("开始登录")
            login_btn.clicked.connect(lambda checked, aid=account.account_id, pid=platform.platform_id: 
                                    self.start_login(aid, pid))
            self.table.setCellWidget(row, 4, login_btn)
            
            # 抓取按钮
            scrape_btn = QPushButton("抓取")
            scrape_btn.setEnabled(is_logged_in)
            scrape_btn.clicked.connect(lambda checked, aid=account.account_id: self.scrape_account(aid))
            self.table.setCellWidget(row, 5, scrape_btn)
    
    def edit_account(self, account_id: str):
        """编辑账号"""
        dialog = AccountEditDialog(self.data_manager, account_id, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_accounts()
    
    def start_login(self, account_id: str, platform_id: str):
        """开始登录"""
        platform = self.platform_service.get_platform_by_id(platform_id)
        if not platform:
            QMessageBox.warning(self, "错误", "平台信息不存在")
            return
        
        # 打开登录页面
        self.cookie_service.open_login_page(platform.login_url)
        
        # 开始监控Cookie
        from urllib.parse import urlparse
        domain = urlparse(platform.login_url).netloc
        
        self.cookie_service.start_cookie_monitoring(account_id, domain, timeout=600)
        
        QMessageBox.information(
            self, 
            "登录提示", 
            f"已打开登录页面，请在浏览器中完成登录。\n"
            f"系统将在10分钟内监控Cookie获取。\n"
            f"登录完成后请刷新账号列表查看状态。"
        )
    
    def scrape_account(self, account_id: str):
        """抓取单个账号数据"""
        # 显示日期选择对话框
        dialog = DateRangeDialog(self)
        if dialog.exec_() != QDialog.Accepted:
            return
        
        start_date, end_date = dialog.get_date_range()
        
        # 创建进度对话框
        progress = QProgressDialog("正在抓取数据...", "取消", 0, 0, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.show()
        
        # 创建工作线程
        self.scrape_worker = ScrapeWorker(
            self.data_scraper, account_id, start_date, end_date
        )
        self.scrape_worker.finished.connect(lambda result: self.on_scrape_finished(result, progress))
        self.scrape_worker.error.connect(lambda error: self.on_scrape_error(error, progress))
        self.scrape_worker.start()
    
    def batch_scrape(self):
        """批量抓取所有账号数据"""
        # 检查是否有已登录账号
        logged_in_accounts = self.account_service.get_logged_in_accounts()
        if not logged_in_accounts:
            QMessageBox.warning(self, "提示", "没有已登录的账号")
            return
        
        # 显示日期选择对话框
        dialog = DateRangeDialog(self)
        if dialog.exec_() != QDialog.Accepted:
            return
        
        start_date, end_date = dialog.get_date_range()
        
        # 创建进度对话框
        progress = QProgressDialog("正在批量抓取数据...", "取消", 0, len(logged_in_accounts), self)
        progress.setWindowModality(Qt.WindowModal)
        progress.show()
        
        # 创建工作线程
        self.batch_worker = BatchScrapeWorker(
            self.data_scraper, start_date, end_date
        )
        self.batch_worker.finished.connect(lambda results: self.on_batch_scrape_finished(results, progress))
        self.batch_worker.error.connect(lambda error: self.on_scrape_error(error, progress))
        self.batch_worker.progress.connect(lambda value, text: self.update_progress(progress, value, text))
        self.batch_worker.start()
    
    def on_scrape_finished(self, result: dict, progress: QProgressDialog):
        """单个抓取完成"""
        progress.close()
        
        if result.get("success"):
            # 选择保存路径
            default_filename = f"{result.get('platform_name', '数据')}-{result.get('account_name', '账号')}.xlsx"
            filepath, _ = QFileDialog.getSaveFileName(
                self, "保存Excel文件", default_filename, "Excel文件 (*.xlsx)"
            )
            
            if filepath:
                try:
                    # 导出Excel
                    output_dir = os.path.dirname(filepath)
                    filename = os.path.basename(filepath)
                    
                    # 临时修改文件名生成逻辑
                    original_generate_filename = self.excel_exporter._generate_filename
                    self.excel_exporter._generate_filename = lambda p, a, d: filename
                    
                    saved_path = self.excel_exporter.export_single_account_data(result, output_dir)
                    
                    # 恢复原始方法
                    self.excel_exporter._generate_filename = original_generate_filename
                    
                    QMessageBox.information(self, "成功", f"数据已导出到: {saved_path}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
        else:
            QMessageBox.critical(self, "抓取失败", result.get("error", "未知错误"))
    
    def on_batch_scrape_finished(self, results: list, progress: QProgressDialog):
        """批量抓取完成"""
        progress.close()
        
        # 选择保存目录
        output_dir = QFileDialog.getExistingDirectory(self, "选择保存目录")
        if not output_dir:
            return
        
        try:
            # 导出Excel文件
            filepaths = self.excel_exporter.export_multiple_accounts_data(
                results, output_dir, merge_files=False
            )
            
            success_count = len([r for r in results if r.get("success")])
            total_count = len(results)
            
            QMessageBox.information(
                self, 
                "批量抓取完成", 
                f"成功抓取 {success_count}/{total_count} 个账号的数据\n"
                f"文件已保存到: {output_dir}"
            )
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def on_scrape_error(self, error: str, progress: QProgressDialog):
        """抓取错误"""
        progress.close()
        QMessageBox.critical(self, "抓取错误", error)
    
    def update_progress(self, progress: QProgressDialog, value: int, text: str):
        """更新进度"""
        progress.setValue(value)
        progress.setLabelText(text)
        QApplication.processEvents()
