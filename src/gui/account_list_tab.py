#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号列表标签页
"""

import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QPushButton, QHeaderView,
                            QMessageBox, QDialog, QFileDialog, QProgressDialog,
                            QApplication, QLabel, QGroupBox, QTextEdit, QInputDialog,
                            QScrollArea, QFrame, QGridLayout, QSizePolicy)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QMetaObject, Q_ARG
from PyQt5.QtGui import QColor, QFont

from src.utils.data_manager import DataManager
from src.utils.status_manager import StatusManager
from src.services.account_service import AccountService
from src.services.platform_service import PlatformService
from src.services.cookie_service import CookieService
from src.services.data_scraper import DataScraper
from src.services.excel_exporter import ExcelExporter
from src.gui.date_range_dialog import DateRangeDialog
from src.gui.account_edit_dialog import AccountEditDialog
from src.gui.cookie_input_dialog import CookieInputDialog
from src.gui.account_detail_dialog import AccountDetailDialog



class ScrapeWorker(QThread):
    """数据抓取工作线程"""
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, scraper, account_id, start_date, end_date):
        super().__init__()
        self.scraper = scraper
        self.account_id = account_id
        self.start_date = start_date
        self.end_date = end_date
    
    def run(self):
        try:
            result = self.scraper.scrape_single_account(
                self.account_id, self.start_date, self.end_date
            )
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))


class BatchScrapeWorker(QThread):
    """批量抓取工作线程"""
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    progress = pyqtSignal(int, str)
    
    def __init__(self, scraper, start_date, end_date):
        super().__init__()
        self.scraper = scraper
        self.start_date = start_date
        self.end_date = end_date
    
    def run(self):
        try:
            # 获取已登录账号
            logged_in_accounts = self.scraper.account_service.get_logged_in_accounts()
            total = len(logged_in_accounts)
            results = []
            
            for i, account in enumerate(logged_in_accounts):
                self.progress.emit(i + 1, f"正在抓取: {account.account_name}")
                
                result = self.scraper.scrape_single_account(
                    account.account_id, self.start_date, self.end_date
                )
                results.append(result)
            
            self.finished.emit(results)
        except Exception as e:
            self.error.emit(str(e))


class AccountListTab(QWidget):
    """账号列表标签页"""
    
    def __init__(self, data_manager: DataManager):
        """初始化账号列表标签页"""
        super().__init__()

        self.data_manager = data_manager
        self.status_manager = StatusManager()
        self.account_service = AccountService(data_manager)
        self.platform_service = PlatformService(data_manager)
        self.cookie_service = CookieService(data_manager)
        self.data_scraper = DataScraper(data_manager)
        self.excel_exporter = ExcelExporter()

        self.init_ui()
        self.refresh_accounts()

        # 启动定时器更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次

        # 启动登录结果检查定时器
        self.login_check_timer = QTimer()
        self.login_check_timer.timeout.connect(self.check_login_results)
        self.login_check_timer.start(500)  # 每0.5秒检查一次登录结果

    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if hasattr(self, 'cookie_service'):
                self.cookie_service.force_close_browser()
        except:
            pass
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 顶部统计信息区域
        self.stats_layout = QHBoxLayout()

        # Agent状态
        self.agent_group = self.create_stats_group("Agent状态", "运行中", "运行中")
        self.stats_layout.addWidget(self.agent_group)

        # 账号数量
        self.account_group = self.create_stats_group("账号数量", "0", "")
        self.stats_layout.addWidget(self.account_group)

        # 采集中账号
        self.collecting_group = self.create_stats_group("采集中账号", "0", "")
        self.stats_layout.addWidget(self.collecting_group)

        # 运行时间
        self.runtime_group = self.create_stats_group("运行时间", "0分钟", "")
        self.stats_layout.addWidget(self.runtime_group)

        layout.addLayout(self.stats_layout)

        # 账号列表标题和按钮
        list_header_layout = QHBoxLayout()
        list_title = QLabel("账号列表")
        list_title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px 0;")
        list_header_layout.addWidget(list_title)

        list_header_layout.addStretch()

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        self.refresh_btn.clicked.connect(self.manual_refresh)
        list_header_layout.addWidget(self.refresh_btn)

        self.batch_scrape_btn = QPushButton("全部抓取")
        self.batch_scrape_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.batch_scrape_btn.clicked.connect(self.batch_scrape)
        list_header_layout.addWidget(self.batch_scrape_btn)

        layout.addLayout(list_header_layout)

        # 账号列表表格
        self.table = QTableWidget()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            "账号名称", "关联平台", "登录状态", "开始采集", "设置Cookie", "浏览器登录", "详情"
        ])

        # 设置表格属性
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)

        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                border: 1px solid #d9d9d9;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QHeaderView::section {
                background-color: #fafafa;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #d9d9d9;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.table)

    def create_stats_group(self, title, value, status=""):
        """创建统计信息组件"""
        from PyQt5.QtWidgets import QGroupBox, QVBoxLayout, QLabel

        group = QGroupBox()
        group.setStyleSheet("""
            QGroupBox {
                background-color: white;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                margin: 5px;
                padding: 10px;
            }
        """)

        layout = QVBoxLayout(group)

        title_label = QLabel(title)
        title_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 5px;")
        layout.addWidget(title_label)

        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #333; margin-bottom: 5px;")
        layout.addWidget(value_label)

        if status:
            status_label = QLabel(status)
            if "采集中" in status or "运行中" in status:
                status_label.setStyleSheet("""
                    background-color: #52c41a;
                    color: white;
                    padding: 2px 8px;
                    border-radius: 10px;
                    font-size: 10px;
                """)
            else:
                status_label.setStyleSheet("color: #999; font-size: 10px;")
            layout.addWidget(status_label)

        return group

    def update_status(self):
        """更新状态信息"""
        try:
            # 更新Agent状态
            agent_status = self.status_manager.get_agent_status()
            agent_detail = self.status_manager.get_agent_status_detail()
            self.update_stats_group(self.agent_group, "Agent状态", agent_status, agent_detail)

            # 更新账号数量
            account_count = len(self.data_manager.get_accounts())
            self.update_stats_group(self.account_group, "账号数量", str(account_count), "")

            # 更新采集中账号数量
            scraping_count = self.status_manager.get_scraping_count()
            self.update_stats_group(self.collecting_group, "采集中账号", str(scraping_count), "")

            # 更新运行时间
            runtime = self.status_manager.get_runtime()
            self.update_stats_group(self.runtime_group, "运行时间", runtime, "")

        except Exception as e:
            print(f"更新状态时出错: {e}")

    def update_stats_group(self, group, title, value, status):
        """更新统计信息组件"""
        try:
            layout = group.layout()
            if layout and layout.count() >= 2:
                # 更新值标签
                value_label = layout.itemAt(1).widget()
                if value_label:
                    value_label.setText(value)

                # 更新状态标签
                if status and layout.count() >= 3:
                    status_label = layout.itemAt(2).widget()
                    if status_label:
                        status_label.setText(status)
                        # 根据状态更新颜色
                        if "采集中" in status or "运行中" in status:
                            status_label.setStyleSheet("""
                                background-color: #52c41a;
                                color: white;
                                padding: 2px 8px;
                                border-radius: 10px;
                                font-size: 10px;
                            """)
                        elif "登录中" in status:
                            status_label.setStyleSheet("""
                                background-color: #fa8c16;
                                color: white;
                                padding: 2px 8px;
                                border-radius: 10px;
                                font-size: 10px;
                            """)
        except Exception as e:
            print(f"更新统计组件时出错: {e}")

    def check_login_results(self):
        """检查登录结果（在主线程中安全执行）"""
        try:
            # 获取所有正在登录的账号
            for account_id in list(self.status_manager.login_accounts):
                result = self.cookie_service.get_login_result(account_id)
                if result:
                    success, message = result

                    # 停止登录状态
                    self.status_manager.stop_login(account_id)

                    # 显示结果（在主线程中安全）
                    if success:
                        QMessageBox.information(self, "登录成功", f"账号登录成功！\n{message}")
                        # 刷新账号列表
                        self.refresh_accounts()
                    else:
                        QMessageBox.warning(self, "登录失败", f"账号登录失败！\n{message}")

                    # 确保浏览器进程被清理
                    self.cookie_service.force_close_browser()

        except Exception as e:
            print(f"检查登录结果时出错: {e}")

    def manual_refresh(self):
        """手动刷新按钮点击事件"""
        try:
            # 显示刷新状态
            self.refresh_btn.setText("刷新中...")
            self.refresh_btn.setEnabled(False)

            # 刷新账号列表
            self.refresh_accounts()

            # 恢复按钮状态
            self.refresh_btn.setText("刷新")
            self.refresh_btn.setEnabled(True)

            # 显示刷新完成提示
            QApplication.processEvents()  # 确保UI更新

        except Exception as e:
            print(f"手动刷新时出错: {e}")
            # 恢复按钮状态
            self.refresh_btn.setText("刷新")
            self.refresh_btn.setEnabled(True)

    def refresh_accounts(self):
        """刷新账号列表"""
        accounts_with_platforms = self.account_service.get_accounts_with_platforms()

        self.table.setRowCount(len(accounts_with_platforms))

        for row, (account, platform) in enumerate(accounts_with_platforms):
            # 账号名称
            name_item = QTableWidgetItem(account.account_name)
            name_item.setData(Qt.UserRole, account.account_id)  # 存储账号ID
            self.table.setItem(row, 0, name_item)

            # 关联平台
            self.table.setItem(row, 1, QTableWidgetItem(platform.platform_name))

            # 登录状态
            is_logged_in = self.account_service.is_account_logged_in(account.account_id)
            status_item = QTableWidgetItem()
            if is_logged_in:
                status_item.setText("已登录")
                status_item.setBackground(QColor(240, 255, 240))  # 浅绿色背景
            else:
                status_item.setText("未登录")
                status_item.setBackground(QColor(255, 240, 240))  # 浅红色背景
            self.table.setItem(row, 2, status_item)

            # 开始采集按钮
            scrape_btn = QPushButton("开始采集")
            scrape_btn.setEnabled(is_logged_in)
            scrape_btn.setStyleSheet(self.get_button_style("#1890ff" if is_logged_in else "#d9d9d9"))
            scrape_btn.clicked.connect(lambda checked, aid=account.account_id: self.scrape_account(aid))
            self.table.setCellWidget(row, 3, scrape_btn)

            # 设置Cookie按钮
            cookie_btn = QPushButton("设置Cookie")
            cookie_btn.setStyleSheet(self.get_button_style("#52c41a"))
            cookie_btn.clicked.connect(lambda checked, aid=account.account_id: self.set_cookie_manual(aid))
            self.table.setCellWidget(row, 4, cookie_btn)

            # 浏览器登录按钮
            login_btn = QPushButton("浏览器登录")
            login_btn.setStyleSheet(self.get_button_style("#fa8c16"))
            login_btn.clicked.connect(lambda checked, aid=account.account_id, pid=platform.platform_id:
                                    self.start_login(aid, pid))
            self.table.setCellWidget(row, 5, login_btn)

            # 详情按钮
            detail_btn = QPushButton("详情")
            detail_btn.setStyleSheet(self.get_button_style("#722ed1"))
            detail_btn.clicked.connect(lambda checked, aid=account.account_id: self.show_account_detail(aid))
            self.table.setCellWidget(row, 6, detail_btn)

    def get_button_style(self, color):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
            QPushButton:disabled {{
                background-color: #d9d9d9;
                color: #999;
            }}
        """

    def set_cookie_manual(self, account_id: str):
        """手动设置Cookie"""
        account = self.account_service.get_account_by_id(account_id)
        if not account:
            QMessageBox.warning(self, "错误", "账号信息不存在")
            return

        # 获取当前Cookie
        current_cookie = self.cookie_service.get_account_cookie(account_id) or ""

        # 显示Cookie输入对话框
        dialog = CookieInputDialog(account.account_name, current_cookie, self)
        if dialog.exec_() == QDialog.Accepted:
            cookie_str = dialog.get_cookie()
            if cookie_str.strip():
                try:
                    # 保存Cookie
                    self.cookie_service.save_account_cookie(account_id, cookie_str.strip())
                    QMessageBox.information(self, "成功", "Cookie设置成功")
                    self.refresh_accounts()
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"设置Cookie失败: {str(e)}")
            else:
                QMessageBox.warning(self, "提示", "Cookie不能为空")

    def show_account_detail(self, account_id: str):
        """显示账号详情"""
        account_platform = self.account_service.get_account_with_platform(account_id)
        if not account_platform:
            QMessageBox.warning(self, "错误", "账号信息不存在")
            return

        account, platform = account_platform
        cookie_data = self.data_manager.get_cookie(account_id)

        detail_info = f"""
账号详情

账号ID: {account.account_id}
账号名称: {account.account_name}
关联平台: {platform.platform_name}
创建时间: {account.create_time}

Cookie信息:
"""

        if cookie_data:
            detail_info += f"""
Cookie更新时间: {cookie_data.get('update_time', '未知')}
Cookie过期时间: {cookie_data.get('expire_time', '未设置')}
Cookie状态: {'有效' if cookie_data.get('cookie_str') else '无效'}
"""
        else:
            detail_info += "未设置Cookie"

        # 显示详情对话框
        dialog = AccountDetailDialog(account.account_name, detail_info, self)
        dialog.exec_()

    def edit_account(self, account_id: str):
        """编辑账号"""
        dialog = AccountEditDialog(self.data_manager, account_id, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_accounts()
    
    def start_login(self, account_id: str, platform_id: str):
        """开始登录"""
        platform = self.platform_service.get_platform_by_id(platform_id)
        if not platform:
            QMessageBox.warning(self, "错误", "平台信息不存在")
            return

        # 标记开始登录
        self.status_manager.start_login(account_id)

        # 使用selenium打开无痕浏览器（不使用回调）
        self.cookie_service.open_incognito_browser(
            platform.login_url,
            account_id,
            callback=None  # 不使用回调，通过轮询检查结果
        )

        QMessageBox.information(
            self,
            "登录提示",
            f"已打开无痕浏览器，请在浏览器中完成登录。\n"
            f"系统将自动监控登录状态并获取Cookie。\n"
            f"登录完成后会自动提示。"
        )
    
    def scrape_account(self, account_id: str):
        """抓取单个账号数据"""
        # 显示日期选择对话框
        dialog = DateRangeDialog(self)
        if dialog.exec_() != QDialog.Accepted:
            return

        start_date, end_date = dialog.get_date_range()

        # 标记开始抓取
        self.status_manager.start_scraping(account_id)

        # 创建进度对话框
        progress = QProgressDialog("正在抓取数据...", "取消", 0, 0, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.show()

        # 创建工作线程
        self.scrape_worker = ScrapeWorker(
            self.data_scraper, account_id, start_date, end_date
        )
        self.scrape_worker.finished.connect(lambda result: self.on_scrape_finished(result, progress, account_id))
        self.scrape_worker.error.connect(lambda error: self.on_scrape_error(error, progress, account_id))
        self.scrape_worker.start()
    
    def batch_scrape(self):
        """批量抓取所有账号数据"""
        # 检查是否有已登录账号
        logged_in_accounts = self.account_service.get_logged_in_accounts()
        if not logged_in_accounts:
            QMessageBox.warning(self, "提示", "没有已登录的账号")
            return
        
        # 显示日期选择对话框
        dialog = DateRangeDialog(self)
        if dialog.exec_() != QDialog.Accepted:
            return
        
        start_date, end_date = dialog.get_date_range()
        
        # 创建进度对话框
        progress = QProgressDialog("正在批量抓取数据...", "取消", 0, len(logged_in_accounts), self)
        progress.setWindowModality(Qt.WindowModal)
        progress.show()
        
        # 创建工作线程
        self.batch_worker = BatchScrapeWorker(
            self.data_scraper, start_date, end_date
        )
        self.batch_worker.finished.connect(lambda results: self.on_batch_scrape_finished(results, progress))
        self.batch_worker.error.connect(lambda error: self.on_scrape_error(error, progress))
        self.batch_worker.progress.connect(lambda value, text: self.update_progress(progress, value, text))
        self.batch_worker.start()
    
    def on_scrape_finished(self, result: dict, progress: QProgressDialog, account_id: str = None):
        """单个抓取完成"""
        progress.close()

        # 停止抓取状态
        if account_id:
            self.status_manager.stop_scraping(account_id)

        if result.get("success"):
            # 选择保存路径
            default_filename = f"{result.get('platform_name', '数据')}-{result.get('account_name', '账号')}.xlsx"
            filepath, _ = QFileDialog.getSaveFileName(
                self, "保存Excel文件", default_filename, "Excel文件 (*.xlsx)"
            )
            
            if filepath:
                try:
                    # 导出Excel
                    output_dir = os.path.dirname(filepath)
                    filename = os.path.basename(filepath)
                    
                    # 临时修改文件名生成逻辑
                    original_generate_filename = self.excel_exporter._generate_filename
                    self.excel_exporter._generate_filename = lambda p, a, d: filename
                    
                    saved_path = self.excel_exporter.export_single_account_data(result, output_dir)
                    
                    # 恢复原始方法
                    self.excel_exporter._generate_filename = original_generate_filename
                    
                    QMessageBox.information(self, "成功", f"数据已导出到: {saved_path}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
        else:
            QMessageBox.critical(self, "抓取失败", result.get("error", "未知错误"))
    
    def on_batch_scrape_finished(self, results: list, progress: QProgressDialog):
        """批量抓取完成"""
        progress.close()
        
        # 选择保存目录
        output_dir = QFileDialog.getExistingDirectory(self, "选择保存目录")
        if not output_dir:
            return
        
        try:
            # 导出Excel文件
            filepaths = self.excel_exporter.export_multiple_accounts_data(
                results, output_dir, merge_files=False
            )
            
            success_count = len([r for r in results if r.get("success")])
            total_count = len(results)
            
            QMessageBox.information(
                self, 
                "批量抓取完成", 
                f"成功抓取 {success_count}/{total_count} 个账号的数据\n"
                f"文件已保存到: {output_dir}"
            )
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def on_scrape_error(self, error: str, progress: QProgressDialog, account_id: str = None):
        """抓取错误"""
        progress.close()

        # 停止抓取状态
        if account_id:
            self.status_manager.stop_scraping(account_id)

        QMessageBox.critical(self, "抓取错误", error)
    
    def update_progress(self, progress: QProgressDialog, value: int, text: str):
        """更新进度"""
        progress.setValue(value)
        progress.setLabelText(text)
        QApplication.processEvents()
