#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台配置标签页
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QPushButton, QHeaderView,
                            QMessageBox, QDialog)
from PyQt5.QtCore import pyqtSignal

from src.utils.data_manager import DataManager
from src.services.platform_service import PlatformService
from src.gui.platform_edit_dialog import PlatformEditDialog


class PlatformTab(QWidget):
    """平台配置标签页"""
    
    platform_changed = pyqtSignal()  # 平台配置变更信号
    
    def __init__(self, data_manager: DataManager):
        """初始化平台配置标签页"""
        super().__init__()
        
        self.data_manager = data_manager
        self.platform_service = PlatformService(data_manager)
        
        self.init_ui()
        self.refresh_platforms()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 顶部按钮区域
        button_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("新增平台")
        self.add_btn.clicked.connect(self.add_platform)
        button_layout.addWidget(self.add_btn)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 平台列表表格
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels([
            "平台名称", "登录URL", "数据接口URL", "编辑", "删除"
        ])
        
        # 设置表格属性
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.table)
    
    def refresh_platforms(self):
        """刷新平台列表"""
        platforms = self.platform_service.get_all_platforms()
        
        self.table.setRowCount(len(platforms))
        
        for row, platform in enumerate(platforms):
            # 平台名称
            self.table.setItem(row, 0, QTableWidgetItem(platform.platform_name))
            
            # 登录URL
            self.table.setItem(row, 1, QTableWidgetItem(platform.login_url))
            
            # 数据接口URL
            self.table.setItem(row, 2, QTableWidgetItem(platform.data_api_url))
            
            # 编辑按钮
            edit_btn = QPushButton("编辑")
            edit_btn.clicked.connect(lambda checked, pid=platform.platform_id: self.edit_platform(pid))
            self.table.setCellWidget(row, 3, edit_btn)
            
            # 删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.clicked.connect(lambda checked, pid=platform.platform_id: self.delete_platform(pid))
            self.table.setCellWidget(row, 4, delete_btn)
    
    def add_platform(self):
        """添加平台"""
        dialog = PlatformEditDialog(self.data_manager, None, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_platforms()
            self.platform_changed.emit()
    
    def edit_platform(self, platform_id: str):
        """编辑平台"""
        dialog = PlatformEditDialog(self.data_manager, platform_id, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_platforms()
            self.platform_changed.emit()
    
    def delete_platform(self, platform_id: str):
        """删除平台"""
        platform = self.platform_service.get_platform_by_id(platform_id)
        if not platform:
            return
        
        reply = QMessageBox.question(
            self,
            '确认删除',
            f'确定要删除平台 "{platform.platform_name}" 吗？\n'
            f'删除后相关账号将无法使用。',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.platform_service.delete_platform(platform_id)
                self.refresh_platforms()
                self.platform_changed.emit()
                QMessageBox.information(self, "成功", "平台删除成功")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除平台失败: {str(e)}")
