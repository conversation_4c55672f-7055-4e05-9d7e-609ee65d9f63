#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号编辑对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QComboBox, QPushButton, QMessageBox,
                            QGroupBox)

from src.utils.data_manager import DataManager
from src.services.account_service import AccountService
from src.services.platform_service import PlatformService


class AccountEditDialog(QDialog):
    """账号编辑对话框"""
    
    def __init__(self, data_manager: DataManager, account_id: str = None, parent=None):
        """
        初始化账号编辑对话框
        
        Args:
            data_manager: 数据管理器
            account_id: 账号ID，None表示新增
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.data_manager = data_manager
        self.account_service = AccountService(data_manager)
        self.platform_service = PlatformService(data_manager)
        self.account_id = account_id
        self.is_edit_mode = account_id is not None
        
        self.init_ui()
        self.load_platforms()
        
        if self.is_edit_mode:
            self.load_account_data()
    
    def init_ui(self):
        """初始化用户界面"""
        title = "编辑账号" if self.is_edit_mode else "添加账号"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(400, 200)
        
        layout = QVBoxLayout(self)
        
        # 表单组
        form_group = QGroupBox("账号信息")
        form_layout = QFormLayout(form_group)
        
        # 账号名称
        self.account_name_edit = QLineEdit()
        self.account_name_edit.setPlaceholderText("请输入账号名称")
        form_layout.addRow("账号名称*:", self.account_name_edit)
        
        # 关联平台
        self.platform_combo = QComboBox()
        form_layout.addRow("关联平台*:", self.platform_combo)
        
        layout.addWidget(form_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_account)
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        # 如果是编辑模式，添加删除按钮
        if self.is_edit_mode:
            self.delete_btn = QPushButton("删除")
            self.delete_btn.clicked.connect(self.delete_account)
            button_layout.addWidget(self.delete_btn)
        
        layout.addLayout(button_layout)
    
    def load_platforms(self):
        """加载平台列表"""
        self.platform_combo.clear()
        
        platforms = self.platform_service.get_platform_names()
        
        if platforms:
            for platform_id, platform_name in platforms:
                self.platform_combo.addItem(platform_name, platform_id)
    
    def load_account_data(self):
        """加载账号数据（编辑模式）"""
        if not self.account_id:
            return
        
        account = self.account_service.get_account_by_id(self.account_id)
        if not account:
            QMessageBox.warning(self, "错误", "账号信息不存在")
            self.reject()
            return
        
        # 填充表单
        self.account_name_edit.setText(account.account_name)
        
        # 选择对应的平台
        for i in range(self.platform_combo.count()):
            if self.platform_combo.itemData(i) == account.platform_id:
                self.platform_combo.setCurrentIndex(i)
                break
    
    def save_account(self):
        """保存账号"""
        # 验证输入
        account_name = self.account_name_edit.text().strip()
        if not account_name:
            QMessageBox.warning(self, "输入错误", "请输入账号名称")
            return
        
        platform_id = self.platform_combo.currentData()
        if not platform_id:
            QMessageBox.warning(self, "输入错误", "请选择关联平台")
            return
        
        try:
            if self.is_edit_mode:
                # 更新账号
                self.account_service.update_account(self.account_id, account_name, platform_id)
                QMessageBox.information(self, "成功", "账号更新成功")
            else:
                # 添加账号
                self.account_service.add_account(account_name, platform_id)
                QMessageBox.information(self, "成功", "账号添加成功")
            
            self.accept()
            
        except Exception as e:
            operation = "更新" if self.is_edit_mode else "添加"
            QMessageBox.critical(self, "错误", f"{operation}账号失败: {str(e)}")
    
    def delete_account(self):
        """删除账号"""
        if not self.is_edit_mode or not self.account_id:
            return
        
        account = self.account_service.get_account_by_id(self.account_id)
        if not account:
            return
        
        reply = QMessageBox.question(
            self,
            '确认删除',
            f'确定要删除账号 "{account.account_name}" 吗？\n'
            f'删除后相关的Cookie也会被清除。',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.account_service.delete_account(self.account_id)
                QMessageBox.information(self, "成功", "账号删除成功")
                self.accept()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除账号失败: {str(e)}")
