#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口
"""

from PyQt5.QtWidgets import (QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout,
                            QWidget, QStatusBar, QMenuBar, QAction,
                            QMessageBox, QApplication, QLabel)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

from src.utils.data_manager import DataManager
from src.gui.account_tab import AccountTab
from src.gui.platform_tab import PlatformTab
from src.gui.account_list_tab import AccountListTab


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 初始化数据管理器
        self.data_manager = DataManager()
        
        self.init_ui()
        self.init_menu()
        self.init_status_bar()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("运营数据采集器")
        self.setGeometry(100, 100, 1400, 900)

        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f2f5;
            }
            QTabWidget::pane {
                border: none;
                background-color: #f0f2f5;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #ffffff;
                border: 1px solid #d9d9d9;
                border-radius:3px;
                border-bottom: none;
                padding: 12px 20px;
                margin-right: 2px;
                font-size: 14px;
                font-weight: bold;
                margin-top: 5px;
            }
            QTabBar::tab:selected {
                background-color: #1890ff;
                border: 1px solid #d9d9d9;       
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e6f7ff;
                color: #1890ff;
            }
        """)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 创建顶部标题栏
        header_widget = self.create_header()
        layout.addWidget(header_widget)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 创建各个标签页
        self.account_list_tab = AccountListTab(self.data_manager)
        self.account_tab = AccountTab(self.data_manager)
        self.platform_tab = PlatformTab(self.data_manager)

        # 添加标签页
        self.tab_widget.addTab(self.account_list_tab, "仪表盘")
        self.tab_widget.addTab(self.account_tab, "添加账号")
        self.tab_widget.addTab(self.platform_tab, "平台配置")

        # 连接信号
        self.account_tab.account_added.connect(self.account_list_tab.refresh_accounts)
        self.platform_tab.platform_changed.connect(self.account_tab.refresh_platforms)
        self.platform_tab.platform_changed.connect(self.account_list_tab.refresh_accounts)

        # 设置默认显示账号列表页
        self.tab_widget.setCurrentIndex(0)

    def create_header(self):
        """创建顶部标题栏"""
        header_widget = QWidget()
        header_widget.setFixedHeight(60)
        header_widget.setStyleSheet("""
            QWidget {
                background: #1890ff;
                border: none;
            }
        """)

        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 0, 20, 0)

        # 标题
        title_label = QLabel("运营数据采集器")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
                background: transparent;
            }
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 状态标签
        status_label = QLabel("运行中")
        status_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                background: rgba(255, 255, 255, 0.2);
                padding: 6px 12px;
                border-radius: 15px;
            }
        """)
        header_layout.addWidget(status_label)

        return header_widget

    def init_menu(self):
        """初始化菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        # 退出动作
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        # 关于动作
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def init_status_bar(self):
        """初始化状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            "多平台运营数据抓取系统\n\n"
            "版本: 1.0.0\n"
            "支持多平台数据抓取和Excel导出\n\n"
            "开发者: AI Assistant"
        )
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self,
            '确认退出',
            '确定要退出程序吗？',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
