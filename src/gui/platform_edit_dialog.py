#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台编辑对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QTextEdit, QPushButton, QMessageBox,
                            QGroupBox, QLabel)

from src.utils.data_manager import DataManager
from src.services.platform_service import PlatformService


class PlatformEditDialog(QDialog):
    """平台编辑对话框"""
    
    def __init__(self, data_manager: DataManager, platform_id: str = None, parent=None):
        """
        初始化平台编辑对话框
        
        Args:
            data_manager: 数据管理器
            platform_id: 平台ID，None表示新增
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.data_manager = data_manager
        self.platform_service = PlatformService(data_manager)
        self.platform_id = platform_id
        self.is_edit_mode = platform_id is not None
        
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_platform_data()
    
    def init_ui(self):
        """初始化用户界面"""
        title = "编辑平台" if self.is_edit_mode else "添加平台"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 表单组
        form_group = QGroupBox("平台信息")
        form_layout = QFormLayout(form_group)
        
        # 平台名称
        self.platform_name_edit = QLineEdit()
        self.platform_name_edit.setPlaceholderText("如：小红书千帆")
        form_layout.addRow("平台名称*:", self.platform_name_edit)
        
        # 登录URL
        self.login_url_edit = QLineEdit()
        self.login_url_edit.setPlaceholderText("如：https://ark.xiaohongshu.com/login")
        form_layout.addRow("登录URL*:", self.login_url_edit)
        
        # 数据接口URL
        self.data_api_url_edit = QLineEdit()
        self.data_api_url_edit.setPlaceholderText("如：https://ark.xiaohongshu.com/api/edith/butterfly/data")
        form_layout.addRow("数据接口URL*:", self.data_api_url_edit)
        
        # 数据提取规则
        self.extract_rule_edit = QTextEdit()
        self.extract_rule_edit.setPlaceholderText("JSONPath表达式，如：$.data[*].data[*]")
        self.extract_rule_edit.setMaximumHeight(80)
        form_layout.addRow("数据提取规则*:", self.extract_rule_edit)
        
        # 添加说明标签
        help_label = QLabel(
            "提示：\n"
            "• 登录URL：用户手动登录的页面地址\n"
            "• 数据接口URL：抓取数据的API地址\n"
            "• 数据提取规则：使用JSONPath语法从API返回数据中提取目标字段"
        )
        help_label.setStyleSheet("color: gray; font-size: 10px;")
        form_layout.addRow(help_label)
        
        layout.addWidget(form_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_platform)
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def load_platform_data(self):
        """加载平台数据（编辑模式）"""
        if not self.platform_id:
            return
        
        platform = self.platform_service.get_platform_by_id(self.platform_id)
        if not platform:
            QMessageBox.warning(self, "错误", "平台信息不存在")
            self.reject()
            return
        
        # 填充表单
        self.platform_name_edit.setText(platform.platform_name)
        self.login_url_edit.setText(platform.login_url)
        self.data_api_url_edit.setText(platform.data_api_url)
        self.extract_rule_edit.setPlainText(platform.extract_rule)
    
    def save_platform(self):
        """保存平台"""
        # 验证输入
        platform_name = self.platform_name_edit.text().strip()
        if not platform_name:
            QMessageBox.warning(self, "输入错误", "请输入平台名称")
            return
        
        login_url = self.login_url_edit.text().strip()
        if not login_url:
            QMessageBox.warning(self, "输入错误", "请输入登录URL")
            return
        
        data_api_url = self.data_api_url_edit.text().strip()
        if not data_api_url:
            QMessageBox.warning(self, "输入错误", "请输入数据接口URL")
            return
        
        extract_rule = self.extract_rule_edit.toPlainText().strip()
        if not extract_rule:
            QMessageBox.warning(self, "输入错误", "请输入数据提取规则")
            return
        
        # 简单验证URL格式
        if not (login_url.startswith('http://') or login_url.startswith('https://')):
            QMessageBox.warning(self, "输入错误", "登录URL格式不正确，请以http://或https://开头")
            return
        
        if not (data_api_url.startswith('http://') or data_api_url.startswith('https://')):
            QMessageBox.warning(self, "输入错误", "数据接口URL格式不正确，请以http://或https://开头")
            return
        
        try:
            if self.is_edit_mode:
                # 更新平台
                self.platform_service.update_platform(
                    self.platform_id, platform_name, login_url, data_api_url, extract_rule
                )
                QMessageBox.information(self, "成功", "平台更新成功")
            else:
                # 添加平台
                self.platform_service.add_platform(
                    platform_name, login_url, data_api_url, extract_rule
                )
                QMessageBox.information(self, "成功", "平台添加成功")
            
            self.accept()
            
        except Exception as e:
            operation = "更新" if self.is_edit_mode else "添加"
            QMessageBox.critical(self, "错误", f"{operation}平台失败: {str(e)}")
