#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日期范围选择对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QDateEdit, QPushButton, QLabel, QGroupBox)
from PyQt5.QtCore import QDate, Qt
from datetime import datetime, timedelta


class DateRangeDialog(QDialog):
    """日期范围选择对话框"""
    
    def __init__(self, parent=None):
        """初始化日期范围选择对话框"""
        super().__init__(parent)
        
        self.init_ui()
        self.set_default_dates()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("选择时间范围")
        self.setModal(True)
        self.resize(300, 150)
        
        layout = QVBoxLayout(self)
        
        # 日期选择组
        date_group = QGroupBox("时间范围")
        form_layout = QFormLayout(date_group)
        
        # 开始日期
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDisplayFormat("yyyy-MM-dd")
        form_layout.addRow("开始日期:", self.start_date_edit)
        
        # 结束日期
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDisplayFormat("yyyy-MM-dd")
        form_layout.addRow("结束日期:", self.end_date_edit)
        
        layout.addWidget(date_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def set_default_dates(self):
        """设置默认日期（最近7天）"""
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)
        
        self.start_date_edit.setDate(QDate.fromString(week_ago.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        self.end_date_edit.setDate(QDate.fromString(today.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
    
    def get_date_range(self):
        """
        获取选择的日期范围
        
        Returns:
            (start_date, end_date) 格式为 "YYYY-MM-DD"
        """
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
        
        return start_date, end_date
    
    def accept(self):
        """确认按钮点击事件"""
        start_date = self.start_date_edit.date()
        end_date = self.end_date_edit.date()
        
        if start_date > end_date:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "日期错误", "开始日期不能晚于结束日期")
            return
        
        super().accept()
