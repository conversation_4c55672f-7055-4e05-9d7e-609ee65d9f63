#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加账号标签页
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QComboBox, QPushButton, QMessageBox,
                            QGroupBox, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import pyqtSignal

from src.utils.data_manager import DataManager
from src.services.account_service import AccountService
from src.services.platform_service import PlatformService


class AccountTab(QWidget):
    """添加账号标签页"""
    
    account_added = pyqtSignal()  # 账号添加成功信号
    
    def __init__(self, data_manager: DataManager):
        """初始化添加账号标签页"""
        super().__init__()
        
        self.data_manager = data_manager
        self.account_service = AccountService(data_manager)
        self.platform_service = PlatformService(data_manager)
        
        self.init_ui()
        self.refresh_platforms()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建表单组
        form_group = QGroupBox("添加新账号")
        form_layout = QFormLayout(form_group)
        
        # 账号名称输入框
        self.account_name_edit = QLineEdit()
        self.account_name_edit.setPlaceholderText("请输入账号名称，如：千帆-运营账号1")
        form_layout.addRow("账号名称*:", self.account_name_edit)
        
        # 平台选择下拉框
        self.platform_combo = QComboBox()
        form_layout.addRow("关联平台*:", self.platform_combo)
        
        layout.addWidget(form_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 添加弹性空间
        button_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        
        # 保存按钮
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_account)
        button_layout.addWidget(self.save_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.clear_form)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        # 添加垂直弹性空间
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
    
    def refresh_platforms(self):
        """刷新平台列表"""
        self.platform_combo.clear()
        
        platforms = self.platform_service.get_platform_names()
        
        if not platforms:
            self.platform_combo.addItem("请先配置平台", "")
            self.save_btn.setEnabled(False)
        else:
            self.platform_combo.addItem("请选择平台", "")
            for platform_id, platform_name in platforms:
                self.platform_combo.addItem(platform_name, platform_id)
            self.save_btn.setEnabled(True)
    
    def save_account(self):
        """保存账号"""
        # 验证输入
        account_name = self.account_name_edit.text().strip()
        if not account_name:
            QMessageBox.warning(self, "输入错误", "请输入账号名称")
            return
        
        platform_id = self.platform_combo.currentData()
        if not platform_id:
            QMessageBox.warning(self, "输入错误", "请选择关联平台")
            return
        
        try:
            # 添加账号
            account_id = self.account_service.add_account(account_name, platform_id)
            
            QMessageBox.information(self, "成功", f"账号 '{account_name}' 添加成功")
            
            # 清空表单
            self.clear_form()
            
            # 发送信号
            self.account_added.emit()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加账号失败: {str(e)}")
    
    def clear_form(self):
        """清空表单"""
        self.account_name_edit.clear()
        self.platform_combo.setCurrentIndex(0)
