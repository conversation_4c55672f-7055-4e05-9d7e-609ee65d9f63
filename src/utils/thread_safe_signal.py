#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程安全信号处理器
用于在不同线程间安全地传递信号和回调
"""

import threading
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication


class ThreadSafeSignal(QObject):
    """线程安全的信号类"""
    
    # 定义信号
    callback_signal = pyqtSignal(bool, str, object)  # success, message, callback
    
    def __init__(self):
        super().__init__()
        self.callback_signal.connect(self._execute_callback)
        
        # 确保在主线程中创建
        app = QApplication.instance()
        if app:
            self.moveToThread(app.thread())
    
    def _execute_callback(self, success, message, callback):
        """在主线程中执行回调"""
        try:
            if callback and callable(callback):
                callback(success, message)
        except Exception as e:
            print(f"主线程回调执行失败: {e}")
    
    def emit_safe_callback(self, success, message, callback):
        """安全地发射回调信号"""
        try:
            current_thread = threading.current_thread()
            main_thread = threading.main_thread()
            
            if current_thread == main_thread:
                # 在主线程中直接执行
                self._execute_callback(success, message, callback)
            else:
                # 在非主线程中通过信号发射
                self.callback_signal.emit(success, message, callback)
                
        except Exception as e:
            print(f"安全回调发射失败: {e}")


class ThreadSafeCallbackManager:
    """线程安全回调管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        self._initialized = True
        self.signal_handler = None
        self._init_signal_handler()
    
    def _init_signal_handler(self):
        """初始化信号处理器"""
        try:
            app = QApplication.instance()
            if app:
                self.signal_handler = ThreadSafeSignal()
            else:
                print("警告: 未找到QApplication实例，线程安全回调可能不可用")
        except Exception as e:
            print(f"初始化线程安全信号处理器失败: {e}")
    
    def execute_callback_safely(self, callback, success, message):
        """安全地执行回调函数"""
        try:
            if self.signal_handler:
                self.signal_handler.emit_safe_callback(success, message, callback)
            else:
                # 降级处理：直接执行
                self._fallback_callback(callback, success, message)
                
        except Exception as e:
            print(f"安全执行回调失败: {e}")
            self._fallback_callback(callback, success, message)
    
    def _fallback_callback(self, callback, success, message):
        """降级回调处理"""
        try:
            current_thread = threading.current_thread()
            main_thread = threading.main_thread()
            
            if current_thread == main_thread:
                # 在主线程中直接执行
                callback(success, message)
            else:
                # 在非主线程中使用QTimer延迟执行
                def delayed_execution():
                    try:
                        callback(success, message)
                    except Exception as e:
                        print(f"延迟回调执行失败: {e}")
                
                QTimer.singleShot(0, delayed_execution)
                
        except Exception as e:
            print(f"降级回调处理失败: {e}")
            # 最后的尝试：在新线程中执行
            try:
                def thread_callback():
                    try:
                        callback(success, message)
                    except Exception as e:
                        print(f"线程回调执行失败: {e}")
                
                callback_thread = threading.Thread(target=thread_callback)
                callback_thread.daemon = True
                callback_thread.start()
                
            except Exception as e2:
                print(f"最终回调处理失败: {e2}")


def create_thread_safe_callback(original_callback):
    """创建线程安全的回调函数包装器"""
    import queue
    import threading

    def safe_callback(success, message):
        try:
            current_thread = threading.current_thread()
            main_thread = threading.main_thread()

            if current_thread == main_thread:
                # 在主线程中直接执行
                original_callback(success, message)
            else:
                # 在非主线程中，将回调存储到队列中，稍后在主线程中处理
                print(f"非主线程回调: success={success}, message={message}")

                # 简单的降级处理：延迟执行
                def delayed_callback():
                    try:
                        original_callback(success, message)
                    except Exception as e:
                        print(f"延迟回调执行失败: {e}")

                # 使用线程安全的方式延迟执行
                import time
                def background_callback():
                    time.sleep(0.1)  # 短暂延迟
                    try:
                        original_callback(success, message)
                    except Exception as e:
                        print(f"后台回调执行失败: {e}")

                callback_thread = threading.Thread(target=background_callback)
                callback_thread.daemon = True
                callback_thread.start()

        except Exception as e:
            print(f"线程安全回调包装器失败: {e}")
            # 最后的尝试：直接执行
            try:
                original_callback(success, message)
            except Exception as e2:
                print(f"直接回调执行也失败: {e2}")

    return safe_callback


def ensure_main_thread_execution(func):
    """装饰器：确保函数在主线程中执行"""
    import threading

    def wrapper(*args, **kwargs):
        current_thread = threading.current_thread()
        main_thread = threading.main_thread()

        if current_thread == main_thread:
            # 在主线程中直接执行
            return func(*args, **kwargs)
        else:
            # 在非主线程中，记录警告并尝试执行
            print(f"警告: 函数 {func.__name__} 在非主线程中执行")

            try:
                # 尝试在当前线程中执行（可能不安全，但避免崩溃）
                return func(*args, **kwargs)
            except Exception as e:
                print(f"非主线程执行失败: {e}")
                # 如果失败，延迟执行
                def delayed_execution():
                    try:
                        func(*args, **kwargs)
                    except Exception as e2:
                        print(f"延迟执行也失败: {e2}")

                # 在新线程中延迟执行
                delay_thread = threading.Thread(target=delayed_execution)
                delay_thread.daemon = True
                delay_thread.start()

                return None

    return wrapper
