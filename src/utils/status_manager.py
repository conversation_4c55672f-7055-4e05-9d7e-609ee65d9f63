#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
状态管理器
管理应用程序的运行状态
"""

import threading
from typing import Set, Dict
from datetime import datetime


class StatusManager:
    """状态管理器单例类"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        self._initialized = True
        self.scraping_accounts: Set[str] = set()  # 正在抓取的账号ID
        self.login_accounts: Set[str] = set()     # 正在登录的账号ID
        self.start_time = datetime.now()          # 应用启动时间
        self._lock = threading.Lock()
    
    def start_scraping(self, account_id: str):
        """开始抓取"""
        with self._lock:
            self.scraping_accounts.add(account_id)
    
    def stop_scraping(self, account_id: str):
        """停止抓取"""
        with self._lock:
            self.scraping_accounts.discard(account_id)
    
    def start_login(self, account_id: str):
        """开始登录"""
        with self._lock:
            self.login_accounts.add(account_id)
    
    def stop_login(self, account_id: str):
        """停止登录"""
        with self._lock:
            self.login_accounts.discard(account_id)
    
    def is_scraping(self, account_id: str = None) -> bool:
        """检查是否正在抓取"""
        with self._lock:
            if account_id:
                return account_id in self.scraping_accounts
            return len(self.scraping_accounts) > 0
    
    def is_login(self, account_id: str = None) -> bool:
        """检查是否正在登录"""
        with self._lock:
            if account_id:
                return account_id in self.login_accounts
            return len(self.login_accounts) > 0
    
    def get_scraping_count(self) -> int:
        """获取正在抓取的账号数量"""
        with self._lock:
            return len(self.scraping_accounts)
    
    def get_login_count(self) -> int:
        """获取正在登录的账号数量"""
        with self._lock:
            return len(self.login_accounts)
    
    def get_agent_status(self) -> str:
        """获取Agent状态"""
        with self._lock:
            if len(self.scraping_accounts) > 0:
                return "采集中"
            elif len(self.login_accounts) > 0:
                return "登录中"
            else:
                return "运行中"
    
    def get_agent_status_detail(self) -> str:
        """获取Agent状态详情"""
        with self._lock:
            if len(self.scraping_accounts) > 0:
                return f"采集中 ({len(self.scraping_accounts)}个账号)"
            elif len(self.login_accounts) > 0:
                return f"登录中 ({len(self.login_accounts)}个账号)"
            else:
                return "运行中"
    
    def get_runtime(self) -> str:
        """获取运行时间"""
        runtime = datetime.now() - self.start_time
        days = runtime.days
        hours, remainder = divmod(runtime.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        
        if days > 0:
            return f"{days}天{hours}小时{minutes}分钟"
        elif hours > 0:
            return f"{hours}小时{minutes}分钟"
        else:
            return f"{minutes}分钟"
    
    def reset(self):
        """重置状态"""
        with self._lock:
            self.scraping_accounts.clear()
            self.login_accounts.clear()
            self.start_time = datetime.now()
