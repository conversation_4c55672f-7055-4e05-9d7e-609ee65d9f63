#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理器
负责本地数据文件的读写操作
"""

import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any


class DataManager:
    """数据管理器类"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化数据管理器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        self.accounts_file = os.path.join(data_dir, "accounts.json")
        self.platforms_file = os.path.join(data_dir, "platforms.json")
        self.cookies_file = os.path.join(data_dir, "cookies.json")
        
    def init_data_files(self):
        """初始化数据文件"""
        # 创建数据目录
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化空的JSON文件
        if not os.path.exists(self.accounts_file):
            self._save_json(self.accounts_file, {})
            
        if not os.path.exists(self.platforms_file):
            # 预配置小红书千帆平台
            default_platforms = {
                "xiaohongshu_qianfan": {
                    "platform_name": "小红书千帆",
                    "login_url": "https://ark.xiaohongshu.com/login",
                    "data_api_url": "https://ark.xiaohongshu.com/api/edith/butterfly/data",
                    "extract_rule": "$.data[*].data[*]"
                }
            }
            self._save_json(self.platforms_file, default_platforms)
            
        if not os.path.exists(self.cookies_file):
            self._save_json(self.cookies_file, {})
    
    def _load_json(self, file_path: str) -> Dict:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def _save_json(self, file_path: str, data: Dict):
        """保存JSON文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    # 平台管理方法
    def get_platforms(self) -> Dict:
        """获取所有平台配置"""
        return self._load_json(self.platforms_file)

    def save_platforms(self, platforms_data: Dict):
        """保存平台配置数据"""
        self._save_json(self.platforms_file, platforms_data)
    
    def add_platform(self, platform_name: str, login_url: str,
                    data_api_url: str, shop_name_api_url: str, shop_name_field: str) -> str:
        """
        添加平台配置
        
        Returns:
            平台ID
        """
        platforms = self.get_platforms()
        platform_id = str(uuid.uuid4())
        
        platforms[platform_id] = {
            "platform_name": platform_name,
            "login_url": login_url,
            "data_api_url": data_api_url,
            "shop_name_api_url": shop_name_api_url,
            "shop_name_field": shop_name_field
        }
        
        self._save_json(self.platforms_file, platforms)
        return platform_id
    
    def update_platform(self, platform_id: str, platform_name: str,
                        login_url: str, data_api_url: str, shop_name_api_url: str, shop_name_field: str):
        """更新平台配置"""
        platforms = self.get_platforms()
        if platform_id in platforms:
            platforms[platform_id].update({
                "platform_name": platform_name,
                "login_url": login_url,
                "data_api_url": data_api_url,
                "shop_name_api_url": shop_name_api_url,
                "shop_name_field": shop_name_field
            })
            self._save_json(self.platforms_file, platforms)
    
    def delete_platform(self, platform_id: str):
        """删除平台配置"""
        platforms = self.get_platforms()
        if platform_id in platforms:
            del platforms[platform_id]
            self._save_json(self.platforms_file, platforms)
    
    # 账号管理方法
    def get_accounts(self) -> Dict:
        """获取所有账号"""
        return self._load_json(self.accounts_file)

    def save_accounts(self, accounts_data: Dict):
        """保存账号数据"""
        self._save_json(self.accounts_file, accounts_data)
    
    def add_account(self, account_name: str, platform_id: str) -> str:
        """
        添加账号
        
        Returns:
            账号ID
        """
        accounts = self.get_accounts()
        account_id = str(uuid.uuid4())
        
        accounts[account_id] = {
            "account_name": account_name,
            "platform_id": platform_id,
            "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self._save_json(self.accounts_file, accounts)
        return account_id
    
    def update_account(self, account_id: str, account_name: str, platform_id: str):
        """更新账号信息"""
        accounts = self.get_accounts()
        if account_id in accounts:
            accounts[account_id].update({
                "account_name": account_name,
                "platform_id": platform_id
            })
            self._save_json(self.accounts_file, accounts)
    
    def delete_account(self, account_id: str):
        """删除账号"""
        accounts = self.get_accounts()
        if account_id in accounts:
            del accounts[account_id]
            self._save_json(self.accounts_file, accounts)
            
        # 同时删除相关的Cookie
        self.delete_cookie(account_id)
    
    # Cookie管理方法
    def get_cookies(self) -> Dict:
        """获取所有Cookie"""
        return self._load_json(self.cookies_file)
    
    def get_cookie(self, account_id: str) -> Optional[Dict]:
        """获取指定账号的Cookie"""
        cookies = self.get_cookies()
        return cookies.get(account_id)
    
    def save_cookie(self, account_id: str, cookie_str: str, expire_time: str = None):
        """保存Cookie"""
        cookies = self.get_cookies()
        
        cookies[account_id] = {
            "cookie_str": cookie_str,
            "expire_time": expire_time,
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self._save_json(self.cookies_file, cookies)
    
    def delete_cookie(self, account_id: str):
        """删除Cookie"""
        cookies = self.get_cookies()
        if account_id in cookies:
            del cookies[account_id]
            self._save_json(self.cookies_file, cookies)
    
    def is_account_logged_in(self, account_id: str) -> bool:
        """检查账号是否已登录（有有效Cookie）"""
        cookie_data = self.get_cookie(account_id)
        if not cookie_data:
            return False
            
        # 简单检查Cookie是否存在，实际应用中可以验证Cookie有效性
        return bool(cookie_data.get("cookie_str"))
