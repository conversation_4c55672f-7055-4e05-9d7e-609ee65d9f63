#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理管理器
统一处理代理配置和错误处理
"""

import requests
import time
from typing import Optional, Dict, Any


class ProxyManager:
    """代理管理器"""
    
    def __init__(self, proxy_host: str = "127.0.0.1", proxy_port: int = 1212):
        """
        初始化代理管理器
        
        Args:
            proxy_host: 代理主机地址
            proxy_port: 代理端口
        """
        self.proxy_host = proxy_host
        self.proxy_port = proxy_port
        self.proxies = {
            'http': f'http://{proxy_host}:{proxy_port}',
            'https': f'http://{proxy_host}:{proxy_port}'
        }
        self.proxy_available = self._test_proxy()
    
    def _test_proxy(self) -> bool:
        """测试代理是否可用"""
        try:
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=self.proxies,
                timeout=5
            )
            return response.status_code == 200
        except:
            return False
    
    def get_proxies(self) -> Optional[Dict[str, str]]:
        """获取代理配置"""
        if self.proxy_available:
            return self.proxies
        return None
    
    def request_with_proxy_fallback(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        使用代理发送请求，失败时自动回退到直连
        
        Args:
            method: HTTP方法 (GET, POST等)
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            响应对象
        """
        # 移除可能存在的proxies参数，我们会自己处理
        kwargs.pop('proxies', None)
        
        # 首先尝试使用代理
        if self.proxy_available:
            try:
                print(f"使用代理 {self.proxy_host}:{self.proxy_port} 发送 {method} 请求到 {url}")
                response = requests.request(method, url, proxies=self.proxies, **kwargs)
                
                # 添加适当的延迟以避免请求过快
                time.sleep(1)
                
                return response
                
            except requests.exceptions.ProxyError as e:
                print(f"代理连接失败: {e}")
                print("尝试直连...")
                self.proxy_available = False
            except requests.exceptions.ConnectTimeout as e:
                print(f"代理连接超时: {e}")
                print("尝试直连...")
                self.proxy_available = False
            except Exception as e:
                print(f"代理请求失败: {e}")
                print("尝试直连...")
        
        # 直连请求
        print(f"直连发送 {method} 请求到 {url}")
        response = requests.request(method, url, **kwargs)
        return response
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET请求"""
        return self.request_with_proxy_fallback('GET', url, **kwargs)
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """POST请求"""
        return self.request_with_proxy_fallback('POST', url, **kwargs)
    
    def put(self, url: str, **kwargs) -> requests.Response:
        """PUT请求"""
        return self.request_with_proxy_fallback('PUT', url, **kwargs)
    
    def delete(self, url: str, **kwargs) -> requests.Response:
        """DELETE请求"""
        return self.request_with_proxy_fallback('DELETE', url, **kwargs)
    
    def refresh_proxy_status(self):
        """刷新代理状态"""
        self.proxy_available = self._test_proxy()
        if self.proxy_available:
            print(f"✅ 代理 {self.proxy_host}:{self.proxy_port} 可用")
        else:
            print(f"❌ 代理 {self.proxy_host}:{self.proxy_port} 不可用")
    
    def set_proxy(self, host: str, port: int):
        """设置新的代理"""
        self.proxy_host = host
        self.proxy_port = port
        self.proxies = {
            'http': f'http://{host}:{port}',
            'https': f'http://{host}:{port}'
        }
        self.refresh_proxy_status()


# 全局代理管理器实例
proxy_manager = ProxyManager()


def get_proxy_manager() -> ProxyManager:
    """获取全局代理管理器实例"""
    return proxy_manager


def configure_proxy(host: str = "127.0.0.1", port: int = 1212):
    """配置全局代理"""
    global proxy_manager
    proxy_manager.set_proxy(host, port)


def make_request(method: str, url: str, **kwargs) -> requests.Response:
    """
    使用全局代理管理器发送请求
    
    Args:
        method: HTTP方法
        url: 请求URL
        **kwargs: 其他请求参数
        
    Returns:
        响应对象
    """
    return proxy_manager.request_with_proxy_fallback(method, url, **kwargs)


def get_with_proxy(url: str, **kwargs) -> requests.Response:
    """使用代理发送GET请求"""
    return proxy_manager.get(url, **kwargs)


def post_with_proxy(url: str, **kwargs) -> requests.Response:
    """使用代理发送POST请求"""
    return proxy_manager.post(url, **kwargs)


# 为了向后兼容，提供简单的函数接口
def get_proxies() -> Optional[Dict[str, str]]:
    """获取代理配置（向后兼容）"""
    return proxy_manager.get_proxies()


def is_proxy_available() -> bool:
    """检查代理是否可用"""
    return proxy_manager.proxy_available


if __name__ == "__main__":
    # 测试代理管理器
    print("测试代理管理器...")
    
    # 测试代理状态
    pm = ProxyManager()
    print(f"代理状态: {'可用' if pm.proxy_available else '不可用'}")
    
    # 测试请求
    try:
        response = pm.get('http://httpbin.org/ip')
        print(f"请求成功，状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
    except Exception as e:
        print(f"请求失败: {e}")
